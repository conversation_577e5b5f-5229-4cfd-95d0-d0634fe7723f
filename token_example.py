#!/usr/bin/env python3
"""
Token处理示例
演示如何使用新的token获取和处理功能
"""

import asyncio
import httpx
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API配置
API_BASE_URL = "http://127.0.0.1:8000"

async def test_token_workflow():
    """测试完整的token工作流程"""
    
    print("🔐 Token处理工作流程测试")
    print("=" * 50)
    
    # 测试参数
    sim = "13800138000"
    channel = 1
    device_id = "13896118973"  # 可以与sim不同
    app_code = "SV_CWTPRO"
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        
        # 1. 测试API服务健康状态
        print("1. 检查API服务状态...")
        try:
            response = await client.get(f"{API_BASE_URL}/health")
            if response.status_code == 200:
                print("✅ API服务正常运行")
            else:
                print("❌ API服务异常")
                return
        except Exception as e:
            print(f"❌ 无法连接到API服务: {e}")
            print("请确保运行: python rtvs_python_api.py")
            return
        
        # 2. 测试传统token获取（从RTVS服务器）
        print("\n2. 测试传统token获取...")
        try:
            response = await client.get(f"{API_BASE_URL}/api/token")
            if response.status_code == 200:
                token_data = response.json()
                print(f"✅ 获取到RTVS token: {token_data.get('Token', 'N/A')[:20]}...")
                print(f"   过期时间: {token_data.get('Expires', 'N/A')}")
            else:
                print("⚠️  传统token获取失败，可能RTVS服务器未运行")
        except Exception as e:
            print(f"⚠️  传统token获取异常: {e}")
        
        # 3. 测试音频对讲（使用新的token处理）
        print("\n3. 测试音频对讲（新token处理）...")
        
        # 查找测试音频文件
        test_audio_files = []
        for audio_path in [
            Path("rtvs-python/111.mp3"),
            Path("test.mp3"),
            Path("audio.mp3")
        ]:
            if audio_path.exists():
                test_audio_files.append(audio_path)
        
        if test_audio_files:
            test_file = test_audio_files[0]
            print(f"📁 使用测试文件: {test_file}")
            
            try:
                with open(test_file, "rb") as audio_file:
                    files = {
                        "audio_file": (test_file.name, audio_file, "audio/mpeg")
                    }
                    data = {
                        "sim": sim,
                        "channel": channel,
                        "device_id": device_id  # 新增的device_id参数
                    }
                    
                    print(f"🎤 发起音频对讲请求...")
                    print(f"   SIM: {sim}")
                    print(f"   通道: {channel}")
                    print(f"   设备ID: {device_id}")
                    
                    response = await client.post(
                        f"{API_BASE_URL}/api/audio/talk/start",
                        files=files,
                        data=data
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        print("✅ 音频对讲成功!")
                        print(f"   消息: {result.get('message', 'N/A')}")
                        print(f"   连接ID: {result.get('connection_id', 'N/A')}")
                        
                        # 显示服务器配置信息
                        server_config = result.get('server_config', {})
                        if server_config:
                            print(f"   服务器: {server_config.get('host', 'N/A')}:{server_config.get('port', 'N/A')}")
                            print(f"   WebSocket: {server_config.get('ws_url', 'N/A')}")
                        
                    else:
                        error_detail = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                        print(f"❌ 音频对讲失败: {response.status_code}")
                        print(f"   错误详情: {error_detail}")
                        
            except Exception as e:
                print(f"❌ 音频对讲异常: {e}")
                
        else:
            print("⚠️  未找到测试音频文件，跳过音频对讲测试")
            print("   请将MP3文件放在以下位置之一:")
            print("   - rtvs-python/111.mp3")
            print("   - test.mp3")
            print("   - audio.mp3")

async def test_direct_config_api():
    """直接测试配置获取API"""
    
    print("\n🔧 直接配置API测试")
    print("=" * 30)
    
    # 这里模拟直接调用配置API
    config_url = "http://************/jt-video/video/websocket/address"
    params = {
        "appCode": "SV_CWTPRO",
        "deviceId": "13896118973"
    }
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print(f"📡 请求配置: {config_url}")
            print(f"   参数: {params}")
            
            response = await client.get(config_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            print(f"📋 响应状态: {response.status_code}")
            print(f"📋 响应数据: {data}")
            
            if data.get("result") == 0:
                config = data.get("detail", {})
                print("✅ 配置获取成功:")
                print(f"   地址: {config.get('address', 'N/A')}")
                print(f"   端口: {config.get('port', 'N/A')}")
                print(f"   Token: {config.get('token', 'N/A')[:20]}..." if config.get('token') else "   Token: N/A")
            else:
                print(f"❌ 配置获取失败: {data.get('resultNote', '未知错误')}")
                
    except httpx.ConnectError:
        print("❌ 无法连接到配置服务器")
        print("   请检查网络连接和服务器地址")
    except httpx.TimeoutException:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 配置获取异常: {e}")

def show_token_workflow_info():
    """显示token工作流程说明"""
    
    print("\n📖 Token处理工作流程说明")
    print("=" * 40)
    print("""
🔄 新的Token处理流程:

1. 客户端调用音频对讲API
   POST /api/audio/talk/start
   - sim: 设备SIM卡号
   - channel: 通道号  
   - device_id: 设备ID（可选）
   - audio_file: 音频文件

2. 服务器自动获取配置
   GET http://************/jt-video/video/websocket/address
   - appCode: 应用代码
   - deviceId: 设备ID

3. 解析配置响应
   {
     "result": 0,
     "detail": {
       "address": "服务器地址",
       "port": "WebSocket端口", 
       "token": "访问令牌"
     }
   }

4. 使用获取的token建立WebSocket连接
   - 构建JT1078协议包
   - 包含token认证信息
   - 发送音频数据

✨ 优势:
- 自动token管理
- 支持不同的设备ID
- 统一的配置获取
- 完整的错误处理
""")

async def main():
    """主函数"""
    
    print("🎵 RTVS Token处理测试工具")
    print("=" * 60)
    
    # 显示工作流程说明
    show_token_workflow_info()
    
    # 测试token工作流程
    await test_token_workflow()
    
    # 测试直接配置API
    await test_direct_config_api()
    
    print("\n🎉 测试完成!")
    print("\n💡 使用提示:")
    print("1. 确保RTVS Python API服务正在运行")
    print("2. 检查网络连接到配置服务器")
    print("3. 验证设备ID和SIM卡号正确")
    print("4. 准备测试音频文件")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试运行出错: {e}")
        import traceback
        traceback.print_exc()
