# RTVS Python API 配置文件

# 服务器配置
server:
  host: "127.0.0.1"          # RTVS服务器IP地址
  port: 17000                # RTVS主端口
  web_port: 9081             # Web API端口
  websocket_port: 18002      # WebSocket端口
  protocol: "JT1078"         # 协议类型: JT1078 或 GB28181
  app_code: "SV_CWTPRO"      # 应用代码

# Token配置服务器
token_server:
  url: "http://************/jt-video/video/websocket/address"  # Token配置获取地址
  timeout: 10                # 请求超时时间(秒)

# 默认设备配置
default_device:
  device_id: "13896118973"   # 默认设备ID
  sim: "13800138000"         # 默认SIM卡号
  channel: 1                 # 默认通道号

# API服务配置
api:
  host: "0.0.0.0"            # API服务监听地址
  port: 8000                 # API服务端口
  debug: true                # 调试模式
  reload: true               # 自动重载

# 日志配置
logging:
  level: "INFO"              # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "rtvs_api.log"       # 日志文件路径(可选)

# 音频配置
audio:
  sample_rate: 8000          # 采样率
  channels: 1                # 声道数
  sample_width: 2            # 采样位宽
  chunk_size: 320            # 数据包大小
  interval: 0.02             # 发送间隔(秒)

# 超时配置
timeout:
  http: 30                   # HTTP请求超时(秒)
  websocket: 10              # WebSocket连接超时(秒)
  response: 5                # 响应等待超时(秒)

# 设备配置示例
devices:
  - sim: "13800138000"
    name: "测试设备1"
    channels: [1, 2, 3, 4]
    protocol: "JT1078"
  - sim: "13800138001"
    name: "测试设备2"
    channels: [1, 2]
    protocol: "GB28181"
