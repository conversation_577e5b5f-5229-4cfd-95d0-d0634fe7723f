#!/usr/bin/env python3
"""
直接使用RTVSClient推流MP3文件
不需要启动API服务，直接使用客户端类
"""

import asyncio
import logging
from pathlib import Path
from rtvs_python_api import RTVSClient, RTVSConfig

async def direct_push_mp3(mp3_file_path: str, sim: str, channel: int):
    """
    直接推流MP3文件到设备
    
    Args:
        mp3_file_path: MP3文件路径
        sim: 设备SIM卡号  
        channel: 通道号
    """
    
    # 检查文件
    mp3_file = Path(mp3_file_path)
    if not mp3_file.exists():
        print(f"❌ 文件不存在: {mp3_file_path}")
        return False
    
    print(f"🎵 开始推流: {mp3_file.name}")
    print(f"📱 目标设备: SIM={sim}, 通道={channel}")
    
    # 配置RTVS客户端
    config = RTVSConfig(
        host="127.0.0.1",        # 修改为您的RTVS服务器地址
        port=17000,
        web_port=9081,
        websocket_port=18002,
        protocol="JT1078"
    )
    
    try:
        # 创建客户端并推流
        async with RTVSClient(config) as client:
            print("🔗 连接到RTVS服务器...")
            
            # 模拟UploadFile对象
            class MockUploadFile:
                def __init__(self, file_path):
                    self.file_path = Path(file_path)
                    self.filename = self.file_path.name
                    self.content_type = "audio/mpeg"
                
                async def read(self):
                    return self.file_path.read_bytes()
            
            mock_file = MockUploadFile(mp3_file_path)
            
            # 开始音频对讲
            result = await client.start_audio_talk(sim, channel, mock_file)
            
            if result.get("success"):
                print(f"✅ 推流成功: {result.get('message')}")
                return True
            else:
                print(f"❌ 推流失败: {result}")
                return False
                
    except Exception as e:
        print(f"❌ 推流过程中发生错误: {e}")
        logging.exception("详细错误信息:")
        return False

async def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 查找MP3文件
    current_dir = Path(".")
    mp3_files = list(current_dir.glob("*.mp3"))
    
    # 也检查rtvs-python目录
    rtvs_python_dir = Path("rtvs-python")
    if rtvs_python_dir.exists():
        mp3_files.extend(list(rtvs_python_dir.glob("*.mp3")))
    
    if not mp3_files:
        print("❌ 未找到MP3文件")
        print("请将MP3文件放在当前目录或rtvs-python目录中")
        return
    
    print("📁 找到以下MP3文件:")
    for i, file in enumerate(mp3_files, 1):
        print(f"  {i}. {file.name}")
    
    # 选择文件
    try:
        choice = int(input(f"请选择文件 (1-{len(mp3_files)}): ")) - 1
        selected_file = mp3_files[choice]
    except (ValueError, IndexError):
        print("❌ 无效选择")
        return
    
    # 设备信息
    sim = input("请输入设备SIM卡号 (默认: 13800138000): ").strip() or "13800138000"
    channel = int(input("请输入通道号 (默认: 1): ").strip() or "1")
    
    # 开始推流
    success = await direct_push_mp3(str(selected_file), sim, channel)
    
    if success:
        print("🎉 推流完成!")
    else:
        print("💥 推流失败")

if __name__ == "__main__":
    asyncio.run(main())
