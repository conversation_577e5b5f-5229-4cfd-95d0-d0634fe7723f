﻿@model JT1078Server.ConfigModel
@{
    Layout = null;
}
<link rel="stylesheet" type="text/css" href="/js/pagination/common/highlight.min.css" media="screen">
<link rel="stylesheet" type="text/css" href="/js/pagination/common/common.css" media="screen">
<link rel="stylesheet" type="text/css" href="/js/pagination/pagination.css" media="screen">
<script src="/js/pagination/common/highlight.min.js"></script>
<script src="/js/pagination/jquery.pagination.js"></script>
<script src="/js/TreeGrid.js" type="text/javascript"></script>

<style>
    tr.on td {
        background-color: #BFEFFF;
    }
</style>

<script type="text/javascript">
    var json_page = { currentPage: 1, totalPage: 1, pageSize: 20, originalData: [], result: [] };
    var reg_time = /^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/;
    var reg_ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    var sql_search = "";
    //时间支持
    Date.prototype.Format = function (fmt) {
        var o = {
            "M+": this.getMonth() + 1, //月
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    //搜索
    function search() {
        //输入验证
        {
            //ip
            if ($("#ip").val() && $("#ip").val() != " ") {//不为空则验证

                if (!reg_ip.test($("#ip").val())) {
                    alert("输入的IP不合法！");
                    return false;
                }
            }

            //sim验证
            if ($("#sim").val() && $("#sim").val() != " ") {//不为空则验证

                if (!/(^\d{12}$)|(^\d{20}$)|(^.{20}_.{20}$)/g.test($("#sim").val())) {
                    alert("请输入正确sim，不足位请前面加0！");
                    return false;
                }
            }

            //时间输入验证
            var end_time = $("#end").val();
            var matchArray = end_time.match(reg_time);
            if (matchArray == null) {
                alert("输入时间格式不正确，请按yyyy-MM-dd HH:mm:ss的格式输入！");
                return;
            }
            var start_time = $("#start").val();
            matchArray = start_time.match(reg_time);
            if (matchArray == null) {
                alert("输入时间格式不正确，请按yyyy-MM-dd HH:mm:ss的格式输入！");
                return;
            }

            var startTime = new Date(Date.parse(start_time.replace(/-/g, "/"))).getTime();
            var endTime = new Date(Date.parse(end_time.replace(/-/g, "/"))).getTime();
            if (startTime > endTime) {
                alert("开始时间大于结束时间！s:" + startTime + " e" + endTime);
                return;
            }
            if (Math.abs((startTime - endTime)) > (1000 * 60 * 60 * 24)) {
                alert("时间间隔不能大于一天！");
                return;
            }

        }
        //查询url
        {
            sql_search = "fd_type=";
            if ($("#type").selectedIndex != 0) {
                sql_search += $("#type").val();
            }
            //else {
            //    sql += "''";
            //}

            sql_search += "&ip=";
            if ($("#ip").val() && $("#ip").val() != " ") {
                sql_search += $("#ip").val();
            }
            //else {
            //    sql += "''";
            //}

            sql_search += "&sim=";
            if ($("#sim").val() && $("#sim").val() != " ") {
                sql_search += $("#sim").val();
            }
            //else {
            //    sql += "''";
            //}

            sql_search += "&start_time=" + start_time;
            sql_search += "&end_time=" + end_time;
            var sql = sql_search + "&page_count=" + json_page.pageSize + "&page_num=1";

            var url = "/Log/IndexLogQuery?" + sql;

            console.log(url);
        }
        //结果处理
        {

            $.ajax({
                url: url,
                dataType: "json",
                success: function (data) {
                    if (typeof (data) != "object")
                        return;

                    var result_count = data.all_num;
                    var data = data.data;

                    config.data = data;
                    treeGrid.show();


                    initPagination(Math.ceil(result_count / json_page.pageSize), 1);


                    return;
                },
                error: function (xhr) {
                    config.data = [];
                    treeGrid.show();
                }
            });
        }
    }
    //分页设置
    function initPagination(totalPage, currentPage) {
        json_page.totalPage = totalPage;
        json_page.currentPage = currentPage;

        //初始化分页控件
        $(".M-box3").pagination({
            pageCount: json_page.totalPage,
            totalData: json_page.totalData,
            current: json_page.currentPage,
            showData: json_page.pageSize,
            jump: true,
            coping: true,
            homePage: '首页',
            endPage: '末页',
            prevContent: '上页',
            nextContent: '下页',
            callback: function (api) {
                paging(api.getCurrent(), json_page.pageSize, json_page.originalData);
            }
        });
    }
    //分页事件
    function paging(page, pageSize, jsonList) {
        //构造请求字符串
        {
            var sql = sql_search + "&page_count=" + json_page.pageSize + "&page_num=" + page;

            var url = "/Log/IndexLogQuery?" + sql;
        }
        //结果处理
        {
            $.ajax({
                url: url,
                dataType: "json",
                success: function (data) {

                    if (typeof (data) != "object")
                        return;

                    var result_count = data.all_num;
                    var data = data.data;

                    config.data = data;
                    treeGrid.show();
                    return;
                },
                error: function (xhr) {
                    config.data = [];
                    treeGrid.show();
                }
            });
        }
    }
    //初始化设置时间
    function setDateTime() {
        var dt = new Date();//获取当前时间
        var time_start = dt.Format("yyyy-MM-dd 00:00:00");

        dt.setDate(dt.getDate() + 1);//设置天数 +1 天
        var time_end = dt.Format("yyyy-MM-dd 00:00:00");

        $("#start").val(time_start);
        $("#end").val(time_end);
    }
    //初始化调用
    $(document).ready(function () {
        setDateTime();
    });
    //显示设置
    var config = {
        id: "table1",
        width: "",
        renderTo: "div1",
        headerAlign: "left",
        headerHeight: "30",
        dataAlign: "left",
        indentation: "20",
        folderOpenIcon: "/images/folderOpen.gif",
        folderCloseIcon: "/images/folderClose.gif",
        defaultLeafIcon: "/images/defaultLeaf.gif",
        hoverRowBackground: "false",
        folderColumnIndex: "0",
        itemClick: "itemClickEvent",
        columns: [
            //{ headerText: "", headerAlign: "center", dataAlign: "center", width: "20", handler: "customCheckBox" },
            { headerText: "设备类型", dataField: "fd_type", headerAlign: "center", dataAlign: "left", width: "100" },
            { headerText: "消息类型", dataField: "bs_type", headerAlign: "center", dataAlign: "center", width: "100" },
            { headerText: "时间", dataField: "time", headerAlign: "center", dataAlign: "center", width: "100" },
            { headerText: "sim-channel", dataField: "sim_channel", headerAlign: "center", dataAlign: "center", width: "100" },
            { headerText: "result", dataField: "result", headerAlign: "center", dataAlign: "center", width: "100" },
            { headerText: "ip-port", dataField: "ip_port", headerAlign: "center", dataAlign: "center", width: "100" }
        ],
        data: "",
        //[
        //    { fd_type: "ocx",  bs_type: "0x9101", sim: "0139888888", channel: 2, time: "2019-1-23 06:08:21", ip: "127.0.0.1", port: "54331", status: "正常", describle: "实时视频指令", guid_key: "123"},
        //    { fd_type: "rtmp",  bs_type: "0x9101", sim: "0139888888", channel: "1", time: "2019-1-23 06:08:21", ip: "127.0.0.1", port: "54331", status: "正常", describle: "实时视频指令", guid_key: "123"},
        //    { fd_type: "wsfmp4", bs_type: "0x9101", sim: "0139888888", channel: "1", time: "2019-1-23 06:08:21", ip: "127.0.0.1", port: "54331", status: "正常", describle: "实时视频指令", guid_key: "123"},
        //    { fd_type: "wsjt1078", bs_type: "0x9101", sim: "0139888888", channel: "1", time: "2019-1-23 06:08:21", ip: "127.0.0.1", port: "54331", status: "正常", describle: "实时视频指令", guid_key: "123"}
        //],
        columnsChild: [
            //{ headerText: "", headerAlign: "center", dataAlign: "center", width: "20", handler: "customCheckBox" },
            { headerText: "设备类型", dataField: "fd_type", headerAlign: "center", dataAlign: "left", width: "100" },
            { headerText: "消息类型", dataField: "bs_type", headerAlign: "center", dataAlign: "center", width: "100" },
            { headerText: "时间", dataField: "time", headerAlign: "center", dataAlign: "center", width: "100" },
            { headerText: "状态", dataField: "explain", headerAlign: "center", dataAlign: "center", width: "100" },
            { headerText: "描述", dataField: "describle", headerAlign: "center", dataAlign: "left", width: "100" }
        ]
    };

    //function setConfigData() {
    //    treeGrid.show();
    //}

    //function updateConfigDataChildren() {
    //    treeGrid.show();
    //}

    var treeGrid = new TreeGrid(config);
    treeGrid.show();
    //treeGrid点击事件
    function itemClickEvent(pid, data) {

        var url = "/Log/IndexLogQueryFlow?guid_key=" + data;

        console.log(pid + "  " + data + " url:" + url);

        $.getJSON(url, function (data) {

            if (typeof (data) != "object")
                return;

            console.log(data);
            treeGrid.insertChild(pid, data)

        });

        //jQuery("#currentRow").val(id + ", " + index + ", " + TreeGrid.json2str(data));
    }
    function setDT(add) {

        var startTime = new Date(Date.parse($("#start").val().replace(/-/g, "/")));
        startTime.setDate(startTime.getDate() + add);//设置天数 +1 天

        var endTime = new Date(Date.parse($("#end").val().replace(/-/g, "/")));
        endTime.setDate(endTime.getDate() + add);//设置天数 +1 天

        $("#start").val(startTime.Format("yyyy-MM-dd hh:mm:ss"));
        $("#end").val(endTime.Format("yyyy-MM-dd hh:mm:ss"));
    }
</script>

<!--这个地方搜索数据-->
<table style="font-weight:bold;">
    <tr>
        <td><nobr>设备类型:</nobr></td>
        <td>
            <select id="type" style="width:250px;">
                <option value="" selected="selected">ALL</option>
                <option value="OCX">OCX/WASM</option>
                <option value="RTMP">RTMP</option>
                <option value="WSFMP4">WSFMP4</option>
                <option value="WS1078">WS1078</option>
                <option value="WEBRTC">WEBRTC</option>
                <option value="Gov">Gov</option>
                <option value="HttpFMp4">HttpFMp4</option>
                <option value="HttpFlv">HttpFlv</option>
                <option value="Rtsp">Rtsp</option>
            </select>
        </td>
        <td><nobr>IP地址:</nobr></td>
        <td><input id="ip" type="text" style="width:250px;" /></td>
        <td><nobr>SIM卡号:</nobr></td>
        <td><input id="sim" type="text" style="width:250px;" /></td>
    </tr>
    <tr>
        <td><nobr>开始时间:</nobr></td>
        <td><input id="start" type="text" style="width:250px;"></td>
        <td><nobr>结束时间:</nobr></td>
        <td><input id="end" type="text" style="width:250px;"></td>
        <td colspan="2">
            <input class="btn btn-primary" type="button" value="上一天" onclick="setDT(-1)">
            <input class="btn btn-primary" type="button" value="下一天" onclick="setDT(1)">
        </td>
    </tr>
    <tr><td colspan="6" align="center"><input class='btn btn-primary' type="button" value="查询" onclick="search()"><span id="msg" class="span"></span></td></tr>
</table>
<!--这个地方展示数据-->
<div id="div1"></div>

<style>
    .input {
        width: 500px;
        color: red;
    }

    .span {
        color: gray;
        padding-left: 20px;
    }

    .table {
        border-collapse: collapse;
        border-spacing: 0;
        table-layout: fixed;
        word-wrap: break-word;
        word-break: break-all;
    }
</style>
<!--这个地方展示分页-->
<div class="m-style M-box3"></div>
