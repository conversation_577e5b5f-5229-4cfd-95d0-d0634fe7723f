﻿@model JT1078Server.ConfigModel
@{
    Layout = null;
}
<link rel="stylesheet" type="text/css" href="/js/pagination/common/highlight.min.css" media="screen">
<link rel="stylesheet" type="text/css" href="/js/pagination/common/common.css" media="screen">
<link rel="stylesheet" type="text/css" href="/js/pagination/pagination.css" media="screen">
<script src="/js/pagination/common/highlight.min.js"></script>
<script src="/js/pagination/jquery.pagination.js"></script>
<script type="text/javascript">
    var json_page = { currentPage: 1, totalPage: 1, pageSize: 30, originalData: [], result: [] };

    function search() {
        //输入验证
        {
            //sim验证
            if ($("#sim").val() && $("#sim").val() != " ") {//不为空则验证
                if (isNaN($("#sim").val())) {
                    alert("请输入数字！");
                    return false;
                }

                if ($("#sim").val().length != 12) {
                    alert("您的输入必须为12位数字! 手机号前面加0");
                    return false;
                }
            }

            //时间输入验证
            var end_time = $("#end").val();
            var matchArray = end_time.match(reg_time);
            if (matchArray == null) {
                alert("输入时间格式不正确，请按yyyy-MM-dd HH:mm:ss的格式输入！");
                return;
            }
            var start_time = $("#start").val();
            matchArray = end_time.match(reg_time);
            if (matchArray == null) {
                alert("输入时间格式不正确，请按yyyy-MM-dd HH:mm:ss的格式输入！");
                return;
            }

            var startTime = new Date(Date.parse(start_time.replace(/-/g, "/"))).getTime();
            var endTime = new Date(Date.parse(end_time.replace(/-/g, "/"))).getTime();
            if (startTime > endTime) {
                alert("开始时间大于结束时间！s:" + startTime + " e" + endTime);
                return;
            }
            if (Math.abs((startTime - endTime)) > (1000 * 60 * 60 * 24)) {
                alert("时间间隔不能大于一天！");
                return;
            }

        }
        //查询url
        {
            sql_search = "log_type=";
            if ($("#type").selectedIndex != 0) {
                sql_search += $("#type").val();
            }
            sql_search += "&start_time=" + start_time;
            sql_search += "&end_time=" + end_time;
            var sql = sql_search + "&page_count=" + json_page.pageSize + "&page_num=1";

            var url = "/Log/InfoLogQuery?" + sql;
        }
        //结果处理
        {
            console.log(url);
            $.getJSON(url, function (data) {
                parseJson(data, true);
            });
        }
    }

    function parseJson(data, initPage) {
        console.log(data);

        if (typeof (data) != "object" || typeof (data.results) != "object" || data.results.length != 2 ||
            typeof (data.results[1].series) != "object" || typeof (data.results[0].series) != "object") {
            $("#msg").css("color", "red");
            $("#msg").html("没有查询到结果!");
            $("#data").html("");
            return;
        }
        $("#msg").css("color", "red");
        $("#msg").html("查询成功!");

        if (typeof (data.results[1].series[0].values) != "object") {
            return;
        }

        if (typeof (data.results[0].series[0].values) != "object") {
            return;
        }

        //获取个数
        var result_count = data.results[1].series[0].values[0][1];

        //显示分页
        if (initPage)
            initPagination(Math.ceil(result_count / json_page.pageSize), 1);

        //显示数据
        var values = data.results[0].series[0].values;

        var result = "";
        for (var i = 0; i < values.length; i++) {
            result += "<tr>";
            var item = values[i];
            var value_row = values[i];
            for (var j = 0; j < value_row.length; j++) {
                if (value_row[j] == null) {
                    result += "<td><nobr>null</nobr></td>";
                } else if (value_row[j].indexOf("-") > 0 && value_row[j].indexOf(".") > value_row[j].indexOf("-")) {
                    //处理时间
                    result += "<td><nobr>" + value_row[j].replace("T", " ").replace("Z", "").split(".")[0] + "</nobr></td>";
                } else {
                    result += "<td><nobr>" + value_row[j].replace(/COMMA/g, ",").replace(/BLANK/g, " ") + "</nobr></td>";
                }
            }
            result += "</tr>"
        }
        $("#data").html(result);
    }

    function initPagination(totalPage, currentPage) {
        console.log("init_page");
        json_page.totalPage = totalPage;
        json_page.currentPage = currentPage;

        //初始化分页控件
        $(".M-box3").pagination({
            pageCount: json_page.totalPage,
            totalData: json_page.totalData,
            current: json_page.currentPage,
            showData: json_page.pageSize,
            jump: true,
            coping: true,
            homePage: '首页',
            endPage: '末页',
            prevContent: '上页',
            nextContent: '下页',
            callback: function (api) {
                paging(api.getCurrent(), json_page.pageSize, json_page.originalData);
            }
        });
    }
    function paging(page, pageSize, jsonList) {
        var sql = sql_search + "&page_count=" + json_page.pageSize + "&page_num=" + page;

        var url = "/Log/InfoLogQuery?" + sql;
        console.log(url);

        $.getJSON(url, function (data) {
            parseJson(data, false);
        });
    }

    function showData(row_values) {
        var result = "";
        for (var i = 0, j = row_values.length; i < j; i++) {
            result += "<tr>";
            var item = row_values[i];
            var values = (item + "").split(",");
            for (var m = 0, n = values.length; m < n; m++) {
                if (values[m].indexOf("-") > 0 && values[m].indexOf(".") > values[m].indexOf("-")) {
                    result += "<td>" + values[m].replace("T", " ").replace("Z", "").split(".")[0] + "</td>";
                } else {
                    result += "<td>" + values[m].replace(/COMMA/g, ",").replace(/BLANK/g, " ") + "</td>";
                }
            }
            result += "</tr>"
        }
        $("#data").html(result);
    }

    function initDate() {
        var date_start = new Date();//获取当前时间
        var time_start = date_start.Format("yyyy-MM-dd 00:00:00");

        var date_end = new Date();//获取当前时间
        date_end.setDate(date_end.getDate() + 1);//设置天数 +1 天
        var time_end = date_end.Format("yyyy-MM-dd 00:00:00");

        $("#start").val(time_start);
        $("#end").val(time_end);
    }

    function getSql() {
        var sql = "select * from error_logs where 1=1 ";
        if ($("#method").val() && $("#method").val() != " ") {
            sql += " and method='" + $("#method").val() + "'";
        }
        if ($("#start").val() && $("#start").val() != " ") {
            sql += " and time >= '" + $("#start").val().replace(" ", "T") + "Z'";
        }
        if ($("#end").val() && $("#end").val() != " ") {
            sql += " and time < '" + $("#end").val().replace(" ", "T") + "Z'";
        }
        sql += " tz('Asia/Shanghai')";
        return sql;
    }

    $(function () {
        initDate();
        search();
    });
</script>


<table style="font-weight:bold;">
    <tr style="display:none"><td>数据库基础地址:</td><td><input id="baseUrl" type="text" value="@Model.InfluxdbBaseUrl" readonly="readonly" class="input"><span class="span">不可修改</span></td></tr>
    <tr style="display:none"><td>数据库名称:</td><td><input id="database" type="text" value="@Model.InfluxdbName" readonly="readonly" class="input"><span class="span">不可修改</span></td></tr>
    <tr style="display:none"><td>用户名:</td><td><input id="username" type="text" value="@Model.InfluxdbUsername" readonly="readonly" class="input"><span class="span">不可修改</span></td></tr>
    <tr style="display:none"><td>密码:</td><td><input id="password" type="text" value="@Model.InfluxdbPassword" readonly="readonly" class="input"><span class="span">不可修改</span></td></tr>
    <tr style="display:none"><td>SQL时区设置:</td><td>tz('Asia/Shanghai') 上海</td></tr>
    <tr style="display:none"><td>SQL条件设置:</td><td>天(d) 时(h) 分(m)</td></tr>
    <tr>
        <td><nobr>日志类型:</nobr></td>
        <td>
            <select id="type" style="width:250px;">
                <option value="" selected="selected">ALL</option>
                <option value="warn">warn</option>
                <option value="error">error</option>
            </select>
        </td>
        <td><nobr>开始时间:</nobr></td>
        <td><input id="start" type="text" style="width:300px;"></td>
        <td><nobr>结束时间:</nobr></td>
        <td><input id="end" type="text" style="width:300px;"></td>
    </tr>
    <tr><td colspan="6" align="center"><input class='btn btn-primary' type="button" value="查询" onclick="search()"><span id="msg" class="span"></span></td></tr>
</table>
<!--这个地方展示数据-->
<table id="data-table" class="table" style="width:100%;">
    <tr>
        <th><nobr>时间</nobr></th>
        <th><nobr>类型</nobr></th>
        <th><nobr>日志</nobr></th>
    </tr>
    <tbody id="data"></tbody>
</table>
<style>
    .input {
        width: 500px;
        color: red;
    }

    .span {
        color: gray;
        padding-left: 20px;
    }

    .table {
        border-collapse: collapse;
        border-spacing: 0;
        word-wrap: break-word;
        word-break: break-all;
    }
</style>
<!--这个地方展示分页-->
<div class="m-style M-box3"></div>

