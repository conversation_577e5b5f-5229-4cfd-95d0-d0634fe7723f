﻿@{
    Layout = null;
}
<!--这个地方展示数据-->
<table id="data-table" class="table"  style="width:100%;">
    <tr>
        <th>设备IP</th>
        <th>设备SIM</th>
        <th>设备逻辑通道</th>
        <th>设备上传速度</th>
        <th>转发客户端列表</th>
    </tr>

</table>
<!--这个地方展示分页-->
<div class="m-style M-box3"></div>

<link rel="stylesheet" type="text/css" href="/js/pagination/common/highlight.min.css" media="screen">
<link rel="stylesheet" type="text/css" href="/js/pagination/common/common.css" media="screen">
<link rel="stylesheet" type="text/css" href="/js/pagination/pagination.css" media="screen">
<script src="/js/pagination/common/highlight.min.js"></script>
<script src="/js/pagination/jquery.pagination.js"></script>

<script type="text/javascript">
    $(function () {
        query(1,10);
    });

    var url = "/Server/DevicesToPage";

    var queryStr = {};

    function getJson(url, data, func) {
        $.getJSON(url, data, func);
    }

    //进行查询
    function query(page, pageSize) {
        queryStr.page = page;
        queryStr.pageSize = pageSize;
        getJson(url+"?random=" + Math.random(), queryStr, loadlist);
    }

    function jsonToStr()
    {
        for (var i = 0; i < json.data.length; i++) {
            var fdlist = "[";
            var list = json.data[i].listClientInfo;
            var count = 0;
            for (var j = 0; j < list.length; j++) {
                var index = 0;
                // JSON转字符串
                var jStr = "{ ";
                for (var item in list[j]) {
                    if (index == 0) {
                        jStr += "'" + item + "':'" + list[j][item] + "'";
                    }
                    else {
                        jStr += ",'" + item + "':'" + list[j][item] + "'";
                    }
                    index++;
                }
                jStr += " }";
                if (count == 0) {
                    fdlist += jStr;
                } else {
                    fdlist += "," + jStr;
                }
                count++;
            }
            fdlist += "]";
        }
    }

    function showResult(json)
    {
        $(".loaded-data").remove();
       
        for (var i = 0; i < json.data.length; i++) {
            var list = json.data[i].listClientInfo;
            // 拼装table表格
            var listHtml = "<table>";
            // 表头名称
            listHtml += "<tr><th>远程连接信息</th><th>客户端类型</th></tr>";
            for (var j = 0; j < list.length; j++) {
                var tr = "<tr>"; 
                for (var item in list[j]) {
                    tr += "<th>" + list[j][item] + "</th>";
                }
                tr+="</tr>"
                listHtml += tr;
            }
            listHtml+= "</table>";

            $("#data-table").append(
                "<tr class=\"loaded-data\"><th>" +
                json.data[i].remoteInfo + "</th><th>" +
                json.data[i].sim + "</th><th>" +
                json.data[i].channel + "</th><th>" +
                json.data[i].receiveByteRate + "</th><th>"
                + listHtml + "</th></tr > ")
        }
    }
    //返回结果处理:注意JSON字段的大小写问题
    function loadlist(json) {     
        showResult(json);
        //初始化分页控件
        $(".M-box3").pagination({
            pageCount: json.totalPage,
            totalData: json.totalData,
            current: json.currentPage,
            showData: json.pageSize,
            jump: true,
            coping: true,
            homePage: '首页',
            endPage: '末页',
            prevContent: '上页',
            nextContent: '下页',
            callback: function (api)
            {
                // 当前第几页
                queryStr.page = api.getCurrent();

                $.getJSON(url + "?random=" + Math.random(), queryStr, function (json) {
                    showResult(json);
                });
            }
        });
    }

    function reload()
    {
        query(1, 10);
    }

</script>