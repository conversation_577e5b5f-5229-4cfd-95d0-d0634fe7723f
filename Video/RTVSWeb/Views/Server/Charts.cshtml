﻿@model JT1078Server.Kernel.MDServerInfo
@{
    Layout = null;
}
<!-- 为ECharts准备一个具备大小（宽高）的Dom -->
<div id="main" style="width: 100%;height:400px;"></div>
<label>视频流中的客户端数:</label>
<label><font color="red">@(Model.ClientStreamingCount)</font></label>
<div>
    <label>服务器带宽:</label>
    @RTVSWeb.Utils.NetworkStat.Singleton.GetStat()
</div>
<script src="/lib/jquery/dist/jquery.js"></script>
<script src="/lib/bootstrap/dist/js/bootstrap.js"></script>
<script src="/js/echarts.min.js"></script>
<script type="text/javascript">

    $(function () {
        loadCharts(@(Model.DeviceChejiCount), @(Model.OcxChejiCount),@(Model.WebSocketFmp4ChejiCount),@(Model.WebSocketJT1078ChejiCount),@(Model.RtmpChejiCount), @(Model.GovChejiCount), @(Model.WebrtcChejiCount), @(Model.HttpFmp4ChejiCount), @(Model.HttpFlvChejiCount), @(Model.RtspChejiChout));
    });

    function loadCharts(DeviceChejiCount, OcxChejiCount, WebSocketFmp4ChejiCount, WebSocketJT1078ChejiCount, RtmpChejiCount, GovChejiCount, WebrtcChejiCount, HttpFMp4ChejiCount, HttpFlvChejiCount, RtspChejiChout) {
         // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('main'));

        // 指定图表的配置项和数据
        var option = {
            title: {
                text: '实时在线数量统计'
            },
            tooltip: {},
            color: ['#3398DB'],
            xAxis: {
                data: ["Device", "OCX", "WSFMP4", "WSJT1078", "RTMP", "GOV", "Webrtc", "HTTPFMP4", "HTTPFLV", "RTSP"]
            },
            yAxis: {},
            series: [{
                name: '实时在线数量统计',
                type: 'bar',
                data: [DeviceChejiCount, OcxChejiCount, WebSocketFmp4ChejiCount, WebSocketJT1078ChejiCount, RtmpChejiCount, GovChejiCount, WebrtcChejiCount, HttpFMp4ChejiCount, HttpFlvChejiCount, RtspChejiChout]
            }]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
    }
</script>
