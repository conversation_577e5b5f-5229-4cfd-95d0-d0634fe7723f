﻿@{
    Layout = null;
}
@model UserModel
<!DOCTYPE html>
<html>
<head>
    <title>系统登录</title>
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.css">
    <link rel="stylesheet" href="~/lib/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="~/css/AdminLTE.css">
    <link rel="stylesheet" href="~/lib/icheck/skins/square/blue.css">
</head>
<body class="hold-transition login-page">
    <div class="login-box">
        <div class="login-logo">
            <b>RTVS Web服务管理</b>
        </div>
        <!-- /.login-logo -->
        <div class="login-box-body">
            <p class="login-box-msg">系统登录校验</p>
            <!-- <div asp-validation-summary="All" class="text-danger"></div> -->
            <form asp-controller="Login" asp-action="SignIn" method="post" onsubmit="return validate()">
                <span class="text-danger">@ViewBag.ErrorInfo</span>
                <div class="form-group has-feedback">
                    <input asp-for="Username" type="text" class="form-control" placeholder="用户名">
                    <span class="glyphicon glyphicon-user form-control-feedback"></span>
                    <span asp-validation-for="Username" class="text-danger"></span>
                </div>
                <div class="form-group has-feedback">
                    <input asp-for="Password" type="password" class="form-control" placeholder="密码" id="password_md5">
                    <span class="glyphicon glyphicon-lock form-control-feedback"></span>
                </div>
                <div class="row">
                    <div class="col-xs-8">
                        <div class="checkbox icheck">
                            <label>
                                <input asp-for="RememberMe" type="checkbox" checked> 记住我
                            </label>
                        </div>
                    </div>
                    <!-- /.col -->
                    <div class="col-xs-4">
                        <button type="submit" class="btn btn-primary btn-block btn-flat">登录</button>
                    </div>
                    <!-- /.col -->
                </div>
            </form>
        </div>
        <!-- /.login-box-body -->
    </div>
    <!-- /.login-box -->
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.js"></script>
    <script src="~/lib/icheck/icheck.js"></script>
    <script src="~/lib/md5.min.js"></script>
    <script>
        $(function () {
            $('input').iCheck({
                checkboxClass: 'icheckbox_square-blue',
                radioClass: 'iradio_square-blue',
                increaseArea: '20%' // optional
            });
        });

        function validate() {
            let dom_password = document.getElementById('password_md5');
            //alert(dom_password.value)
            dom_password.value = md5(dom_password.value);
            //alert(dom_password.value)
        }
    </script>
</body>
</html>