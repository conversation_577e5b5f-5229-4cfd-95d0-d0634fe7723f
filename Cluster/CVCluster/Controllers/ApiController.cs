﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using CVCluster.Models;
using SQ.Base.Cluster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using SQ.Base;
using System.Runtime.InteropServices;
using System.Text;
using System.Collections.Concurrent;
using SQ.Base.Mem;

namespace CVCluster.Controllers
{
    public class ApiController : Controller
    {
        [MyAuthorize]
        public IActionResult Index()
        {
            return View(ClusterManagement.Instance.GetConfs());
        }


        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }


        [MyAuthorize]
        // GET: Default/Create
        public ActionResult Create()
        {
            return View();
        }

        [MyAuthorize]
        // POST: Default/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(ServerConf conf)
        {
            try
            {
                // TODO: Add insert logic here
                ClusterManagement.Instance.SetConf(conf);

                return RedirectToAction(nameof(Index));
            }
            catch
            {
                return View();
            }
        }

        [MyAuthorize]
        // GET: Default/Edit/5
        public ActionResult Edit(int id)
        {
            return View(ClusterManagement.Instance.GetConf(id));
        }

        [MyAuthorize]
        // POST: Default/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(int id, ServerConf conf)
        {
            try
            {
                conf.Type = id;
                // TODO: Add update logic here
                ClusterManagement.Instance.SetConf(conf);

                return RedirectToAction(nameof(Index));
            }
            catch
            {
                return View();
            }
        }

        [MyAuthorize]
        // GET: Default/Delete/5
        public ActionResult Delete(int id)
        {
            return View(ClusterManagement.Instance.GetConf(id));
        }

        [MyAuthorize]
        // POST: Default/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Delete(int id, IFormCollection collection)
        {
            try
            {
                // TODO: Add delete logic here

                return RedirectToAction(nameof(Index));
            }
            catch
            {
                return View();
            }
        }

        [MyAuthorize]
        public ActionResult Details(int id)
        {
            return View(ClusterManagement.Instance.GetConf(id));
        }


        /// <summary>
        /// 心跳
        /// </summary>
        /// <param name="data"></param>
        /// <returns>1代表成功 2代表已被升级，需在适当时停止 3代表服务已停用，需立即停止</returns>
        [HttpPost]
        public int Heartbeat(string data, string mkey)
        {
            if (mkey == null)
            {
                return 3;
            }
            ReportCV(data, mkey);
            return ClusterManagement.Instance.UpdateServers(mkey, SQ.Base.JsonHelper.ParseJSON<List<ServerInfo>>(data));
        }

        /// <summary>
        /// 加密信息，鉴权
        /// </summary>
        /// <param name="auth"></param>
        /// <param name="sign_text"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<string> Authentication(string auth, string sign, string mkey)
        {
            return await ReportCVAdditionInfo(auth, sign, mkey);
        }
        [HttpPost]
        public async Task<string> AuthenticationRecord(string auth, string sign, string mkey)
        {
            return await ReportRecord(auth, sign, mkey);
        }

        /// <summary>
        /// 升级
        /// </summary>
        /// <param name="Type"></param>
        /// <param name="NewVer"></param>
        /// <returns></returns>
        public int Upgrade(int Type, string NewVer)
        {
            if (ClusterManagement.Instance.Upgrade(Type, NewVer))
            {
                return 1;
            }
            return 0;
        }
        /// <summary>
        /// 设置配置
        /// </summary>
        /// <param name="Conf"></param>
        /// <returns></returns>
        [HttpPost]
        public int SetConf(string Conf)
        {
            ClusterManagement.Instance.SetConf(SQ.Base.JsonHelper.ParseJSON<ServerConf>(Conf));
            return 1;
        }

        /// <summary>
        /// 获取最优服务器信息
        /// </summary>
        /// <param name="Type">服务类型</param>
        /// <param name="Tag">特征码(在未升级情况下，同一特征码一定会路由到同一服务，为NULL或空字符串时特征码不起作用)</param>
        /// <returns></returns>
        public string GetBest(int Type, string Tag)
        {
            var item = ClusterManagement.Instance.GetBestServer(Type, Tag);
            if (item != null)
            {
                return item.ConnInfo;
            }
            return null;
        }
        static System.Collections.Concurrent.ConcurrentDictionary<string, DateTime> ditTime = new System.Collections.Concurrent.ConcurrentDictionary<string, DateTime>();
        private async void ReportCV(string data, string mkey)
        {
            //#if DEBUG
            //            try
            //            {

            //                await HttpHelperByHttpClient.HttpRequestHtml2("http://localhost:5100/rtvs?key=" + System.Web.HttpUtility.UrlEncode(mkey.ToCJson()), true, System.Threading.CancellationToken.None, data: "data=" + System.Web.HttpUtility.UrlEncode(data.ToCJson()), encoding: System.Text.Encoding.UTF8);
            //            }
            //            catch
            //            {
            //            }
            //            return;
            //#endif

            if (CheckDt(mkey))
            {
                ditTime[mkey] = DateTime.Now;

                try
                {
                    //Log.WriteLog4("http://rtvs.cvtsp.com:15399/rtvs?key=" + System.Web.HttpUtility.UrlEncode(mkey.ToCJson()));
                    //Log.WriteLog4("data=" + System.Web.HttpUtility.UrlEncode(data.ToCJson()));
                    var ret = await HttpHelperByHttpClient.HttpRequestHtml2("http://rtvs.cvtsp.com:15399/rtvs?key=" + System.Web.HttpUtility.UrlEncode(mkey.ToCJson()), true, System.Threading.CancellationToken.None, data: "data=" + System.Web.HttpUtility.UrlEncode(data.ToCJson()), encoding: System.Text.Encoding.UTF8);
                    //var ret = await HttpHelperByHttpClient.HttpRequestHtml2("http://************:15399/rtvs?key=" + System.Web.HttpUtility.UrlEncode(mkey.ToCJson()), true, System.Threading.CancellationToken.None, data: "data=" + System.Web.HttpUtility.UrlEncode(data.ToCJson()), encoding: System.Text.Encoding.UTF8);

                    if (ret != null)
                    {
                        return;
                    }
                }
                catch
                {
                }
                ditTime[mkey] = DateTime.Now.AddHours(-11.5);

            }


        }
        private bool CheckDt(string key)
        {
            if (ditTime.TryGetValue(key, out var dt))
            {
                return (DateTime.Now - dt).TotalHours >= 12;
            }
            return true;
        }

        class ServerResponseInfo
        {
            public int errcode;
            public DateTime time;
            public string sign;
            public string server_chipertext;
        }
        static ConcurrentDictionary<string, ServerResponseInfo> dicAdditionCache = new ConcurrentDictionary<string, ServerResponseInfo>();
        static ConcurrentDictionary<string, ServerResponseInfo> dicRecord = new ConcurrentDictionary<string, ServerResponseInfo>();
        private async Task<string> ReportCVAdditionInfo(string auth, string sign, string mkey)
        {
            if (dicAdditionCache.TryGetValue(mkey, out ServerResponseInfo sri))
            {
                if (sri.sign == sign && sri.time.CompareTo(DateTime.Now) > 0)
                {
                    //使用cache回应
                    return sri.server_chipertext;
                }
            }

            string json = "{auth:\"" + auth + "\",sign:\"" + sign + "\"}";
            var result = await HttpHelperByHttpClient.HttpRequestHtml2("http://rtvs.cvtsp.com:15399/rtvs/UploadAdditionInfo?key=" + System.Web.HttpUtility.UrlEncode(mkey.ToCJson()), true, System.Threading.CancellationToken.None, data: "data=" + json, encoding: System.Text.Encoding.UTF8, errLog: false);
            //var result = await HttpHelperByHttpClient.HttpRequestHtml2("http://************:15399/rtvs/UploadAdditionInfo?key=" + System.Web.HttpUtility.UrlEncode(mkey.ToCJson()), true, System.Threading.CancellationToken.None,data: "data=" + json, encoding: System.Text.Encoding.UTF8);
            if (result == null || result.Length == 0)
            {
                return "";
            }
            string ret_ciphertext = Encoding.UTF8.GetString(result);

            //加入cache
            ServerResponseInfo addition_cache = new ServerResponseInfo();
            addition_cache.time = AuthParseServerCipher(ret_ciphertext, ref addition_cache.errcode);
            addition_cache.sign = sign;
            addition_cache.server_chipertext = ret_ciphertext;
            dicAdditionCache.AddOrUpdate(mkey, addition_cache, (key, old_value) =>
            {
                return addition_cache;
            });

            return ret_ciphertext;
        }

        private async Task<string> ReportRecord(string auth, string sign, string mkey)
        {
            if (dicRecord.TryGetValue(mkey, out ServerResponseInfo sri))
            {
                if (sri.sign == sign && sri.time.CompareTo(DateTime.Now) > 0)
                {
                    //使用cache回应
                    return sri.server_chipertext;
                }
            }

            string json = "{auth:\"" + auth + "\",sign:\"" + sign + "\"}";
            var result = await HttpHelperByHttpClient.HttpRequestHtml2("http://rtvs.cvtsp.com:15399/rtvs/RecordReport?key=" + System.Web.HttpUtility.UrlEncode(mkey.ToCJson()), true, System.Threading.CancellationToken.None, data: "data=" + json, encoding: System.Text.Encoding.UTF8);
            //var result = await HttpHelperByHttpClient.HttpRequestHtml2("http://************:15399/rtvs/RecordReport?key=" + System.Web.HttpUtility.UrlEncode(mkey.ToCJson()), true, System.Threading.CancellationToken.None,data: "data=" + json, encoding: System.Text.Encoding.UTF8);
            if (result == null || result.Length == 0)
            {
                return "";
            }
            string ret_ciphertext = Encoding.UTF8.GetString(result);

            //加入cache
            ServerResponseInfo sri_cache = new ServerResponseInfo();
            sri_cache.time = AuthParseServerCipher(ret_ciphertext, ref sri_cache.errcode);
            sri_cache.sign = sign;
            sri_cache.server_chipertext = ret_ciphertext;
            dicRecord.AddOrUpdate(mkey, sri_cache, (key, old_value) =>
            {
                return sri_cache;
            });

            return ret_ciphertext;
        }

        #region 解密
        [DllImport("RTVS", EntryPoint = "AuthDecrypt", CallingConvention = CallingConvention.StdCall)]
        extern static int AuthDecrypt(IntPtr data, UInt32 size, IntPtr data_out, UInt32 out_size);
        /// <summary>
        /// 解析服务器返回密文
        /// </summary>
        /// <param name="text_server"></param>
        /// <param name="errcode"></param>
        /// <returns></returns>
        private DateTime AuthParseServerCipher(string text_server, ref int errcode)
        {
            //text_server = "0e7OdXnFV9SyKsk9lwtiYIAu0znGtk0UbAyefQI+KsbfHbgCVYIUft0gZZOsWkEhTJKZnghfwLvpqlpykLe/bJ4yRkxhcwxk3pU1YgczH3VZ8YdBGtQQVb9Eapt5ioux0Wq/AkBTz+2pgj06/HCdB8VHC0C0ZQVKYUSp42dmzyRXLfd5gQnM544Mlw5vzvmg1Dg/z8vZVRw3ROIeZOxLZqUXMELPgpBNwfHkKbYauZqjza2ZJgU5tcccE+k/T8PXduJiTUoTygPrhhE0ZqDvnuFOQkiaFZHsRy0fqrMMcbH2YyivqZH7enx5tgIAyxCndLP/VCnghnrhX3aUMcPWWA==";
            //m_original_text = "E3060400FFFBEBBF";

            errcode = 0;
            if (text_server == null)
                return DateTime.Now;
            try
            {
                using (var data_out = new HGlobal(512))
                {
                    int size;
                    using (var data = new HGlobalAnsi(text_server))
                    {
                        size = AuthDecrypt(data.Data, (uint)text_server.Length, data_out.Data, 512);
                    }
                    var text = Marshal.PtrToStringUTF8(data_out.Data + 12, size - 12);
                    if (text.Length >= 12)
                    {
                        byte[] tmp = new byte[8];
                        Marshal.Copy(data_out.Data, tmp, 0, 4);
                        errcode = ByteHelper.Byte4ToInt(tmp, 0);

                        Marshal.Copy(data_out.Data + 4, tmp, 0, 8);
                        ulong utc_time = ByteHelper.Byte8ToULong(tmp, 0);
                        DateTime time = DateTimeHelper.UNIXtoDateTime((long)utc_time);
                        return time;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteLog4("AuthParseServerText failed:" + ex.ToString());
            }
            return DateTime.Now;
        }


        #endregion

    }
}
