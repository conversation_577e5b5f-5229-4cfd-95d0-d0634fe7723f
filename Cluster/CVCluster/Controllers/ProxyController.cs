﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SQ.Base.Cluster;
using System.Text.RegularExpressions;
using SQ.Base;
using System.Threading;
using System.Net.Http;

namespace CVCluster.Controllers
{
    public class ProxyController : Controller
    {


        private int GetUrl(Microsoft.AspNetCore.Http.HttpRequest request, out string url)
        {
            url = null;
            var mth = reg.Match(request.Path);
            if (mth != null && mth.Success)
            {
                if (int.TryParse(mth.Groups[5].Value, out var type))
                {
                    var conf = ClusterManagement.Instance.GetConf(type);
                    if (conf == null)
                    {
                        return 404;
                    }
                    if (mth.Groups[2].Success && !conf.CanProxy)
                    {
                        return 403;
                    }
                    if (mth.Groups[3].Success && !conf.CanAgent)
                    {
                        return 403;
                    }
                    if (mth.Groups[4].Success && !conf.CanAgentPort)
                    {
                        return 403;
                    }
                    var item = ClusterManagement.Instance.GetBestServer(type, null);
                    if (item != null)
                    {
                        url = item.ConnInfo;
                        if (mth.Groups[4].Success)
                        {
                            var url_host = "http";
                            if (request.IsHttps)
                            {
                                url_host += "s";
                            }
                            url_host += "://" + request.Host.Host;

                            var i = url.IndexOf(":", 6);
                            if (i >= 0)
                            {
                                url = url_host + url.Substring(i);
                            }
                            else
                            {
                                url = url_host;
                            }
                        }
                        if (mth.Groups[6].Success)
                        {
                            url += mth.Groups[6].Value;
                        }
                        if (request.QueryString.HasValue)
                        {
                            url += request.QueryString;
                        }
                        return 200;
                    }
                    return 502;
                }
            }
            return 400;
        }
        static Regex reg = new Regex(@"((Proxy)|(Agent)|(AgentPort))/(\d+)(/.+){0,1}", RegexOptions.Compiled);

        [Route("Proxy/{*url}", Order = 999)]
        public async Task CatchAll()
        {
            var errcode = GetUrl(HttpContext.Request, out var url);
            if (errcode == 200)
            {
                await ProxyRequest(url);
            }
            else
            {
                Response.StatusCode = errcode;
            }
        }

        [Route("Agent/{*url}", Order = 999)]
        public IActionResult CatchAll2()
        {
            var errcode = GetUrl(HttpContext.Request, out var url);
            if (errcode == 200)
            {
                return new RedirectResult(url);
            }
            else
            {
                Response.StatusCode = errcode;
                return new EmptyResult();
            }
        }

        [Route("AgentPort/{*url}", Order = 999)]
        public IActionResult CatchAll3()
        {
            var errcode = GetUrl(HttpContext.Request, out var url);
            if (errcode == 200)
            {
                return new RedirectResult(url);
            }
            else
            {
                Response.StatusCode = errcode;
                return new EmptyResult();
            }
        }




        static Regex regProxyTag = new Regex(@"ProxyTag/([^/]+)/(\d+)(/.+){0,1}", RegexOptions.Compiled);
        private int GetProxyTagUrl(Microsoft.AspNetCore.Http.HttpRequest request, out string url)
        {
            url = null;
            var mth = regProxyTag.Match(request.Path);
            if (mth != null && mth.Success)
            {
                if (int.TryParse(mth.Groups[2].Value, out var type))
                {
                    var conf = ClusterManagement.Instance.GetConf(type);
                    if (conf == null)
                    {
                        return 404;
                    }
                    if (!conf.CanProxy)
                    {
                        return 403;
                    }
                    var item = ClusterManagement.Instance.GetBestServer(type, mth.Groups[1].Value);
                    if (item != null)
                    {
                        url = item.ConnInfo;
                        if (mth.Groups[3].Success)
                        {
                            url += mth.Groups[3].Value;
                        }
                        if (request.QueryString.HasValue)
                        {
                            url += request.QueryString;
                        }
                        return 200;
                    }
                    return 502;
                }
            }
            return 400;
        }
        [Route("ProxyTag/{*url}", Order = 999)]
        public async Task ProxyTag()
        {

            var errcode = GetProxyTagUrl(HttpContext.Request, out var url);
            if (errcode == 200)
            {
                await ProxyRequest(url);
            }
            else
            {
                Response.StatusCode = errcode;
            }
        }

        private async Task ProxyRequest(string url)
        {
            DictionaryEx<string, string> dit = new DictionaryEx<string, string>();

            foreach (var item in HttpContext.Request.Headers)
            {
                dit.Add(item.Key, item.Value.ToString());
            }
            Task<HttpResponseMessage> res;
            if (HttpContext.Request.Method == "POST")
            {
                if (HttpContext.Request.ContentLength > 0)
                {
                    var bts = new byte[HttpContext.Request.ContentLength.Value];
                    await HttpContext.Request.Body.ReadAsync(bts, 0, bts.Length);

                    res = HttpHelperByHttpClient.HttpRequestRaw(url, true, dit, bts, CancellationToken.None);
                }
                else
                {
                    res = HttpHelperByHttpClient.HttpRequestRaw(url, true, dit, null, CancellationToken.None);
                }
            }
            else
            {
                res = HttpHelperByHttpClient.HttpRequestRaw(url, false, dit, null, CancellationToken.None);
            }

            var response = await res;
            // 将状态码设置到当前请求的Response中
            Response.StatusCode = (int)response.StatusCode;

            // 将响应头信息复制到当前请求的Response中
            foreach (var header in response.Headers)
            {
                Response.Headers[header.Key] = string.Join(",", header.Value);
            }

            // 将Content Header信息复制到当前请求的Response中
            foreach (var header in response.Content.Headers)
            {
                Response.Headers[header.Key] = string.Join(",", header.Value);
            }
            if (Response.Headers["Transfer-Encoding"] == "chunked" && response.Content.Headers.ContentLength.HasValue)
            {
                Response.Headers.Remove("Transfer-Encoding");
                Response.Headers.ContentLength = response.Content.Headers.ContentLength;
                //var chunk_size = (response.Content.Headers.ContentLength.Value.ToString("x") + "\r\n").ToUtf8Bytes();
                //await Response.Body.WriteAsync(chunk_size);
                //// 将响应内容写入当前请求的Response中
                //await response.Content.CopyToAsync(Response.Body);
                //await Response.Body.WriteAsync(new byte[2] { 0x0d, 0x0a });
            }
            //else
            //{
            // 将响应内容写入当前请求的Response中
            await response.Content.CopyToAsync(Response.Body);
            //}
            //Response.ContentLength = response.Content.Headers.ContentLength;
        }
    }
}