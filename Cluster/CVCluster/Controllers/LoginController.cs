﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using CVCluster.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

// For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace CVCluster.Controllers
{
    public class LoginController : Controller
    {
        // GET: /<controller>/
        public IActionResult SignIn(UserModel userModel)
        {
            if (ModelState.IsValid)
            {
                MD5 md5Hash = MD5.Create();
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(Program.wsServer.Config.WebUsrPwd));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < data.Length; i++)
                {
                    builder.Append(data[i].ToString("x2")); // 将每个字节转换为十六进制并添加到结果中
                }
                var webPwd = builder.ToString();

                //Program.task.Config.WebUsrPwd 加密md5
                //MD5CryptoServiceProvider md5Hasher = new MD5CryptoServiceProvider();

                //检查用户信息
                if (userModel.Username == Program.wsServer.Config.WebUsrName && userModel.Password == webPwd)
                {
                    userModel.Password = Program.wsServer.Config.WebUsrPwd;
                    //记录Session
                    HttpContext.Session.SetInt32("Login", 1);
                    if (userModel.RememberMe)
                    {
                        HttpContext.Response.Cookies.Append("Authentication"
                            , SQ.Base.EnDecrypt.EnDecrypt.MD5(userModel.Username + "_" + userModel.Password)
                            , new CookieOptions { Expires = DateTime.Now.AddDays(10) });
                    }
                    //跳转到系统首页
                    return RedirectToAction("Index", "Api");
                }
                ViewBag.ErrorInfo = "用户名或密码错误";
                return View(userModel);
            }
            ViewBag.ErrorInfo = ModelState.Values.First().Errors[0].ErrorMessage;
            return View(userModel);
        }

        public IActionResult SignOut()
        {
            //清除Session
            HttpContext.Session.Clear();
            HttpContext.Response.Cookies.Delete("Authentication");
            //跳转到系统登录界面
            return RedirectToAction("SignIn", "Login");
        }

    }
}
