<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <DockerTargetOS>Linux</DockerTargetOS>
    <UserSecretsId>************************************</UserSecretsId>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Description>cv集群管理</Description>
    <Version>1.1.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Error_log\**" />
    <Compile Remove="LogFile\**" />
    <Content Remove="Error_log\**" />
    <Content Remove="LogFile\**" />
    <EmbeddedResource Remove="Error_log\**" />
    <EmbeddedResource Remove="LogFile\**" />
    <None Remove="Error_log\**" />
    <None Remove="LogFile\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Base\CV.Base\CV.Base.csproj" />
    <ProjectReference Include="..\..\Base\CV.Network\CV.Network.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ApiServer.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
