﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CVCluster.Models
{
    public class ConfModel
    {
        public ConfModel()
        {
            WsPort = 17000;
            TimeoutSec = 60;
            CdnID = "";
            CdnUrl = "";
            AKey = "";
            WebUsrName = "rtvsweb";
            WebUsrPwd = "admin";
        }
        public int WsPort { get; set; }
        public string X509FileName { get; set; }
        public string X509Password { get; set; }
        public string PemFileName { get; set; }
        public string PemKeyFileName { get; set; }
        public double TimeoutSec { get; set; }

        public string CdnID { get; set; }
        public string AKey { get; set; }
        public string CdnUrl { get; set; }
        public string WebUsrName { get; set; }
        public string WebUsrPwd { get; set; }
    }
}
