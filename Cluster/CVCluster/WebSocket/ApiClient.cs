﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CV.Network;
using Network;
using SQ.Base;
using SQ.Base.Cluster;

namespace CVCluster.WebSocket
{
    public class ApiClient
    {
        private WebSocketChannel channel;

        public long Key { get; internal set; }
        public DateTime LastUpdate { get; internal set; }

        string m_ucid, m_akey, m_cdn_url;
        public ApiClient(WebSocketChannel channel, string ucid = null, string akey = null, string cdn_url = null)
        {
            this.channel = channel;
            LastUpdate = DateTime.Now;

            m_ucid = ucid;
            m_akey = akey;
            m_cdn_url = cdn_url;
        }

        public void Receive(object sender, ChannelReceiveArg arg)
        {
            try
            {
                if (arg.Data.Length > 0)
                {
                    if (arg.Data[0] == 1)
                    {
                        if (arg.Data.Length >= 5)
                        {
                            //GetBest(int Type, string Tag)
                            int Type = ByteHelper.Byte4ToInt(arg.Data[1], arg.Data[2], arg.Data[3], arg.Data[4]);
                            string Tag = null;
                            if (arg.Data.Length > 5)
                            {
                                Tag = ByteHelper.GBKToString(arg.Data, 5);
                            }

                            ServerInfo item = null;
                            item = ClusterManagement.Instance.GetBestServer(Type, Tag);
                            if (item != null)
                            {
                                channel.Send(ByteHelper.StringToGBK(item.ConnInfo));
                            }
                            else
                            {
                                channel.Close();
                            }

                        }
                    }
                    else if (arg.Data[0] == 2)      //使用cdn
                    {
                        if (arg.Data.Length >= 5)
                        {
                            //GetBest(int Type, string Tag)
                            int Type = ByteHelper.Byte4ToInt(arg.Data[1], arg.Data[2], arg.Data[3], arg.Data[4]);
                            string Tag = null;
                            if (arg.Data.Length > 5)
                            {
                                Tag = ByteHelper.GBKToString(arg.Data, 5);
                            }
                            if (string.IsNullOrWhiteSpace(m_ucid) || string.IsNullOrWhiteSpace(m_akey) || string.IsNullOrWhiteSpace(m_cdn_url))
                            {
                                channel?.Close();
                            }
                            else
                            {
                                CdnRequest.GetCdnServer(Type, m_cdn_url, Tag, m_ucid, m_akey, (item) =>
                                {
                                    if (item != null)
                                    {
                                        channel?.Send(ByteHelper.StringToGBK(item.ConnInfo));
                                    }
                                    else
                                    {
                                        channel?.Close();
                                    }
                                });
                            }


                        }

                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLog.WriteLog4Ex("Receive", ex);
                channel.Close();
            }
        }

        public void DataSend(object sender, ChannelSendArg arg)
        {
        }
        /// <summary>
        /// 检查
        /// </summary>
        /// <param name="TimeoutSec"></param>
        /// <returns>false 表示无变化 true 表示被移除</returns>
        public bool Timeout(double TimeoutSec)
        {
            if (LastUpdate.DiffNowSec() > TimeoutSec)
            {
                channel.Close();
                return true;
            }
            return false;
        }
    }
}
