﻿using CV.Network;
using CVCluster.Models;
using Network;
using SQ.Base;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace CVCluster.WebSocket
{
    public class ApiServer
    {
        WebSocketServer webSocketServer;
        public ConfModel Config { get; protected set; }

        private ConcurrentDictionary<long, ApiClient> ClientPool = new ConcurrentDictionary<long, ApiClient>();
        ThreadWhile<object> threadWhile = new ThreadWhile<object>();
        public bool ReadConfig()
        {
            try
            {
                Config = SerializableHelper.DeserializeSetting<ConfModel>(FileHelp.GetMyConfPath() + "ApiServer.xml");
                return true;
            }
            catch (Exception ex)
            {
                Log.WriteLog4("ReadConfig:" + ex.ToString(), LOGTYPE.ERRORD);
                Config = new ConfModel();
                return false;
            }
        }
        public void Start()
        {
            try
            {
                ReadConfig();
                webSocketServer = new CV.Network.WebSocketServer("0.0.0.0", Config.WsPort
                    , SQ.Base.Certificate.CertificateHelper.GetX509Certificate2(Config.X509FileName, Config.X509Password, Config.PemFileName, Config.PemKeyFileName));
                webSocketServer.ChannelConnect += MServer_ChannelConnect;
                webSocketServer.ChannelDispose += MServer_ChannelDispose;
                if (webSocketServer.Start())
                {
                    Log.WriteLog4("Start WebSocket service success 0.0.0.0:" + Config.WsPort, LOGTYPE.INFO);
                    threadWhile.SleepMs = 30000;
                    threadWhile.StartIfNotRun(Check, null, "CheckThread");
                }
                else
                {
                    Log.WriteLog4("Start WebSocket service failed 0.0.0.0:" + Config.WsPort, LOGTYPE.INFO);
                }
            }
            catch (Exception ex)
            {
                ErrorLog.WriteLog4Ex("Start", ex);
            }
        }

        private void Check(object tag, CancellationToken cancellationToken)
        {
            var arr = ClientPool.Keys.ToArray();
            foreach (var key in arr)
            {
                if (ClientPool.TryGetValue(key, out var client))
                {
                    if (client.Timeout(Config.TimeoutSec))
                    {
                        ClientPool.TryRemove(client.Key, out var _);
                    }
                }
            }
        }

        public void Stop()
        {
            try
            {
                if (webSocketServer != null)
                {
                    webSocketServer.Stop();
                    threadWhile.Abort();
                    threadWhile.Join();
                }
            }
            catch (Exception ex)
            {
                ErrorLog.WriteLog4Ex("Stop", ex);
            }
        }

        private void MServer_ChannelDispose(object sender, ChannelDisposeArg arg)
        {
            try
            {
                if (arg.Channel.Tag != null)
                {
                    var client = (ApiClient)arg.Channel.Tag;
                    arg.Channel.Tag = null;
                    ClientPool.TryRemove(client.Key, out var _);
                    Log.WriteLog4("MServer_ChannelDispose SocketError:" + arg.SocketError);
                }
            }
            catch (Exception exChannelDisposed)
            {
                Log.WriteLog4Ex("MServer_ChannelDispose", exChannelDisposed);
            }
        }

        private void MServer_ChannelConnect(object sender, ChannelConnectArg arg)
        {
            try
            {
                if (arg.SocketError == System.Net.Sockets.SocketError.Success)
                {
                    int port = ((IPEndPoint)arg.Channel.Socket.LocalEndPoint).Port;

                    ApiClient client;

                    client = new ApiClient((WebSocketChannel)arg.Channel, Config.CdnID, Config.AKey, Config.CdnUrl);
                    client.Key = GetKey();
                    ClientPool[client.Key] = client;

                    //连接相关
                    arg.Channel.Tag = client;
                    arg.Channel.DataReceive = client.Receive;
                    arg.Channel.DataSend = client.DataSend;
                    arg.Channel.StartReceiveAsync();

                    Log.WriteLog4("MServer_ChannelConnect " + arg.Channel.RemoteHost + ":" + arg.Channel.RemotePort);
                }
            }
            catch (Exception exChannelDisposed)
            {
                ErrorLog.WriteLog4Ex("MServer_ChannelConnect", exChannelDisposed);
            }
        }
        private long GetKey()
        {
            long key;
            do
            {
                key = DateTime.Now.Ticks;
            } while (this.ClientPool.ContainsKey(key));
            return key;
        }

    }
}
