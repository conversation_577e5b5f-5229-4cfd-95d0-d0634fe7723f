﻿using SQ.Base;
using SQ.Base.Cluster;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace CVCluster.WebSocket
{
    public class CdnRequest
    {

        class CdnResult
        {
            public int ret { get; set; }
            public string address { get; set; }
            public int port { get; set; }
            public int oid { get; set; }
            public string reason { get; set; }
        }
        public delegate void GetCdnServerCB(ServerInfo info);
        public static async void GetCdnServer(int type, string url, string tag, string ucid, string akey, GetCdnServerCB fun)
        {
            var req = $"{url}?type={type}&ucid={ucid}&akey={akey}&tag={tag}";
            Log.WriteLog4($"Cdn requesr url:{req}");

            //HttpClient client = new HttpClient();
            //client.BaseAddress = new Uri(url);
            //client.Timeout = TimeSpan.FromSeconds(10);
            //var result_str = await client.GetStringAsync(qstr);

            var result_str = await HttpHelperByHttpClient.HttpRequestHtml(req, false, CancellationToken.None);

            try
            {
                var result_cdn = JsonSerializer.Deserialize<CdnResult>(result_str);
                if (result_cdn.ret != 0)
                {
                    Log.WriteLog4("Cdn requesr failed:" + result_cdn.reason);
                    fun(null);
                    return;
                }

                ServerInfo item = new ServerInfo();
                item.ConnInfo = result_cdn.address + ":" + result_cdn.port;
                if (type == 1003)
                    item.ConnInfo += ":v2";
                item.ConnInfo += ("#" + result_cdn.oid);

                fun(item);
            }
            catch (System.Exception ex)
            {
                Log.WriteLog4("Cdn requesr exception:" + ex.ToString());
                fun(null);
            }
        }
    }
}
