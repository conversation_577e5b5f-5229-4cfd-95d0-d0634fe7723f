﻿@model SQ.Base.Cluster.ServerConf

@{
    ViewData["Title"] = "Create";
}

<h2>添加</h2>

<h4>配置信息</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Name" class="control-label">服务名称</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Type" class="control-label">服务类型ID</label>
                <input asp-for="Type" class="form-control" />
                <span asp-validation-for="Type" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="NowVer" class="control-label">当前版本</label>
                <input asp-for="NowVer" class="form-control" />
                <span asp-validation-for="NowVer" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="TimeoutMsec" class="control-label">心跳超时时间(毫秒)</label>
                <input asp-for="TimeoutMsec" class="form-control" />
                <span asp-validation-for="TimeoutMsec" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ReMark" class="control-label">服务说明</label>
                <input asp-for="ReMark" class="form-control" />
                <span asp-validation-for="ReMark" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="添加" class="btn btn-default" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">返回列表页面</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
