﻿@model IEnumerable<SQ.Base.Cluster.ServerConf>
@{
    ViewData["Title"] = "Home Page";
}

<div id="myCarousel" class="carousel slide" data-ride="carousel" data-interval="6000">

    <table class="table">
        <thead>
            <tr>
                <th>
                    服务名称
                </th>
                <th>
                    服务类型ID
                </th>
                <th>
                    当前版本
                </th>
                <th>
                    心跳超时时间(毫秒)
                </th>
                <th>
                    服务数
                </th>
                <th>
                    服务说明
                </th>
                <th></th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.Name)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Type)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.NowVer)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.TimeoutMsec)
                    </td>
                    <td>
                        @item.ServerList.Count
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.ReMark)
                    </td>
                    <td>
                        @Html.ActionLink("修改", "Edit", new { id = item.Type }) |
                        @*@Html.ActionLink("删除", "Delete", new { id = item.Type }) |*@
                        @Html.ActionLink("服务列表", "Details", new { id = item.Type })
                    </td>
                    <td>
                        @{
                            var flag = true;
                        }
                        @if (item.CanProxy)
                        {
                            if (flag)
                            {
                                flag = false;
                            }
                            else
                            {
                                @Html.Raw(" |")
                            }
                            <a href="/Proxy/@item.Type">代理</a>
                        }
                        @if (item.CanAgent)
                        {
                            if (flag)
                            {
                                flag = false;
                            }
                            else
                            {
                                @Html.Raw(" |")
                            }
                            <a href="/Agent/@item.Type">跳转</a>
                        }
                        @if (item.CanAgentPort)
                        {
                            if (flag)
                            {
                                flag = false;
                            }
                            else
                            {
                                @Html.Raw(" |")
                            }
                            <a href="/AgentPort/@item.Type">跳转(仅端口)</a>
                        }
                    </td>
                </tr>
            }
        </tbody>
    </table>
    <div class="row">
        <div class="col-xs-4">

            <a asp-action="Create" class="btn btn-primary btn-block btn-flat">添加新服务</a>
        </div>
    </div>
</div>

<b>API</b>
<div class="row">
    <div class="col-md-3">
        <h2>服务配置</h2>
        <ul>
            <li>/SetConf?Conf=</li>
            <li>参数说明<br />Conf ServerConf 配置信息</li>
            <li>此接口可添加或修改服务配置</li>
        </ul>
    </div>
    <div class="col-md-3">
        <h2>服务发现</h2>
        <ul>
            <li>/Heartbeat?data=</li>
            <li>参数说明<br />data List&lt;ServerInfo&gt; 服务信息</li>
            <li>此接口定时向集群管理报告自己的状态。</li>
            <li>此接口也用做心跳，用来判断服务是否存活。</li>
        </ul>
    </div>
    <div class="col-md-3">
        <h2>获取最优服务</h2>
        <ul>
            <li>/GetBest?Type=</li>
            <li>参数说明<br />Type int 服务类型ID</li>
            <li>此接口可通过集群管理获取当前最优支持服务</li>
            <li>注:当前版本还未支持按负载量挑选</li>
        </ul>
    </div>
    <div class="col-md-3">
        <h2>服务升级</h2>
        <ul>
            <li>/Upgrade?Type=&NewVer=</li>
            <li>参数说明<br />Type int 服务类型ID<br />NewVer string 版本号</li>
            <li>此接口可升级服务配置的版本号，也可通过服务配置接口完成</li>
        </ul>
    </div>
</div>
