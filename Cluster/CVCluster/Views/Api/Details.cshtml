﻿@model SQ.Base.Cluster.ServerConf

@{
    ViewData["Title"] = "Details";
    var lst = Model.ServerList.GetList(Model.NowVer);
}

<h2>服务列表</h2>

<div>

    <h3>@(Model.Name + "(" + Model.Type + ")")</h3>
    <h4>当前版本:@Model.NowVer 超时:@(Model.TimeoutMsec + "毫秒")</h4>
    @Model.ReMark
    <hr />


    <h4>当前版本</h4>
    <table class="table">
        <thead>
            <tr>
                <th>
                    连接信息
                </th>
                <th>
                    版本
                </th>
                <th>
                    负载量
                </th>
                <th>
                    启动时间
                </th>
                <th>
                    上次心跳时间
                </th>
                <th>
                    附加信息
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in lst.NowVer)
            {
                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.ConnInfo)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Ver)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.OnlineCount)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.StartTime)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.LastHeartbeat)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Extra)
                    </td>
                </tr>
            }
        </tbody>
    </table>

    <hr />
    <h4>其他版本</h4>
    <table class="table">
        <thead>
            <tr>
                <th>
                    连接信息
                </th>
                <th>
                    版本
                </th>
                <th>
                    负载量
                </th>
                <th>
                    启动时间
                </th>
                <th>
                    上次心跳时间
                </th>
                <th>
                    附加信息
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in lst.OtherVer)
            {
                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.ConnInfo)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Ver)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.OnlineCount)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.StartTime)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.LastHeartbeat)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Extra)
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>
<div>
    <a asp-action="Index">返回列表页面</a>
</div>
