﻿@model SQ.Base.Cluster.ServerConf

@{
    ViewData["Title"] = "Delete";
}

<h2>删除</h2>

<h3>确认删除此数据?</h3>
<div>
    <h4>ServerConf</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            服务名称
        </dt>
        <dd>
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt>
            服务类型ID
        </dt>
        <dd>
            @Html.DisplayFor(model => model.Type)
        </dd>
        <dt>
            当前版本
        </dt>
        <dd>
            @Html.DisplayFor(model => model.NowVer)
        </dd>
        <dt>
            心跳超时时间(毫秒)
        </dt>
        <dd>
            @Html.DisplayFor(model => model.TimeoutMsec)
        </dd>
        <dt>
            服务说明
        </dt>
        <dd>
            @Html.DisplayFor(model => model.ReMark)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="submit" value="删除" class="btn btn-default" /> |
        <a asp-action="Index">返回列表页面</a>
    </form>
</div>
