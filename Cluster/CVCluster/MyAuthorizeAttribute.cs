﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CVCluster
{
    public class MyAuthorizeAttribute : Attribute, IActionFilter
    {
        public void OnActionExecuted(ActionExecutedContext context)
        {
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
            if (!context.HttpContext.Session.TryGetValue("Login", out var result) || result == null)
            {
                if (context.HttpContext.Request.Cookies.TryGetValue("Authentication", out var cookie))
                {
                    if (cookie == SQ.Base.EnDecrypt.EnDecrypt.MD5(Program.wsServer.Config.WebUsrName + "_" + Program.wsServer.Config.WebUsrPwd))
                    {
                        context.HttpContext.Session.SetInt32("Login", 1);
                        return;
                    }

                }
                context.Result = new RedirectResult("/Login/SignIn");
                return;
            }
        }
    }
}
