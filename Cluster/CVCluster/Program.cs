﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SQ.Base;

namespace CVCluster
{
    public class Program
    {
        public static WebSocket.ApiServer wsServer = new WebSocket.ApiServer();
        public static void Main(string[] args)
        {
            ByteHelper.RegisterGBKEncoding();
            SQ.Base.Cluster.ClusterManagement.Instance.StartCheck();
            wsServer.Start();
            CreateHostBuilder(args).Build().Run();
            wsServer.Stop();
            SQ.Base.Cluster.ClusterManagement.Instance.StopCheck();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });
    }
}
