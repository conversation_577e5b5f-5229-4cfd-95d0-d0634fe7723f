# RTVS Python API

基于现有JavaScript功能的完整Python API实现，用于与RTVS (实时视频监控系统) 进行交互。

## 功能特性

### 🎥 视频播放控制
- 实时视频播放
- 历史视频回放
- 多码流支持 (主码流/子码流)
- WebSocket连接管理

### 🎤 音频对讲
- 音频文件上传和推流
- 实时音频对讲
- 支持多种音频格式 (MP3, WAV等)
- G.711A编码支持
- 自动token获取和管理

### 📊 设备管理
- 设备状态查询
- 服务器统计信息
- 设备控制命令
- 录像文件查询

### 🌐 RESTful API
- 完整的REST API接口
- FastAPI框架
- 自动API文档生成
- 异步处理支持

## 快速开始

### 1. 环境准备

```bash
# 安装Python 3.8+
python --version

# 安装依赖
pip install -r requirements.txt

# 安装FFmpeg (音频处理需要)
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# Windows
# 从 https://ffmpeg.org/download.html 下载并添加到PATH
```

### 2. 配置设置

编辑 `config.yaml` 文件，设置您的RTVS服务器信息：

```yaml
server:
  host: "*************"      # 您的RTVS服务器IP
  port: 17000                # RTVS主端口
  web_port: 9081             # Web API端口
  websocket_port: 18002      # WebSocket端口
```

### 3. 启动服务

```bash
# 方式1: 直接运行
python rtvs_python_api.py

# 方式2: 使用uvicorn
uvicorn rtvs_python_api:app --host 0.0.0.0 --port 8000 --reload

# 服务将在 http://127.0.0.1:8000 启动
```

### 4. 查看API文档

启动服务后，访问以下地址查看自动生成的API文档：

- Swagger UI: http://127.0.0.1:8000/docs
- ReDoc: http://127.0.0.1:8000/redoc

## API接口说明

### 服务器管理

```bash
# 获取服务器统计
GET /api/server/stats

# 获取设备统计
GET /api/device/{sim}/stats

# 获取访问令牌
GET /api/token
```

### 视频播放

```bash
# 开始视频播放
POST /api/video/play/start
{
  "sim": "13800138000",
  "channel": 1,
  "stream_type": 0,
  "play_type": "realtime"
}

# 停止视频播放
POST /api/video/play/stop/{connection_id}
```

### 音频对讲

```bash
# 开始音频对讲
POST /api/audio/talk/start
Content-Type: multipart/form-data
- sim: 13800138000
- channel: 1
- audio_file: [音频文件]
```

### 设备控制

```bash
# 设备控制
POST /api/device/control
{
  "sim": "13800138000",
  "channel": 1,
  "command": 0,
  "param": {"type": "stop"}
}

# 查询录像文件
GET /api/video/files/{sim}/{channel}?start_time=2024-12-01 00:00:00&end_time=2024-12-01 23:59:59
```

## 使用示例

### Python客户端示例

```python
import asyncio
import httpx

async def example():
    async with httpx.AsyncClient() as client:
        # 获取服务器状态
        response = await client.get("http://127.0.0.1:8000/api/server/stats")
        print(response.json())
        
        # 开始视频播放
        video_request = {
            "sim": "13800138000",
            "channel": 1,
            "stream_type": 0,
            "play_type": "realtime"
        }
        response = await client.post(
            "http://127.0.0.1:8000/api/video/play/start",
            json=video_request
        )
        print(response.json())

asyncio.run(example())
```

### curl示例

```bash
# 健康检查
curl http://127.0.0.1:8000/health

# 获取服务器统计
curl http://127.0.0.1:8000/api/server/stats

# 音频对讲
curl -X POST http://127.0.0.1:8000/api/audio/talk/start \
  -F "sim=13800138000" \
  -F "channel=1" \
  -F "audio_file=@test.mp3"
```

## 与JavaScript功能对比

| JavaScript功能 | Python API等价功能 | 说明 |
|---|---|---|
| TreeGrid.js | `/api/device/{sim}/stats` | 设备树形结构数据 |
| CvNetVideo.StartPlay() | `POST /api/video/play/start` | 开始视频播放 |
| CvNetVideo.StopPlay() | `POST /api/video/play/stop/{id}` | 停止视频播放 |
| CvNetVideo.StartSpeek() | `POST /api/audio/talk/start` | 开始音频对讲 |
| CvNetVideo.AVTransferControl() | `POST /api/device/control` | 设备控制 |
| CvNetVideo.QueryVideoFileList() | `GET /api/video/files/{sim}/{channel}` | 查询录像文件 |

## 协议支持

- **JT/T 1078**: 交通部音视频传输协议
- **GB28181**: 国标视频监控协议
- **WebSocket**: 实时数据传输
- **HTTP/REST**: API接口通信

## 开发和调试

### 运行示例

```bash
# 运行完整示例
python example_usage.py
```

### 日志配置

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 测试

```bash
# 安装测试依赖
pip install pytest pytest-asyncio

# 运行测试
pytest tests/
```

## 部署建议

### Docker部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "rtvs_python_api:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 生产环境

```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn rtvs_python_api:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 注意事项

1. **协议兼容性**: 确保Python实现的协议包格式与C#后端完全一致
2. **网络配置**: 检查防火墙和网络连接设置
3. **音频格式**: 支持的音频格式取决于FFmpeg安装
4. **并发处理**: WebSocket连接需要合理管理避免资源泄露
5. **错误处理**: 生产环境需要完善的错误处理和重试机制

## 故障排除

### 常见问题

1. **连接超时**: 检查RTVS服务器地址和端口配置
2. **音频推流失败**: 确认FFmpeg正确安装和音频文件格式
3. **协议不匹配**: 对比Python生成的数据包与C#后端期望格式
4. **权限问题**: 确认API访问权限和令牌有效性

### 调试方法

```python
# 启用详细日志
import logging
logging.getLogger("rtvs_python_api").setLevel(logging.DEBUG)

# 查看数据包内容
logger.debug(f"发送数据包: {packet.hex()}")
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目遵循与原RTVS项目相同的许可证。
