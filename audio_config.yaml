# RTVS 语音对讲配置文件

# 基本配置
app_code: "SV_CWTPRO"                                                    # 应用代码
config_url: "http://************/jt-video/video/websocket/address"      # Token配置获取地址
timeout: 10                                                              # 请求超时时间(秒)

# 音频配置
audio:
  sample_rate: 8000      # 采样率
  channels: 1            # 声道数
  sample_width: 2        # 采样位宽
  chunk_size: 320        # 数据包大小
  interval: 0.02         # 发送间隔(秒)

# 默认设备配置
default_device:
  sim: "13800138000"         # 默认SIM卡号
  channel: 1                 # 默认通道号
  device_id: "13896118973"   # 默认设备ID

# API服务配置
api:
  host: "0.0.0.0"        # API服务监听地址
  port: 8000             # API服务端口
  debug: true            # 调试模式

# 日志配置
logging:
  level: "INFO"          # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
