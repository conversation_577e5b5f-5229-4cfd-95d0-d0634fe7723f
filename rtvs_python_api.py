#!/usr/bin/env python3
"""
RTVS Python API Client
基于现有JavaScript功能的完整Python API实现
支持视频播放、设备管理、音频对讲等功能
"""

import asyncio
import websockets
import httpx
import logging
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from pydub import AudioSegment
from contextlib import asynccontextmanager
import io

# =============================================================================
# 1. 配置和数据模型
# =============================================================================

@dataclass
class RTVSConfig:
    """RTVS服务器配置"""
    host: str = "127.0.0.1"
    port: int = 17000
    web_port: int = 9081
    websocket_port: int = 18002
    protocol: str = "JT1078"  # JT1078 或 GB28181
    app_code: str = "SV_CWTPRO"

@dataclass
class DeviceInfo:
    """设备信息"""
    sim: str
    channel: int
    device_id: str
    status: str = "offline"
    last_seen: Optional[datetime] = None

@dataclass
class VideoPlayRequest:
    """视频播放请求"""
    sim: str
    channel: int
    stream_type: int = 0  # 0=主码流, 1=子码流
    play_type: str = "realtime"  # realtime, playback
    start_time: Optional[str] = None
    end_time: Optional[str] = None

@dataclass
class ServerStats:
    """服务器统计信息"""
    online_devices: int
    total_connections: int
    cpu_usage: float
    memory_usage: float
    uptime: int

# Pydantic模型用于API请求/响应
class VideoPlayRequestModel(BaseModel):
    sim: str
    channel: int
    stream_type: int = 0
    play_type: str = "realtime"
    start_time: Optional[str] = None
    end_time: Optional[str] = None

class AudioTalkRequest(BaseModel):
    sim: str
    channel: int
    duration: int = 30  # 对讲时长(秒)

class DeviceControlRequest(BaseModel):
    sim: str
    channel: int
    command: int  # 控制命令
    param: Optional[Dict] = None

# =============================================================================
# 2. RTVS客户端核心类
# =============================================================================

class RTVSClient:
    """RTVS客户端主类"""
    
    def __init__(self, config: RTVSConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.http_client = httpx.AsyncClient(timeout=30.0)
        self.websocket_connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.devices: Dict[str, DeviceInfo] = {}
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
        
    async def close(self):
        """关闭客户端连接"""
        await self.http_client.aclose()
        for ws in self.websocket_connections.values():
            await ws.close()

    async def get_server_config(self, app_code: str, device_id: str) -> dict:
        """
        获取JT1078音频发送器配置

        Args:
            app_code: 应用代码
            device_id: 设备ID

        Returns:
            dict: 包含address, port, token等信息的配置字典
        """
        # 使用您提供的配置URL
        url = "http://************/jt-video/video/websocket/address"
        params = {
            "appCode": app_code,
            "deviceId": device_id
        }

        try:
            self.logger.info(f"正在从 {url} 获取设备 {device_id} 的配置...")
            response = await self.http_client.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get("result") == 0:
                config = data.get("detail", {})
                self.logger.info(f"获取配置成功: {config}")

                # 验证必要的配置项
                required_keys = ["address", "port", "token"]
                if not all(k in config for k in required_keys):
                    missing_keys = [k for k in required_keys if k not in config]
                    raise Exception(f"返回的配置中缺少必要字段: {missing_keys}")

                return config
            else:
                error_msg = data.get('resultNote', '未知错误')
                raise Exception(f"服务器返回错误: {error_msg}")

        except httpx.RequestError as e:
            self.logger.error(f"请求配置接口失败: {e}")
            raise Exception(f"请求配置接口失败: {e}")
        except Exception as e:
            self.logger.error(f"解析配置数据失败: {e}")
            raise Exception(f"解析配置数据失败: {e}")
    
    # =============================================================================
    # 3. 服务器管理API
    # =============================================================================
    
    async def get_server_stats(self) -> ServerStats:
        """获取服务器统计信息"""
        try:
            url = f"http://{self.config.host}:{self.config.web_port}/api/Server/GetServerStat"
            response = await self.http_client.get(url)
            response.raise_for_status()
            data = response.json()
            
            return ServerStats(
                online_devices=data.get("OnlineDeviceCount", 0),
                total_connections=data.get("TotalConnections", 0),
                cpu_usage=data.get("CpuUsage", 0.0),
                memory_usage=data.get("MemoryUsage", 0.0),
                uptime=data.get("Uptime", 0)
            )
        except Exception as e:
            self.logger.error(f"获取服务器统计失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取服务器统计失败: {str(e)}")
    
    async def get_device_stats(self, sim: str) -> Dict:
        """获取设备统计信息"""
        try:
            url = f"http://{self.config.host}:{self.config.web_port}/api/Server/GetSimStat"
            params = {"sim": sim}
            response = await self.http_client.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"获取设备统计失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取设备统计失败: {str(e)}")
    
    async def get_token(self) -> Dict:
        """获取访问令牌"""
        try:
            url = f"http://{self.config.host}:{self.config.web_port}/api/Server/GetToken"
            response = await self.http_client.get(url)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"获取令牌失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取令牌失败: {str(e)}")
    
    # =============================================================================
    # 4. 视频播放控制API
    # =============================================================================
    
    async def start_video_play(self, request: VideoPlayRequest) -> Dict:
        """开始视频播放"""
        try:
            token_info = await self.get_token()
            
            if request.play_type == "realtime":
                return await self._start_realtime_play(request, token_info)
            elif request.play_type == "playback":
                return await self._start_playback_play(request, token_info)
            else:
                raise ValueError(f"不支持的播放类型: {request.play_type}")
                
        except Exception as e:
            self.logger.error(f"开始视频播放失败: {e}")
            raise HTTPException(status_code=500, detail=f"开始视频播放失败: {str(e)}")
    
    async def _start_realtime_play(self, request: VideoPlayRequest, token_info: Dict) -> Dict:
        """开始实时视频播放"""
        ws_url = f"ws://{self.config.host}:{self.config.websocket_port}"
        
        # 构建JT1078协议数据包
        packet = self._build_video_request_packet(
            request.sim, request.channel, request.stream_type, token_info
        )
        
        try:
            websocket = await websockets.connect(ws_url)
            await websocket.send(packet)
            
            # 等待服务器响应
            response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
            
            # 保存连接用于后续控制
            connection_id = f"{request.sim}_{request.channel}"
            self.websocket_connections[connection_id] = websocket
            
            return {
                "success": True,
                "connection_id": connection_id,
                "play_url": f"ws://{self.config.host}:{self.config.websocket_port}",
                "message": "实时视频播放已开始"
            }
            
        except Exception as e:
            self.logger.error(f"实时播放连接失败: {e}")
            raise
    
    async def _start_playback_play(self, request: VideoPlayRequest, token_info: Dict) -> Dict:
        """开始历史视频播放"""
        if not request.start_time or not request.end_time:
            raise ValueError("历史播放需要指定开始和结束时间")
        
        # 实现历史播放逻辑
        # 这里需要根据具体的协议实现
        return {
            "success": True,
            "message": "历史视频播放已开始",
            "start_time": request.start_time,
            "end_time": request.end_time
        }
    
    async def stop_video_play(self, connection_id: str) -> Dict:
        """停止视频播放"""
        try:
            if connection_id in self.websocket_connections:
                websocket = self.websocket_connections[connection_id]
                await websocket.close()
                del self.websocket_connections[connection_id]
                
            return {"success": True, "message": "视频播放已停止"}
            
        except Exception as e:
            self.logger.error(f"停止视频播放失败: {e}")
            raise HTTPException(status_code=500, detail=f"停止视频播放失败: {str(e)}")
    
    # =============================================================================
    # 5. 音频对讲API
    # =============================================================================
    
    async def start_audio_talk(self, sim: str, channel: int, audio_file: UploadFile = None, device_id: str = None) -> Dict:
        """开始音频对讲"""
        try:
            # 使用新的配置获取方法
            if device_id is None:
                device_id = sim  # 如果没有指定device_id，使用sim作为默认值

            # 获取服务器配置（包含token）
            server_config = await self.get_server_config(self.config.app_code, device_id)
            host = server_config["address"]
            port = int(server_config["port"])
            token = server_config["token"]

            ws_url = f"ws://{host}:{port}"
            self.logger.info(f"准备连接到: {ws_url}")

            # 构建对讲请求包（使用从配置获取的token）
            packet = self._build_final_packet(sim, channel, token, host, port)
            self.logger.info(f"发送 0x9101 数据包 (hex): {packet.hex(' ')}")

            websocket = await websockets.connect(ws_url, open_timeout=10, close_timeout=10)
            await websocket.send(packet)

            # 等待服务器确认
            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            self.logger.info(f"对讲请求确认: {response.hex(' ')}")

            if audio_file:
                # 处理音频文件并推流
                await self._stream_audio_file(websocket, sim, channel, audio_file)
                self.logger.info("音频推流完成")

            return {
                "success": True,
                "message": "音频对讲已开始",
                "connection_id": f"{sim}_{channel}_audio",
                "server_config": {
                    "host": host,
                    "port": port,
                    "ws_url": ws_url
                }
            }

        except asyncio.TimeoutError:
            self.logger.error("音频对讲超时")
            raise HTTPException(status_code=408, detail="发送请求后等待服务器响应超时，设备可能不在线")
        except Exception as e:
            self.logger.error(f"音频对讲失败: {e}")
            raise HTTPException(status_code=500, detail=f"音频对讲失败: {str(e)}")

    async def _stream_audio_file(self, websocket, sim: str, channel: int, audio_file: UploadFile):
        """推流音频文件"""
        try:
            # 读取并转换音频文件
            file_content = await audio_file.read()
            audio = AudioSegment.from_file(io.BytesIO(file_content))
            audio = audio.set_frame_rate(8000).set_channels(1).set_sample_width(2)
            pcm_data = audio.raw_data

            # 分包发送音频数据
            sequence = 1
            timestamp = 0
            chunk_size = 320  # 每包320字节

            for i in range(0, len(pcm_data), chunk_size):
                sequence += 1
                chunk = pcm_data[i:i+chunk_size]
                audio_packet = self._build_audio_packet(sim, channel, sequence, timestamp, chunk)
                await websocket.send(audio_packet)
                await asyncio.sleep(0.02)  # 20ms间隔
                timestamp += 20

            self.logger.info("音频推流完成")

        except Exception as e:
            self.logger.error(f"音频推流失败: {e}")
            raise

    # =============================================================================
    # 6. 设备控制API
    # =============================================================================

    async def control_device(self, request: DeviceControlRequest) -> Dict:
        """设备控制"""
        try:
            # 构建控制命令包
            control_packet = self._build_control_packet(
                request.sim, request.channel, request.command, request.param
            )

            # 发送控制命令
            ws_url = f"ws://{self.config.host}:{self.config.websocket_port}"
            async with websockets.connect(ws_url) as websocket:
                await websocket.send(control_packet)
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)

            return {
                "success": True,
                "message": "设备控制命令已发送",
                "response": response.hex()
            }

        except Exception as e:
            self.logger.error(f"设备控制失败: {e}")
            raise HTTPException(status_code=500, detail=f"设备控制失败: {str(e)}")

    async def query_video_files(self, sim: str, channel: int, start_time: str, end_time: str) -> List[Dict]:
        """查询录像文件列表"""
        try:
            # 构建查询包
            query_packet = self._build_file_query_packet(sim, channel, start_time, end_time)

            ws_url = f"ws://{self.config.host}:{self.config.websocket_port}"
            async with websockets.connect(ws_url) as websocket:
                await websocket.send(query_packet)
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)

                # 解析文件列表响应
                file_list = self._parse_file_list_response(response)

            return file_list

        except Exception as e:
            self.logger.error(f"查询录像文件失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询录像文件失败: {str(e)}")

    # =============================================================================
    # 7. 协议数据包构建方法
    # =============================================================================

    def _build_video_request_packet(self, sim: str, channel: int, stream_type: int, token_info: Dict) -> bytes:
        """构建视频请求数据包"""
        # 实现JT1078视频请求协议包
        # 这里使用现有的协议实现
        return self._build_jt1078_packet(0x9101, sim, channel, {
            "stream_type": stream_type,
            "token": token_info.get("token", "")
        })

    def _build_audio_talk_packet(self, sim: str, channel: int, token_info: Dict) -> bytes:
        """构建音频对讲请求包"""
        # 使用现有main.py中的实现
        token = token_info.get("token", "")
        host = self.config.host
        port = self.config.websocket_port

        return self._build_final_packet(sim, channel, token, host, port)

    def _build_audio_packet(self, sim: str, channel: int, sequence: int, timestamp: int, audio_data: bytes) -> bytes:
        """构建音频数据包"""
        codec_type_g711a = 6
        header = self._build_1078_rt_header(
            sim, channel, len(audio_data), sequence,
            has_timestamp=True, timestamp=timestamp, payload_type=codec_type_g711a
        )
        return header + audio_data

    def _build_control_packet(self, sim: str, channel: int, command: int, param: Dict = None) -> bytes:
        """构建设备控制包"""
        # 实现设备控制协议包
        return self._build_jt1078_packet(0x9102, sim, channel, {
            "command": command,
            "param": param or {}
        })

    def _build_file_query_packet(self, sim: str, channel: int, start_time: str, end_time: str) -> bytes:
        """构建文件查询包"""
        # 实现文件查询协议包
        return self._build_jt1078_packet(0x9205, sim, channel, {
            "start_time": start_time,
            "end_time": end_time
        })

    def _build_jt1078_packet(self, msg_id: int, sim: str, channel: int, data: Dict) -> bytes:
        """构建JT1078协议包的通用方法"""
        # 这里实现通用的JT1078协议包构建逻辑
        # 可以参考现有main.py中的实现
        packet = bytearray()

        # 添加协议头
        packet.extend(b'\x30\x31\x63\x64')  # 固定头
        packet.extend(msg_id.to_bytes(2, 'big'))
        packet.extend(bytes.fromhex(sim.zfill(12)))
        packet.append(channel)

        # 添加数据部分
        data_bytes = json.dumps(data).encode('utf-8')
        packet.extend(len(data_bytes).to_bytes(2, 'big'))
        packet.extend(data_bytes)

        return bytes(packet)

    # 从现有main.py复制的协议实现方法
    def _build_final_packet(self, sim: str, channel: int, token: str, server_ip: str, server_tcp_port: int) -> bytes:
        """构建最终的对讲请求包"""
        # 复用现有main.py中的实现
        msg_id_808 = 0x9101

        ip_bytes = server_ip.encode('ascii')
        body_808 = bytearray()
        body_808.append(len(ip_bytes))
        body_808.extend(ip_bytes)
        body_808.extend(server_tcp_port.to_bytes(2, 'big'))
        body_808.extend((0).to_bytes(2, 'big'))
        body_808.append(channel)
        body_808.append(2)  # 数据类型: 2=双向对讲
        body_808.append(1)  # 码流类型: 1=子码流

        sequence = 1
        header_808 = self._build_jt1078_header(sim, msg_id_808, len(body_808), sequence)
        packet_808 = header_808 + body_808

        # 计算校验码并转义
        checksum_808 = 0
        for byte in packet_808:
            checksum_808 ^= byte

        escaped_808 = self._escape_jt1078_data(packet_808)
        escaped_808.extend(self._escape_jt1078_data(checksum_808.to_bytes(1, 'big')))

        # 构建认证尾部
        auth_tail = bytearray()
        token_bytes = token.encode('utf-8')
        auth_tail.extend((0).to_bytes(2, 'big'))
        auth_tail.extend((0).to_bytes(2, 'big'))
        auth_tail.append(1)
        auth_tail.extend(len(token_bytes).to_bytes(2, 'big'))
        auth_tail.extend(token_bytes)

        # 构建最外层包头
        body_1078_len = 1 + len(escaped_808) + 1 + len(auth_tail)
        header_1078 = self._build_1078_rt_header(sim, channel, body_1078_len, sequence, has_timestamp=False)

        # 组装最终数据包
        final_packet = bytearray()
        final_packet.extend(header_1078)
        final_packet.extend(b'\x7e')
        final_packet.extend(escaped_808)
        final_packet.extend(b'\x7e')
        final_packet.extend(auth_tail)

        return bytes(final_packet)

    def _build_1078_rt_header(self, sim: str, channel: int, data_len: int, sequence: int,
                             has_timestamp: bool, timestamp: int = 0, payload_type: int = 0) -> bytearray:
        """构建1078实时数据包头"""
        header = bytearray(b'\x30\x31\x63\x64')
        header.append(0x81)
        header.append(payload_type)
        header.extend(sequence.to_bytes(2, 'big'))
        header.extend(bytes.fromhex(sim.zfill(12)))
        header.append(channel)

        if has_timestamp:
            header.append(0b00100000)
            header.extend(timestamp.to_bytes(8, 'big'))
            header.extend((0).to_bytes(2, 'big'))
            header.extend((0).to_bytes(2, 'big'))
            header.extend(data_len.to_bytes(2, 'big'))
        else:
            header.append(0x80)
            header.extend(data_len.to_bytes(2, 'big'))

        return header

    def _build_jt1078_header(self, sim: str, msg_id: int, body_length: int, sequence: int) -> bytearray:
        """构建JT1078消息头"""
        header = bytearray()
        header.extend(msg_id.to_bytes(2, 'big'))
        body_props = body_length & 0x3FF
        header.extend(body_props.to_bytes(2, 'big'))
        header.extend(bytes.fromhex(sim.zfill(12)))
        header.extend(sequence.to_bytes(2, 'big'))
        return header

    def _escape_jt1078_data(self, data: bytearray) -> bytearray:
        """JT1078数据转义"""
        escaped_data = bytearray()
        for byte in data:
            if byte == 0x7e:
                escaped_data.extend([0x7d, 0x02])
            elif byte == 0x7d:
                escaped_data.extend([0x7d, 0x01])
            else:
                escaped_data.append(byte)
        return escaped_data

    def _parse_file_list_response(self, response: bytes) -> List[Dict]:
        """解析文件列表响应"""
        # 这里需要根据具体的协议格式来解析响应数据
        # 目前返回示例数据，实际使用时需要根据协议解析response
        self.logger.debug(f"解析文件列表响应: {response.hex()}")

        return [
            {
                "filename": "20241201_120000.mp4",
                "start_time": "2024-12-01 12:00:00",
                "end_time": "2024-12-01 12:30:00",
                "file_size": 1024000,
                "file_type": 0
            }
        ]

# =============================================================================
# 8. FastAPI应用和路由
# =============================================================================

# 全局配置和客户端实例
config = RTVSConfig()
rtvs_client = None

@asynccontextmanager
async def lifespan(_):
    """应用生命周期管理"""
    global rtvs_client
    # 启动
    rtvs_client = RTVSClient(config)
    logging.basicConfig(level=logging.INFO)
    yield
    # 关闭
    if rtvs_client:
        await rtvs_client.close()

app = FastAPI(
    title="RTVS Python API",
    description="RTVS实时视频监控系统Python API接口",
    version="1.0.0",
    lifespan=lifespan
)

# =============================================================================
# 9. API路由定义
# =============================================================================

@app.get("/api/server/stats")
async def get_server_stats():
    """获取服务器统计信息"""
    try:
        stats = await rtvs_client.get_server_stats()
        return JSONResponse(content=asdict(stats))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/device/{sim}/stats")
async def get_device_stats(sim: str):
    """获取设备统计信息"""
    try:
        stats = await rtvs_client.get_device_stats(sim)
        return JSONResponse(content=stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/token")
async def get_token():
    """获取访问令牌"""
    try:
        token = await rtvs_client.get_token()
        return JSONResponse(content=token)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/video/play/start")
async def start_video_play(request: VideoPlayRequestModel):
    """开始视频播放"""
    try:
        video_request = VideoPlayRequest(
            sim=request.sim,
            channel=request.channel,
            stream_type=request.stream_type,
            play_type=request.play_type,
            start_time=request.start_time,
            end_time=request.end_time
        )
        result = await rtvs_client.start_video_play(video_request)
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/video/play/stop/{connection_id}")
async def stop_video_play(connection_id: str):
    """停止视频播放"""
    try:
        result = await rtvs_client.stop_video_play(connection_id)
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/audio/talk/start")
async def start_audio_talk(
    sim: str = Form(...),
    channel: int = Form(...),
    device_id: str = Form(None),
    audio_file: UploadFile = File(None)
):
    """开始音频对讲"""
    try:
        result = await rtvs_client.start_audio_talk(sim, channel, audio_file, device_id)
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/device/control")
async def control_device(request: DeviceControlRequest):
    """设备控制"""
    try:
        result = await rtvs_client.control_device(request)
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/video/files/{sim}/{channel}")
async def query_video_files(sim: str, channel: int, start_time: str, end_time: str):
    """查询录像文件列表"""
    try:
        files = await rtvs_client.query_video_files(sim, channel, start_time, end_time)
        return JSONResponse(content={"files": files})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# =============================================================================
# 10. 健康检查和工具API
# =============================================================================

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc).isoformat()}

@app.get("/api/config")
async def get_config():
    """获取当前配置"""
    return JSONResponse(content=asdict(config))

@app.post("/api/config")
async def update_config(new_config: dict):
    """更新配置"""
    global config, rtvs_client
    try:
        # 更新配置
        for key, value in new_config.items():
            if hasattr(config, key):
                setattr(config, key, value)

        # 重新创建客户端
        if rtvs_client:
            await rtvs_client.close()
        rtvs_client = RTVSClient(config)

        return {"success": True, "message": "配置已更新"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# =============================================================================
# 11. 主函数和启动脚本
# =============================================================================

if __name__ == "__main__":
    import uvicorn

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 启动服务
    uvicorn.run(
        "rtvs_python_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
