# RTVS 语音对讲 API

专注于语音对讲功能的简化版本，移除了视频播放、设备管理等其他功能。

## 功能特性

### 🎤 语音对讲
- 音频文件上传和推流
- 自动token获取和管理
- 支持多种音频格式 (MP3, WAV, M4A等)
- G.711A编码支持
- JT/T 1078协议支持

## 快速开始

### 1. 环境准备

```bash
# 安装Python 3.8+
python --version

# 安装依赖
pip install -r audio_requirements.txt

# 安装FFmpeg (音频处理需要)
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# Windows
# 从 https://ffmpeg.org/download.html 下载并添加到PATH
```

### 2. 配置设置

编辑 `audio_config.yaml` 文件，设置您的配置信息：

```yaml
# 基本配置
app_code: "SV_CWTPRO"
config_url: "http://************/jt-video/video/websocket/address"

# 默认设备配置
default_device:
  sim: "13800138000"         # 您的设备SIM卡号
  channel: 1                 # 通道号
  device_id: "13896118973"   # 设备ID
```

### 3. 启动服务

```bash
# 启动语音对讲API服务
python audio_talk_api.py

# 服务将在 http://127.0.0.1:8000 启动
```

### 4. 推流音频文件

#### 方式1：交互式推流（推荐）

```bash
python simple_audio_push.py
```

这个脚本会：
- 自动查找当前目录中的音频文件
- 让您选择要推流的文件
- 输入设备信息（或使用默认值）
- 开始推流

#### 方式2：命令行推流

```bash
# 使用默认设备信息
python simple_audio_push.py your_audio.mp3

# 指定完整参数
python simple_audio_push.py your_audio.mp3 13800138000 1 13896118973
```

#### 方式3：使用curl

```bash
curl -X POST http://127.0.0.1:8000/api/audio/talk \
  -F "sim=13800138000" \
  -F "channel=1" \
  -F "device_id=13896118973" \
  -F "audio_file=@your_audio.mp3"
```

## API接口说明

### 语音对讲

```bash
POST /api/audio/talk
Content-Type: multipart/form-data

参数:
- sim: 设备SIM卡号 (必需)
- channel: 通道号 (必需)
- device_id: 设备ID (可选，默认使用sim)
- audio_file: 音频文件 (必需)
```

### 健康检查

```bash
GET /health
```

### 配置管理

```bash
# 获取配置
GET /api/config

# 更新配置
POST /api/config
{
  "app_code": "SV_CWTPRO",
  "timeout": 10
}
```

## 工作流程

1. **客户端调用API**
   ```
   POST /api/audio/talk
   - 上传音频文件
   - 提供设备信息
   ```

2. **自动获取配置**
   ```
   GET http://************/jt-video/video/websocket/address
   - 使用app_code和device_id
   - 获取服务器地址、端口和token
   ```

3. **建立WebSocket连接**
   ```
   - 使用获取的服务器信息
   - 发送JT/T 1078对讲请求包
   - 包含token认证信息
   ```

4. **推流音频数据**
   ```
   - 转换音频格式 (8000Hz, 单声道, 16bit)
   - 分包发送音频数据
   - G.711A编码
   ```

## 使用示例

### Python客户端示例

```python
import asyncio
import httpx

async def send_audio():
    async with httpx.AsyncClient() as client:
        with open("test.mp3", "rb") as audio_file:
            files = {"audio_file": ("test.mp3", audio_file, "audio/mpeg")}
            data = {
                "sim": "13800138000",
                "channel": 1,
                "device_id": "13896118973"
            }
            
            response = await client.post(
                "http://127.0.0.1:8000/api/audio/talk",
                files=files,
                data=data
            )
            
            print(response.json())

asyncio.run(send_audio())
```

## 支持的音频格式

- **MP3** - 最常用格式
- **WAV** - 无损格式
- **M4A** - Apple格式
- **其他** - FFmpeg支持的格式

## 故障排除

### 常见问题

1. **连接超时**
   - 检查配置服务器地址是否正确
   - 确认网络连接正常

2. **音频推流失败**
   - 确认FFmpeg正确安装
   - 检查音频文件格式是否支持

3. **设备无响应**
   - 确认设备在线
   - 检查SIM卡号和设备ID是否正确

### 调试方法

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 部署建议

### Docker部署

```dockerfile
FROM python:3.11-slim

# 安装FFmpeg
RUN apt-get update && apt-get install -y ffmpeg

WORKDIR /app
COPY audio_requirements.txt .
RUN pip install -r audio_requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "audio_talk_api.py"]
```

### 生产环境

```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn audio_talk_api:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 注意事项

1. **音频质量**: 系统会自动转换为8000Hz单声道格式
2. **文件大小**: 建议单个音频文件不超过10MB
3. **网络稳定**: 确保网络连接稳定，避免推流中断
4. **设备状态**: 确认目标设备在线且支持语音对讲

## 许可证

本项目遵循与原RTVS项目相同的许可证。
