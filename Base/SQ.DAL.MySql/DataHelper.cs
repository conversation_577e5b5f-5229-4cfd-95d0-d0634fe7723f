﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

using SQ.Base;
using System.Threading.Tasks;
using MySqlConnector;

namespace SQ.DAL.MySql
{
    public class DataHelper
    {
        #region 查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="SIndex">查询开始索引</param>
        /// <param name="PageSize">页大小</param>
        /// <param name="Where">where条件</param>
        /// <param name="TableName">表名</param>
        /// <param name="OrderField">排序字段</param>
        /// <param name="SelectField">查询字段</param>
        /// <param name="count">总记录数</param>
        /// <returns></returns>
        public static MyDataReader SelectPagination(ConnecterManager cma, int SIndex, int PageSize, string Where, string TableName, string OrderField, string SelectField, out int count)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                cmd.CommandText = "select count(*) from " + TableName + " where 1=1 " + Where;
                cmd.Connection = cma.GetConnection();
                count = Convert.ToInt32(cmd.ExecuteScalar().ToString());

                cmd.CommandText = string.Format(@"SELECT {0} FROM {1} WHERE 1=1 {2} ORDER BY {3} LIMIT {4},{5}"
                    , SelectField, TableName, Where, OrderField, SIndex, PageSize);
                return new MyDataReader(cmd.ExecuteReader());
            }
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="SIndex">查询开始索引</param>
        /// <param name="PageSize">页大小</param>
        /// <param name="Where">where条件</param>
        /// <param name="TableName">表名</param>
        /// <param name="OrderField">排序字段</param>
        /// <param name="SelectField">查询字段</param>
        /// <returns></returns>
        public static async Task<Tuple<MyDataReader, int>> SelectPaginationAsync(ConnecterManager cma, int SIndex, int PageSize, string Where, string TableName, string OrderField, string SelectField)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                cmd.CommandText = "select count(*) from " + TableName + " where 1=1 " + Where;
                cmd.Connection = await cma.GetConnectionAsync();
                int count = Convert.ToInt32((await cmd.ExecuteScalarAsync()).ToString());

                cmd.CommandText = string.Format(@"SELECT {0} FROM {1} WHERE 1=1 {2} ORDER BY {3} LIMIT {4},{5}"
                    , SelectField, TableName, Where, OrderField, SIndex, PageSize);
                return new Tuple<MyDataReader, int>(new MyDataReader(await cmd.ExecuteReaderAsync()), count);
            }
        }


        /// <summary>
        /// 查询数据 按存储过程查询
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">存储过程名</param>
        /// <param name="args">参数列表</param>
        /// <returns></returns>
        public static MyDataReader SelectDataByStoredProcedure(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return SelectData(CommandType.StoredProcedure, cma, sql, parms);
        }


        /// <summary>
        /// 查询数据 按存储过程查询
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">存储过程名</param>
        /// <param name="args">参数列表</param>
        /// <returns></returns>
        public static Task<MyDataReader> SelectDataByStoredProcedureAsync(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return SelectDataAsync(CommandType.StoredProcedure, cma, sql, parms);
        }
        /// <summary>
        /// 查询数据 按SQL查询
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL</param>
        /// <param name="args">参数列表</param>
        /// <returns></returns>
        public static MyDataReader SelectData(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return SelectData(CommandType.Text, cma, sql, parms);
        }
        /// <summary>
        /// 查询数据 按SQL查询
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL</param>
        /// <param name="args">参数列表</param>
        /// <returns></returns>
        public static Task<MyDataReader> SelectDataAsync(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return SelectDataAsync(CommandType.Text, cma, sql, parms);
        }
        /// <summary>
        /// 查询数据
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL</param>
        /// <param name="args">参数列表</param>
        /// <returns></returns>
        public static MyDataReader SelectData(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = cma.GetConnection();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                return new MyDataReader(cmd.ExecuteReader());
            }
        }
        /// <summary>
        /// 查询数据
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL</param>
        /// <param name="args">参数列表</param>
        /// <returns></returns>
        public static async Task<MyDataReader> SelectDataAsync(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = await cma.GetConnectionAsync();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                return new MyDataReader(await cmd.ExecuteReaderAsync());
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmbType"></param>
        /// <param name="cma"></param>
        /// <param name="sql"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static DataTable SelectDataTable(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {

            using (MySqlCommand cmd = new MySqlCommand())
            {
                DataTable dt = new DataTable();
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = cma.GetConnection();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                using (MySqlDataAdapter adp = new MySqlDataAdapter(cmd))
                {
                    adp.Fill(dt);
                    return dt;
                }
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cmbType"></param>
        /// <param name="cma"></param>
        /// <param name="sql"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static async Task<DataTable> SelectDataTableAsync(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {

            using (MySqlCommand cmd = new MySqlCommand())
            {
                DataTable dt = new DataTable();
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = await cma.GetConnectionAsync();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                using (MySqlDataAdapter adp = new MySqlDataAdapter(cmd))
                {
                    adp.Fill(dt);
                    return dt;
                }
            }
        }
        public static DataSet SelectDataSet(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                DataSet ds = new DataSet();
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = cma.GetConnection();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                using (MySqlDataAdapter adp = new MySqlDataAdapter(cmd))
                {
                    adp.Fill(ds);
                    return ds;
                }
            }
        }

        public static async Task<DataSet> SelectDataSetAsync(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                DataSet ds = new DataSet();
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = await cma.GetConnectionAsync();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                using (MySqlDataAdapter adp = new MySqlDataAdapter(cmd))
                {
                    adp.Fill(ds);
                    return ds;
                }
            }
        }
        #endregion

        #region Exec
        /// <summary>
        /// 执行存储过程
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">存储过程名</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static int ExecuteStoredProcedure(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteStoredProcedure(cma, sql, parms, null);
        }
        /// <summary>
        /// 执行存储过程
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">存储过程名</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static Task<int> ExecuteStoredProcedureAsync(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteStoredProcedureAsync(cma, sql, parms, null);
        }
        /// <summary>
        /// 执行存储过程
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">存储过程名</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        /// <returns></returns>
        public static int ExecuteStoredProcedure(ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Cpars)
        {
            return ExecuteNonQuery(CommandType.StoredProcedure, cma, sql, parms, Cpars);
        }
        /// <summary>
        /// 执行存储过程
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">存储过程名</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        /// <returns></returns>
        public static Task<int> ExecuteStoredProcedureAsync(ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Cpars)
        {
            return ExecuteNonQueryAsync(CommandType.StoredProcedure, cma, sql, parms, Cpars);
        }
        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static int ExecuteSQL(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteSQL(cma, sql, parms, Opars: null);
        }
        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static Task<int> ExecuteSQLAsync(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteSQLAsync(cma, sql, parms, Opars: null);
        }
        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        /// <returns></returns>
        public static int ExecuteSQL(ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Cpars)
        {
            return ExecuteNonQuery(CommandType.Text, cma, sql, parms, Cpars);
        }
        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        /// <returns></returns>
        public static Task<int> ExecuteSQLAsync(ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Cpars)
        {
            return ExecuteNonQueryAsync(CommandType.Text, cma, sql, parms, Cpars);
        }
        public static int ExecuteSQL(ConnecterManager cma, string sql, NameAndValueList parms, IEnumerable<MySqlParameter> Opars)
        {
            return ExecuteNonQueryByCpars(CommandType.Text, cma, sql, parms, Opars);
        }
        public static Task<int> ExecuteSQLAsync(ConnecterManager cma, string sql, NameAndValueList parms, IEnumerable<MySqlParameter> Opars)
        {
            return ExecuteNonQueryByCparsAsync(CommandType.Text, cma, sql, parms, Opars);
        }
        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static int ExecuteNonQuery(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteNonQuery(cmbType, cma, sql, parms, null);
            //using (MySqlCommand cmd = new MySqlCommand())
            //{
            //    cmd.CommandText = sql;
            //    cmd.CommandType = cmbType;
            //    cmd.Connection = cma.GetConnection();
            //    if (args != null)
            //    {
            //        foreach (var item in args)
            //        {
            //            cmd.Parameters.AddWithValue(item.Name, item.Value);
            //        }
            //    }
            //    return cmd.ExecuteNonQuery();
            //}
        }
        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static Task<int> ExecuteNonQueryAsync(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteNonQueryAsync(cmbType, cma, sql, parms, null);
        }
        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Outpars">Output参数</param>
        /// <returns></returns>
        public static int ExecuteNonQuery(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Outpars)
        {
            if (Outpars != null)
            {
                Dictionary<string, MySqlParameter> dit = new Dictionary<string, MySqlParameter>(Outpars.Count);
                foreach (var item in Outpars)
                {
                    var para = new MySqlParameter();
                    para.ParameterName = item.Name;
                    para.Direction = ParameterDirection.Output;
                    dit[item.Name] = para;
                }
                int ret = ExecuteNonQueryByCpars(cmbType, cma, sql, parms, dit.Values);
                foreach (var item in Outpars)
                {
                    item.Value = dit[item.Name].Value;
                }
                return ret;
            }
            else
            {
                return ExecuteNonQueryByCpars(cmbType, cma, sql, parms, null);
            }
        }
        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Outpars">Output参数</param>
        /// <returns></returns>
        public static async Task<int> ExecuteNonQueryAsync(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Outpars)
        {
            if (Outpars != null)
            {
                Dictionary<string, MySqlParameter> dit = new Dictionary<string, MySqlParameter>(Outpars.Count);
                foreach (var item in Outpars)
                {
                    var para = new MySqlParameter();
                    para.ParameterName = item.Name;
                    para.Direction = ParameterDirection.Output;
                    dit[item.Name] = para;
                }
                int ret = await ExecuteNonQueryByCparsAsync(cmbType, cma, sql, parms, dit.Values);
                foreach (var item in Outpars)
                {
                    item.Value = dit[item.Name].Value;
                }
                return ret;
            }
            else
            {
                return await ExecuteNonQueryByCparsAsync(cmbType, cma, sql, parms, null);
            }
        }


        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        public static int ExecuteNonQueryByCpars(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms, IEnumerable<MySqlParameter> Cpars)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = cma.GetConnection();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                if (Cpars != null)
                {
                    foreach (var item in Cpars)
                    {
                        cmd.Parameters.Add(item);
                    }
                }
                return cmd.ExecuteNonQuery();
            }
        }


        /// <summary>
        /// 执行SQL
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        public static async Task<int> ExecuteNonQueryByCparsAsync(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms, IEnumerable<MySqlParameter> Cpars)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = await cma.GetConnectionAsync();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                if (Cpars != null)
                {
                    foreach (var item in Cpars)
                    {
                        cmd.Parameters.Add(item);
                    }
                }
                return await cmd.ExecuteNonQueryAsync();
            }
        }

        /// <summary>
        /// 执行SQL返回第一行第一列
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static object ExecuteScalar(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteScalar(cma, sql, parms, null);
        }
        /// <summary>
        /// 执行SQL返回第一行第一列
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static Task<object> ExecuteScalarAsync(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteScalarAsync(cma, sql, parms, null);
        }
        /// <summary>
        /// 执行SQL返回第一行第一列
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        /// <returns></returns>
        public static object ExecuteScalar(ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Cpars)
        {
            return ExecuteScalar(CommandType.Text, cma, sql, parms, Cpars);
        }
        /// <summary>
        /// 执行SQL返回第一行第一列
        /// </summary>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        /// <returns></returns>
        public static Task<object> ExecuteScalarAsync(ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Cpars)
        {
            return ExecuteScalarAsync(CommandType.Text, cma, sql, parms, Cpars);
        }
        /// <summary>
        /// 执行SQL返回第一行第一列
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static object ExecuteScalar(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteScalar(cmbType, cma, sql, parms, null);
        }
        /// <summary>
        /// 执行SQL返回第一行第一列
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <returns></returns>
        public static Task<object> ExecuteScalarAsync(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms)
        {
            return ExecuteScalarAsync(cmbType, cma, sql, parms, null);
        }
        /// <summary>
        /// 执行SQL返回第一行第一列
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        /// <returns></returns>
        public static object ExecuteScalar(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Cpars)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = cma.GetConnection();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                if (Cpars != null)
                {
                    foreach (var item in Cpars)
                    {
                        var para = new MySqlParameter();
                        para.ParameterName = item.Name;
                        para.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(para);
                    }
                }
                return cmd.ExecuteScalar();
            }
        }
        /// <summary>
        /// 执行SQL返回第一行第一列
        /// </summary>
        /// <param name="cmbType">如何解释命令字符串</param>
        /// <param name="cma">连接控制类</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="args">参数</param>
        /// <param name="Cpars">参数(可定义可返回参数)</param>
        /// <returns></returns>
        public static async Task<object> ExecuteScalarAsync(CommandType cmbType, ConnecterManager cma, string sql, NameAndValueList parms, NameAndValueList Cpars)
        {
            using (MySqlCommand cmd = new MySqlCommand())
            {
                cmd.CommandText = sql;
                cmd.CommandType = cmbType;
                cmd.Connection = await cma.GetConnectionAsync();
                if (parms != null)
                {
                    foreach (var item in parms)
                    {
                        cmd.Parameters.Add(new MySqlParameter(item.Name, item.Value));
                    }
                }
                if (Cpars != null)
                {
                    foreach (var item in Cpars)
                    {
                        var para = new MySqlParameter();
                        para.ParameterName = item.Name;
                        para.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(para);
                    }
                }
                return await cmd.ExecuteScalarAsync();
            }
        }
        #endregion
    }
}
