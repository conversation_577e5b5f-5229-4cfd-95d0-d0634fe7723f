﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Configuration;
using System.Threading.Tasks;

namespace SQ.DAL.MySql
{
    internal class Connecter
    {
        static Connecter()
        {
            //设置默认值
            MaxConnCount = 200;
            Overtime = 10000;
        }
        #region 属性、私有字段
        /// <summary>
        /// 最大连接数
        /// </summary>
        public static int MaxConnCount { get; set; }
        /// <summary>
        /// 超时时间(单位 毫秒)
        /// </summary>
        public static int Overtime { get; set; }
        /// <summary>
        /// 数据库连接池
        /// </summary>
        private static List<MyConnection> conns = new List<MyConnection>();
        /// <summary>
        /// 锁定对象
        /// </summary>
        private static object mLock = new object();
        #endregion

        #region 方法
        /// <summary>
        /// 获取指定连接对象
        /// </summary>
        /// <returns></returns>
        public static MyConnection GetConnection(Guid id)
        {
            var conn = conns.Find(p => p.ID == id);
            if (conn == null)
                throw new Exception("不存在此连接");
            else
            {
                conn.IsUsed = true;
                if (conn != null && conn.Conn.State != ConnectionState.Open)
                    conn.Conn.Open();
            }
            return conn;
        }
        public static async Task<MyConnection> GetConnectionAsync(Guid id)
        {
            var conn = conns.Find(p => p.ID == id);
            if (conn == null)
                throw new Exception("不存在此连接");
            else
            {
                conn.IsUsed = true;
                if (conn != null && conn.Conn.State != ConnectionState.Open)
                    await conn.Conn.OpenAsync();
            }
            return conn;
        }

        /// <summary>
        /// 创建指定连接对象
        /// </summary>
        /// <returns></returns>
        public static MyConnection CreateConnection(Guid id, string ConnectionName)
        {
            if (conns.Exists(p => p.ID == id &&
             p.IsUsed
            //p.Conn.State == ConnectionState.Open
            ))
                throw new Exception("已存在此连接");

            var conn = GetUsableConnAndCheck(ConnectionName);
            conn.ID = id;
            return conn;
        }
        public static async Task<MyConnection> CreateConnectionAsync(Guid id, string ConnectionName)
        {
            if (conns.Exists(p => p.ID == id &&
             p.IsUsed
            //p.Conn.State == ConnectionState.Open
            ))
                throw new Exception("已存在此连接");

            var conn = await GetUsableConnAndCheckAsync(ConnectionName);
            conn.ID = id;
            return conn;
        }
        /// <summary>
        /// 关闭连接对象
        /// </summary>
        /// <returns></returns>
        public static void CloseConnection(Guid id)
        {
            var conn = conns.Find(p => p.ID == id);
            if (conn == null)
            {
                SQ.Base.Log.WriteLog4("不存在此连接[CloseConnection]", SQ.Base.LOGTYPE.WARN);
                return;
                //throw new Exception("不存在此连接");
            }
            if (conn.Conn.State != ConnectionState.Closed)
            {
                conn.Conn.Close();
            }
            conn.IsUsed = false;
        }

        /// <summary>
        /// 获取可用连接 无可用连接时等待直到超时
        /// </summary>
        /// <returns></returns>
        public static MyConnection GetUsableConnAndCheck(string ConnectionName)
        {
            var conn = GetUsableConn(ConnectionName);
            int i = 0;
            while (conn == null)
            {

                //无可用连接时 判断是否超时
                if (Overtime < (i * 200))
                    throw new Exception("服务器正忙，请稍候重试。");
                //暂停200毫秒后再次获取
                System.Threading.Thread.Sleep(200);
                i++;
                conn = GetUsableConn(ConnectionName);
            }
            return conn;

        }
        public static async Task<MyConnection> GetUsableConnAndCheckAsync(string ConnectionName)
        {
            var conn = await GetUsableConnAsync(ConnectionName);
            int i = 0;
            while (conn == null)
            {
                //无可用连接时 判断是否超时
                if (Overtime < (i * 200))
                    throw new Exception("服务器正忙，请稍候重试。");
                //暂停200毫秒后再次获取
                await Task.Delay(200);
                i++;
                conn = await GetUsableConnAsync(ConnectionName);
            }
            return conn;

        }
        /// <summary>
        /// 获取可用连接 无可用连接时返回NULL
        /// </summary>
        /// <returns></returns>
        private static MyConnection GetUsableConn(string ConnectionName)
        {
            lock (mLock)
            {
                var connstr = GetConnectionString(ConnectionName);
                //var conn = conns.Find(p => p.Conn.State == ConnectionState.Closed);
                //2018-8-20 wanjj修改 从连接池挑可用连接改为自定义状态判断 根据连接状态判断在非主动关闭连接情况下可能导致未找到此连接警告
                var conn = conns.Find(p => p.IsUsed == false && p.ConnectionString == connstr);

                if (conn != null)
                {
                    conn.IsUsed = true;
                    conn.Conn.Open();
                    return conn;
                }
                else if (conns.Count < MaxConnCount)//当无可用连接且连接数未超过最大连接数时 新创建一个连接
                {
                    try
                    {
                        conn = new MyConnection(connstr);
                        conn.IsUsed = true;
                        conn.Conn.Open();
                        conns.Add(conn);
                        return conn;
                    }
                    catch (Exception ex)
                    {
                        SQ.Base.ErrorLog.WriteLog4Ex("创建连接异常", ex);
                        conn = null;
                        return null;
                    }
                }
            }
            return null;
        }

        private static async Task<MyConnection> GetUsableConnAsync(string ConnectionName)
        {
            MyConnection conn;
            lock (mLock)
            {
                var connstr = GetConnectionString(ConnectionName);
                //var conn = conns.Find(p => p.Conn.State == ConnectionState.Closed);
                //2018-8-20 wanjj修改 从连接池挑可用连接改为自定义状态判断 根据连接状态判断在非主动关闭连接情况下可能导致未找到此连接警告
                conn = conns.Find(p => p.IsUsed == false && p.ConnectionString == connstr);

                if (conn != null)
                {
                    conn.IsUsed = true;
                }
                else if (conns.Count < MaxConnCount)//当无可用连接且连接数未超过最大连接数时 新创建一个连接
                {
                    conn = new MyConnection(connstr);
                    conn.IsUsed = true;
                    conns.Add(conn);
                }
            }

            try
            {
                await conn?.Conn.OpenAsync();
            }
            catch (Exception ex)
            {
                SQ.Base.ErrorLog.WriteLog4Ex("创建连接异常", ex);
                conn.IsUsed = false;
                conn = null;
            }
            return conn;
        }
        /// <summary>
        /// 获取数据库连接字串
        /// </summary>
        /// <param name="ConnectionName">连接名</param>
        /// <returns></returns>
        private static string GetConnectionString(string ConnectionName)
        {
            //            //"connectionString" 
            //            if (ConstantInfo.ConnectionStrings.ContainsKey(ConnectionName))
            //            {
            //                return ConstantInfo.ConnectionStrings[ConnectionName];
            //            }
            //            else
            //            {
            //#if NETSTANDARD  || NET5_0_OR_GREATER  || NETCOREAPP
            //                return ConfigurationManager.ConnectionStrings[ConnectionName].ConnectionString;
            //#else
            //                return "";
            //#endif
            //            }

            // 直接读取配置文件值
            return DBConfigHelp.MysqlConnectionString;
        }
        #endregion
    }
}
