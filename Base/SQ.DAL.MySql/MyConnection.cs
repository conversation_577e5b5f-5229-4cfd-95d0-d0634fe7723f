﻿using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace SQ.DAL.MySql
{
    public class MyConnection
    {
        public MyConnection(string ConnectionString)
            : this(ConnectionString, Guid.NewGuid())
        {
        }
        public MyConnection(string ConnectionString, Guid id)
        {
            Conn = new MySqlConnection(ConnectionString);
            this.ConnectionString = ConnectionString;
            ID = id;
            IsUsed = false;
        }
        /// <summary>
        /// 连接
        /// </summary>
        public MySqlConnection Conn { get; set; }
        /// <summary>
        /// 连接ID
        /// </summary>
        public Guid ID { get; set; }
        /// <summary>
        /// 指示当前连接是否正在使用
        /// </summary>
        public bool IsUsed { get; set; }
        /// <summary>
        /// 连接字符串
        /// </summary>
        public string ConnectionString { get; protected set; }
    }
}
