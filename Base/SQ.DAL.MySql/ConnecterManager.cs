﻿using MySqlConnector;
using SQ.Base.DAL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SQ.DAL.MySql
{
    [Serializable]
    public class ConnecterManager : BaseConnecterManager
    {
        public ConnecterManager()
            : this("connectionString")
        {

        }
        public ConnecterManager(string ConnectionName)
        {
            this.ConnectionName = ConnectionName;
        }
        #region 连接相关
        #region 属性
        private Guid? m_ConnID;
        /// <summary>
        /// 连接ID
        /// </summary>
        public Guid? ConnID
        {
            get { return m_ConnID; }
            set
            {
                IsCreateByThis = false;
                m_ConnID = value;
            }
        }
        /// <summary>
        /// 是否自治(仅获取)
        /// </summary>
        public bool IsCreateByThis { get; private set; }
        /// <summary>
        /// 连接名称
        /// </summary>
        public string ConnectionName { get; set; }
        #endregion

        #region 方法
        /// <summary>
        /// 开启当前连接(ConnID未赋值时自治)
        /// </summary>
        public void OpenConnection()
        {
            GetConnection();
        }
        /// <summary>
        /// 获取连接(ConnID未赋值时自治)
        /// </summary>
        /// <returns></returns>
        public  MySqlConnection GetConnection()
        {
            if (ConnID.HasValue)
            {
                //当前ID不为空时 重新开启此连接
                return Connecter.GetConnection(ConnID.Value).Conn;
            }
            else
            {
                //当前ID为空时 创建一个连接
                ConnID = Guid.NewGuid();
                IsCreateByThis = true;
                return Connecter.CreateConnection(ConnID.Value, ConnectionName).Conn;
            }
        }
        public async Task<MySqlConnection> GetConnectionAsync()
        {
            if (ConnID.HasValue)
            {
                //当前ID不为空时 重新开启此连接
                return (await Connecter.GetConnectionAsync(ConnID.Value)).Conn;
            }
            else
            {
                //当前ID为空时 创建一个连接
                ConnID = Guid.NewGuid();
                IsCreateByThis = true;
                return Connecter.CreateConnection(ConnID.Value, ConnectionName).Conn;
            }
        }
        /// <summary>
        /// 结束当前连接(仅自治时有效 非自治时请在最外层手动结束)
        /// </summary>
        public void CloseConnection()
        {
            if (IsCreateByThis && ConnID.HasValue)
            {
                var id = ConnID.Value;
                ConnID = null;
                CloseConnection(id);
            }
        }

        public override void Dispose()
        {
            CloseConnection();
        }
        /// <summary>
        /// 创建指定连接
        /// </summary>
        /// <param name="TransID">连接ID</param>
        public static ConnecterManager CreateAndOpenConnection(Guid ConnID, string ConnectionName)
        {
            Connecter.CreateConnection(ConnID, ConnectionName);
            var manager = new ConnecterManager();
            manager.m_ConnID = ConnID;
            manager.ConnectionName = ConnectionName;
            manager.IsCreateByThis = true;
            return manager;
        }
        /// <summary>
        /// 创建新连接
        /// </summary>
        /// <returns></returns>
        public static ConnecterManager CreateAndOpenConnection(string ConnectionName)
        {
            return CreateAndOpenConnection(Guid.NewGuid(), ConnectionName);
        }

        /// <summary>
        /// 结束指定连接
        /// </summary>
        /// <param name="ConnID">连接ID</param>
        public static void CloseConnection(Guid ConnID)
        {
            Connecter.CloseConnection(ConnID);
        }

        public override bool AutoOpenConnection()
        {
            return GetConnection() != null;
        }


        #endregion
        #endregion

    }
}