﻿using SQ.Base;
using System;
using System.Collections.Generic;
using System.Text;

namespace SQ.DAL.MySql
{
    public static class DBConfigHelp
    {
        #region 读配置文件
        public class ConfigModel
        {
            public string MysqlConnectionString { get; set; }
        }
        private static ConfigModel Config = null;

        public static string MysqlConnectionString         //= ConfigurationManager.ConnectionStrings["MysqlConnectionString"].ConnectionString;
        {
            get
            {
                if (Config == null)
                {
                    Config = SerializableHelper.DeserializeSetting<ConfigModel>(FileHelp.GetMyConfPath() + "SettingConfig.xml");
                }

                if (Config != null)
                {
                    return Config.MysqlConnectionString;
                }
                return null;
            }
        }
        #endregion

    }
}