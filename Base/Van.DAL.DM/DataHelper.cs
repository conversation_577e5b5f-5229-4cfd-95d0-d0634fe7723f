﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

using SQ.Base;
using System.Threading.Tasks;
using Dm;

namespace Van.DAL.DM
{
    public class DataHelper
    {
        public static DataTable SelectDataTable(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            if (parms != null && parms.Count > 0)
            {
                var dps = CovPars(ref sql, parms);
                return cma.DbClient.SelectDataTable(sql, dps);
            }
            return cma.DbClient.SelectDataTable(sql);
        }
        public static int ExecuteSQL(ConnecterManager cma, string sql, NameAndValueList parms)
        {
            if (parms != null && parms.Count > 0)
            {
                DmParameter[] dps = CovPars(ref sql, parms);
                return cma.DbClient.ExecuteSql(sql, dps);
            }
            return cma.DbClient.ExecuteSql(sql);
        }
        private static DmParameter[] CovPars(ref string sql, NameAndValueList parms)
        {
            sql = sql.Replace("@", ":");
            var dps = new DmParameter[parms.Count];
            for (int i = 0; i < parms.Count; i++)
            {
                dps[i] = new DmParameter(parms[i].Name.Replace("@", ":"), parms[i].Value);
            }

            return dps;
        }
    }
}