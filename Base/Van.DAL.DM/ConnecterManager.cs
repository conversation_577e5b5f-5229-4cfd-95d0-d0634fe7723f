﻿using SQ.Base.DAL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Van.DAL.DM
{
    [Serializable]
    public class ConnecterManager : BaseConnecterManager
    {
        internal DmDbClient DbClient { get; private set; }

        public ConnecterManager(string connectionString)
        {
            DbClient = new DmDbClient(connectionString);
            //= new DmDbClient("Server=192.168.1.238:20236; UserId=SYSDBA; PWD=*********; schema=FILECACHE");
        }

        public override bool AutoOpenConnection()
        {
            return true;
        }

        public override void Dispose()
        {
        }
    }
}