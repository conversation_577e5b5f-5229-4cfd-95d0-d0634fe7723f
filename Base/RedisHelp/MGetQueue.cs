﻿using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Text;

namespace RedisHelp
{
    /// <summary>
    /// 可多次消费队列
    /// 传统队列仅支持消费一次即移除
    /// 此队列可多次获取，仅过期后才会移除
    /// 由redis的sorted set实现，score为添加时的时间戳
    /// </summary>
    public class MGetQueue
    {
        public MGetQueue()
                : this(null)
        {
        }

        public MGetQueue(string readWriteHosts)
        {
            this.readWriteHosts = readWriteHosts;

            ClearLen = 500;
        }

        private string readWriteHosts;
        /// <summary>
        /// 队列超多少之后开始自动清理(清理在添加时调用 或主动调用)
        /// </summary>
        public long ClearLen { get; set; }
        private ConnectionMultiplexer _conn;

        protected IDatabase GetDatabase()
        {
            if (_conn == null)
            {
                _conn =
                        string.IsNullOrWhiteSpace(readWriteHosts) ?
                        RedisConnectionHelp.Instance :
                        RedisConnectionHelp.GetConnectionMultiplexer(readWriteHosts);
            }
            return _conn.GetDatabase();

        }

        /// <summary>
        /// 从队列获取(>tk之后的数据)
        /// </summary>
        /// <param name="QueueName">队列名</param>
        /// <param name="tk">时间戳</param>
        /// <param name="count">数量</param>
        /// <returns></returns>
        public List<string> GetList(string QueueName, ref long tk, int count)
        {
            var db = GetDatabase();
            bool canGetKeys = true;
            List<string> lst;
            if (count > 0)
            {
                lst = new List<string>(count);
            }
            else
            {
                lst = new List<string>();
            }
            double tk_Start = tk;
            while (canGetKeys)
            {
                var keys = db.SortedSetRangeByScoreWithScores(QueueName, tk_Start, exclude: Exclude.Start, take: 10);
                //判断之后是否还有数据
                canGetKeys = keys.Length == 10;
                //待移除列表 批量操作效率更高
                List<RedisValue> lstRemove = new List<RedisValue>(10);

                for (int i = 0; i < keys.Length; i++)
                {
                    //Score为时间戳
                    var ik = keys[i];
                    if (ik.Score > tk_Start)
                    {
                        tk_Start = ik.Score;
                    }
                    var ret = db.StringGet(ik.Element.ToString());
                    //为空表示已经过期 加入待移除列表
                    if (ret.IsNull)
                    {
                        lstRemove.Add(ik.Element);
                    }
                    else
                    {
                        tk = (long)ik.Score;
                        lst.Add(ret);
                        if (count > 0 && lst.Count >= count)
                        {
                            canGetKeys = false;
                            break;
                        }
                    }
                }
                if (lstRemove.Count > 0)
                {
                    db.SortedSetRemove(QueueName, lstRemove.ToArray());
                }
            }
            return lst;
        }
        /// <summary>
        /// 添加到队列
        /// </summary>
        /// <param name="QueueName">队列名称</param>
        /// <param name="val">值</param>
        /// <param name="expiry">过期时间</param>
        /// <param name="key">防重复键，为NULL为当前时间戳</param>
        /// <returns></returns>
        public double Add(string QueueName, string val, DateTime expiry, string key = null)
        {
            return Add(QueueName, val, expiry - DateTime.Now, key);
        }
        object lck = new object();
        /// <summary>
        /// 添加到队列
        /// </summary>
        /// <param name="QueueName">队列名称</param>
        /// <param name="key">键防重复用</param>
        /// <param name="val">值</param>
        /// <param name="expiry">过期时间</param>
        /// <returns>添加的时间戳</returns>
        public double Add(string QueueName, string val, TimeSpan expiry, string key = null)
        {
            var db = GetDatabase();
            long tk;
            //锁住并暂停1毫秒防止时间戳重复
            lock (lck)
            {
                tk = SQ.Base.DateTimeHelper.ToTimeStamp(DateTime.Now);
                System.Threading.Thread.Sleep(1);
                if (key == null)
                {
                    key = tk.ToString();
                }
            }
            key = QueueName + "_k_" + key;
            db.StringSet(key, val, expiry);
            db.SortedSetAdd(QueueName, key, tk);
            ClearExpired(QueueName, false, db);
            return tk;
        }


        public void ClearExpired(string QueueName, bool Force = false, IDatabase db = null)
        {
            if (db == null)
            {
                db = GetDatabase();
            }
            if (Force || db.SortedSetLength(QueueName) > ClearLen)
            {
                long d = 0;
                GetList(QueueName, ref d, 1);
            }
        }
    }
}
