﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <connectionStrings>
    <add name="connectionString" connectionString="Database=filecache;Data Source=**************;port=3306;User Id=root;Password=root;charset=utf8;pooling=true"/>
    <add name="RedisExchangeHosts" connectionString="************:7000,************:7001,************:7002,************:7003,************:7004,************:7005,connectTimeout=20000,syncTimeout=20000,responseTimeout=20000"/>
  </connectionStrings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/>
  </startup>
</configuration>