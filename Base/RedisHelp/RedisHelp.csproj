﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.0;net45</TargetFrameworks>
  </PropertyGroup>

  <!--通用平台配置满足netstandard2.0和net45-->
  <ItemGroup>
    <PackageReference Condition="'$(TargetFramework)' =='netstandard2.0' " Include="StackExchange.Redis" Version="2.7.33" />
    <PackageReference Condition="'$(TargetFramework)' =='net45' " Include="StackExchange.Redis" Version="1.2.6" />
  </ItemGroup>
  <!--平台配置满足netstandard2.0-->


  <ItemGroup>
    <ProjectReference Include="..\CV.Base\CV.Base.csproj" />
  </ItemGroup>

</Project>
