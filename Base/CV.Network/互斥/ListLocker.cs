﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace Network
{
	/// <summary>
	/// 线程安全链表
	/// </summary>
	/// <typeparam name="TValue"></typeparam>
	public class ListLocker<TValue> : List<TValue>
	{
		private ReaderWriterLock Locker { get; set; }
		private SpinLock SpinLocker { get; set; }

        public ListLocker()
		{
			Locker = new ReaderWriterLock();
			SpinLocker = new SpinLock(false);
		}

		public ListLocker(int capacity)
			: base(capacity)
		{
			Locker = new ReaderWriterLock();
			SpinLocker = new SpinLock(false);
		}

		public ListLocker(IEnumerable<TValue> items)
			: base(items)
		{
			Locker = new ReaderWriterLock();
			SpinLocker = new SpinLock(false);
		}
         

        /// <summary>
        /// 获取读锁
        /// </summary>
        /// <param name="millisecondsTimeout">超时时间</param>
        public void AcquireRead(int millisecondsTimeout = -1)
        {
            AcquireReadIn(millisecondsTimeout);
        }

        /// <summary>
        /// 释放读锁
        /// </summary>
        public void ReleaseRead()
        {
            ReleaseReadIn();

        }

        /// <summary>
        /// 获取读锁
        /// </summary>
        /// <param name="millisecondsTimeout">超时时间</param>
        private void AcquireReadIn(int millisecondsTimeout = -1)
        {

            if (CommTstDT.TimeMonitor)
            {
                var id = new System.Diagnostics.StackTrace(2).ToString();
                var dt = DateTime.Now;
                Locker.AcquireReaderLock(millisecondsTimeout);
                if (!CommTstDT.ditTimeRL.ContainsKey(id))
                {
                    CommTstDT.ditTimeRL[id] = new tstDT()
                    {
                        Name = id.ToString()
                    };
                }
                CommTstDT.ditTimeRL[id].Start(dt);
            }
            else
            {
                Locker.AcquireReaderLock(millisecondsTimeout);
            }
        }

        /// <summary>
        /// 释放读锁
        /// </summary>
        private void ReleaseReadIn()
        {
            if (CommTstDT.TimeMonitor)
            {
                var id = new System.Diagnostics.StackTrace(2).ToString();
                CommTstDT.ditTimeRL[id].End();
            }
            Locker.ReleaseReaderLock();
        }

        /// <summary>
        /// 获取写锁
        /// </summary>
        /// <param name="millisecondsTimeout">超时时间</param>
        public void AcquireWrite(int millisecondsTimeout = -1)
        {
            AcquireWriteIn(millisecondsTimeout);
        }

        /// <summary>
        /// 释放写锁
        /// </summary>
        public void ReleaseWrite()
        {
            ReleaseWriteIn();
        }
        /// <summary>
        /// 获取写锁
        /// </summary>
        /// <param name="millisecondsTimeout">超时时间</param>
        private void AcquireWriteIn( int millisecondsTimeout = -1)
        {
            if (CommTstDT.TimeMonitor)
            {
                var id = new System.Diagnostics.StackTrace(2).ToString();
                var dt = DateTime.Now;
                Locker.AcquireWriterLock(millisecondsTimeout);

                if (!CommTstDT.ditTimeWL.ContainsKey(id))
                {
                    CommTstDT.ditTimeWL[id] = new tstDT()
                    {
                        Name = id.ToString()
                    };
                }
                CommTstDT.ditTimeWL[id].Start(dt);
            }
            else
            {
                Locker.AcquireWriterLock(millisecondsTimeout);
            }
        }

        /// <summary>
        /// 释放写锁
        /// </summary>
        private void ReleaseWriteIn()
        {
            if (CommTstDT.TimeMonitor)
            {
                var id = new System.Diagnostics.StackTrace(2).ToString();
                CommTstDT.ditTimeWL[id].End();
            }
            Locker.ReleaseWriterLock();
		}

		/// <summary>
		/// 加锁
		/// </summary>
		public void Enter()
		{
// 			while (true)
// 			{
// 				bool result = false;
// 				SpinLocker.Enter(ref result);
// 				if (result)
// 				{
// 					break;
// 				}
// 			}
			AcquireWriteIn();
		}

		/// <summary>
		/// 解锁
		/// </summary>
		public void Exit()
		{
//			SpinLocker.Exit();
			ReleaseWriteIn();
		}
	}
}
