﻿using SQ.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace Network
{
    public class CommTstDT
    {
        public const bool TimeMonitor = false;

        public static Dictionary<string, tstDT> ditTimeR = new Dictionary<string, tstDT>();
        public static Dictionary<string, tstDT> ditTimeW = new Dictionary<string, tstDT>();
        public static Dictionary<string, tstDT> ditTimeRL = new Dictionary<string, tstDT>();
        public static Dictionary<string, tstDT> ditTimeWL = new Dictionary<string, tstDT>();


        public static string OutPut()
        {
            StringBuilder str = new StringBuilder();
            foreach (var item in ditTimeR)
            {
                str.AppendFormat("{0, -16}: {1, -8}\r\n", "ditTimeR", item.Key);
                str.AppendFormat("{0, -16}: {1, -8} ", "MaxWaitInTime", item.Value.MaxWaitInTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "AvgWait", item.Value.AvgWait.ToString("0.000"));
                str.AppendFormat("{0, -16}: {1, -8} ", "Times", item.Value.Times);
                str.AppendFormat("{0, -16}: {1, -8} \r\n", "LastInTime", item.Value.LastInTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "MaxTime", item.Value.MaxTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "Avg", item.Value.Avg.ToString("0.000"));
                str.AppendFormat("{0, -16}: {1, -8} ", "TimesEnd", item.Value.TimesEnd);
                str.AppendFormat("{0, -16}: {1, -8}\r\n", "LastWaitInTime", item.Value.LastWaitInTime);
            }
            foreach (var item in ditTimeW)
            {
                str.AppendFormat("{0, -16}: {1, -8}\r\n", "ditTimeW", item.Key);
                str.AppendFormat("{0, -16}: {1, -8} ", "MaxWaitInTime", item.Value.MaxWaitInTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "AvgWait", item.Value.AvgWait.ToString("0.000"));
                str.AppendFormat("{0, -16}: {1, -8} ", "Times", item.Value.Times);
                str.AppendFormat("{0, -16}: {1, -8} \r\n", "LastInTime", item.Value.LastInTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "MaxTime", item.Value.MaxTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "Avg", item.Value.Avg.ToString("0.000"));
                str.AppendFormat("{0, -16}: {1, -8} ", "TimesEnd", item.Value.TimesEnd);
                str.AppendFormat("{0, -16}: {1, -8}\r\n", "LastWaitInTime", item.Value.LastWaitInTime);
            }
            foreach (var item in ditTimeRL)
            {
                str.AppendFormat("{0, -16}: {1, -8}\r\n", "ditTimeRL", item.Key);
                str.AppendFormat("{0, -16}: {1, -8} ", "MaxWaitInTime", item.Value.MaxWaitInTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "AvgWait", item.Value.AvgWait.ToString("0.000"));
                str.AppendFormat("{0, -16}: {1, -8} ", "Times", item.Value.Times);
                str.AppendFormat("{0, -16}: {1, -8} \r\n", "LastInTime", item.Value.LastInTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "MaxTime", item.Value.MaxTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "Avg", item.Value.Avg.ToString("0.000"));
                str.AppendFormat("{0, -16}: {1, -8} ", "TimesEnd", item.Value.TimesEnd);
                str.AppendFormat("{0, -16}: {1, -8}\r\n", "LastWaitInTime", item.Value.LastWaitInTime);
            }
            foreach (var item in ditTimeWL)
            {
                str.AppendFormat("{0, -16}: {1, -8}\r\n", "ditTimeWL", item.Key);
                str.AppendFormat("{0, -16}: {1, -8} ", "MaxWaitInTime", item.Value.MaxWaitInTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "AvgWait", item.Value.AvgWait.ToString("0.000"));
                str.AppendFormat("{0, -16}: {1, -8} ", "Times", item.Value.Times);
                str.AppendFormat("{0, -16}: {1, -8} \r\n", "LastInTime", item.Value.LastInTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "MaxTime", item.Value.MaxTime);
                str.AppendFormat("{0, -16}: {1, -8} ", "Avg", item.Value.Avg.ToString("0.000"));
                str.AppendFormat("{0, -16}: {1, -8} ", "TimesEnd", item.Value.TimesEnd);
                str.AppendFormat("{0, -16}: {1, -8}\r\n", "LastWaitInTime", item.Value.LastWaitInTime);
            }

            return str.ToString();
        }

    }
    public class tstDT
    {
        public tstDT()
        {
            Times = new TestCount();
            TimesEnd = new TestCount();
        }
        public string Name { get; set; }
        //id已用至65
        public DateTime LastInTime { get; set; }
        public DateTime LastOutTime { get; set; }
        public TimeSpan MaxTime { get; set; }
        public TimeSpan SumTime { get; set; }
        public TestCount Times { get; set; }
        public TestCount TimesEnd { get; set; }

        public TimeSpan LastWaitInTime { get; set; }
        public TimeSpan MaxWaitInTime { get; set; }
        public TimeSpan SumWaitInTime { get; set; }

        public double Avg
        {
            get
            {
                return SumTime.TotalMilliseconds / TimesEnd.GetCount();
            }
        }
        public double AvgWait
        {
            get
            {
                return SumWaitInTime.TotalMilliseconds / TimesEnd.GetCount();
            }
        }

        public void Start(DateTime wsTime)
        {
            LastWaitInTime = DateTime.Now - wsTime;
            if (MaxTime < LastWaitInTime)
            {
                MaxWaitInTime = LastWaitInTime;
            }
            SumWaitInTime += LastWaitInTime;

            LastInTime = DateTime.Now;
            Times.Increment();
        }
        public void End()
        {
            LastOutTime = DateTime.Now;
            var now = (LastOutTime - LastInTime);
            if (MaxTime < now)
            {
                MaxTime = now;
            }
            SumTime += now;
            TimesEnd.Increment();

        }
    }
    /// <summary>
    /// 线程安全字典
    /// </summary>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    public class DictionaryLocker<TKey, TValue> : Dictionary<TKey, TValue>
    {
        private ReaderWriterLock Locker { get; set; }
        private SpinLock SpinLocker { get; set; }


        public DictionaryLocker()
        {
            Locker = new ReaderWriterLock();
            SpinLocker = new SpinLock(false);
        }
        /// <summary>
        /// 获取读锁
        /// </summary>
        /// <param name="millisecondsTimeout"></param>
        public void AcquireRead(int millisecondsTimeout = -1)
        {
            AcquireReadIn(millisecondsTimeout);
        }

        /// <summary>
        /// 释放读锁
        /// </summary>
        public void ReleaseRead()
        {
            ReleaseReadIn();
        }

        /// <summary>
        /// 获取读锁
        /// </summary>
        /// <param name="millisecondsTimeout"></param>
        private void AcquireReadIn(int millisecondsTimeout)
        {
            if (CommTstDT.TimeMonitor)
            {
                var dt = DateTime.Now;
                var id = new System.Diagnostics.StackTrace(2).ToString();
                Locker.AcquireReaderLock(millisecondsTimeout);
                if (!CommTstDT.ditTimeR.ContainsKey(id))
                {
                    CommTstDT.ditTimeR[id] = new tstDT()
                    {
                        Name = id
                    };
                }
                CommTstDT.ditTimeR[id].Start(dt);
            }
            else
            {
                Locker.AcquireReaderLock(millisecondsTimeout);
            }
        }

        /// <summary>
        /// 释放读锁
        /// </summary>
        private void ReleaseReadIn()
        {
            if (CommTstDT.TimeMonitor)
            {
                var id = new System.Diagnostics.StackTrace(2).ToString();
                CommTstDT.ditTimeR[id].End();
            }
            Locker.ReleaseReaderLock();
        }


        /// <summary>
        /// 获取写锁
        /// </summary>
        /// <param name="millisecondsTimeout"></param>
        public void AcquireWrite(int millisecondsTimeout = -1)
        {
            AcquireWriteIn(millisecondsTimeout);
        }

        /// <summary>
        /// 释放写锁
        /// </summary>
        public void ReleaseWrite()
        {
            ReleaseWriteIn();
        }

        private void AcquireWriteIn(int millisecondsTimeout)
        {
            if (CommTstDT.TimeMonitor)
            {
                var dt = DateTime.Now;
                var id = new System.Diagnostics.StackTrace(2).ToString();
                Locker.AcquireWriterLock(millisecondsTimeout);

                if (!CommTstDT.ditTimeW.ContainsKey(id))
                {
                    CommTstDT.ditTimeW[id] = new tstDT()
                    {
                        Name = id.ToString()
                    };
                }
                CommTstDT.ditTimeW[id].Start(dt);
            }
            else
            {
                Locker.AcquireWriterLock(millisecondsTimeout);
            }
        }

        /// <summary>
        /// 释放写锁
        /// </summary>
        private void ReleaseWriteIn()
        {
            if (CommTstDT.TimeMonitor)
            {
                var id = new System.Diagnostics.StackTrace(2).ToString();
                CommTstDT.ditTimeW[id].End();
            }
            Locker.ReleaseWriterLock();
        }

        /// <summary>
        /// 加锁
        /// </summary>
        public void Enter()
        {
            // 			while (true)
            // 			{
            // 				bool result = false;
            // 				SpinLocker.Enter(ref result);
            // 				if (result)
            // 				{
            // 					break;
            // 				}
            // 			}
            AcquireWriteIn(-1);
        }

        /// <summary>
        /// 解锁
        /// </summary>
        public void Exit()
        {
            //SpinLocker.Exit();
            ReleaseWriteIn();
        }
    }
}
