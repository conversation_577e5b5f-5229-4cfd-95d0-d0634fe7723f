﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SQ.Base;
using System.Threading;

namespace Network
{
    #region 委托定义
    public delegate void EventClientConnect(object sender, ClientConnectArg arg);
    public delegate void EventClientReceive(object sender, ClientReceiveArg arg);
    public delegate void EventClientSend(object sender, ClientSendArg arg);
    public delegate void EventClientlDispose(object sender, ClientDisposeArg arg);
    public delegate void EventClientError(object sender, ClientErrorArg arg);
    #endregion

    public abstract class Client
    {
        private AutoResetEvent connWaiter = new AutoResetEvent(false);

        #region 属性
        /// <summary>
        /// 客户端连接通道
        /// </summary>
        public Channel Channel { get; protected set; }
        /// <summary>
        /// 用户令牌
        /// </summary>
        public object Tag { get; set; }
        /// <summary>
        /// 日志打印标志
        /// </summary>
        public bool LogFocus = false;
        /// <summary>
        /// Client计数器
        /// </summary>
        public ClientCounter ClientCounter = new ClientCounter();
        /// <summary>
        /// 客户端连接事件
        /// </summary>
        public event EventClientConnect ClientConnect;
        /// <summary>
        /// 客户端释放事件
        /// </summary>
        public event EventClientlDispose ClientDispose;
        /// <summary>
        /// 客户端异常事件
        /// </summary>
        public event EventClientError ClientError;
        /// <summary>
        /// 客户端收到数据事件
        /// </summary>
        public EventClientReceive ObjectReceive;
        /// <summary>
        /// 客户端数据发送完成事件
        /// </summary>
        public EventClientSend ObjectSend;
        #endregion

        #region 事件处理
        /// <summary>
        /// 客户端连接事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnConnect(object sender, ChannelConnectArg arg)
        {
            connWaiter.Set();
            if (ClientConnect != null)
            {
                try
                {
                    ClientConnect(this, new ClientConnectArg(this));
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Client event error", ex);
                }
            }
        }

        /// <summary>
        /// 客户端释放事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnDispose(object sender, ChannelDisposeArg arg)
        {
            if (ClientDispose != null)
            {
                try
                {
                    ClientDispose(this, new ClientDisposeArg(this));
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Client event error", ex);
                }
            }
        }

        /// <summary>
        /// 客户端错误事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnError(object sender, ChannelErrorArg arg)
        {
            if (ClientError != null)
            {
                try
                {
                    ClientError(this, new ClientErrorArg(this));
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Client event error", ex);
                }
            }
        }

        /// <summary>
        /// 客户端收到数据事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnReceive(object sender, ChannelReceiveArg arg)
        {
            ClientCounter.RefreshReceiveCount();
            if (ClientCounter.ReceiveCountSum < 1 || (LogFocus && arg.Data != null)) Log.WriteLog4("[OnReceive]source=" + Channel.RemoteHost + ":" + Channel.RemotePort.ToString() + ",arg.Data=", arg.Data, LOGTYPE.INFO);
            if (ObjectReceive != null)
            {
                try
                {
                    ObjectReceive(this, new ClientReceiveArg(arg.Data));
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Client event error", ex);
                }
            }
        }

        /// <summary>
        /// 客户端数据发送完成事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnSend(object sender, ChannelSendArg arg)
        {
            bool boolExcuteObjectSend = false;
            if (ObjectSend != null)
            {
                try
                {
                    ObjectSend(this, new ClientSendArg(arg.Token));
                    boolExcuteObjectSend = true;
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Client event error", ex);
                }
            }
            if (LogFocus && arg.Buffer != null) Log.WriteLog4("[OnSend]ExcuteObjectSend=" + boolExcuteObjectSend.ToString() + " __ 发往" + (sender as Channel).RemoteHost + ":" + (sender as Channel).RemotePort.ToString() + "的数据：", arg.Buffer, LOGTYPE.INFO);
            ClientCounter.RefreshSentCount();
            //if ((sender as Channel).RemoteHost == "***************" || (sender as Channel).RemoteHost == "*************" || (sender as Channel).RemoteHost == "*************" || (sender as Channel).RemoteHost == "************")
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 通道通道构造客户端
        /// </summary>
        /// <param name="channel">通道</param>
        public Client(Channel channel)
        {
            Channel = channel;
            Channel.ChannelConnect += OnConnect;
            Channel.ChannelDispose += OnDispose;
            Channel.ChannelError += OnError;
            Channel.DataReceive += OnReceive;
            Channel.DataSend += OnSend;
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="token"></param>
        public virtual void Send(byte[] data)
        {
            if (data != null)
            {
                //Channel.SendAsync(data);
                Channel.Send(data);
                ClientCounter.RefreshSendCount();
                if (ClientCounter.SendCountSum < 1 || (LogFocus && data != null)) Log.WriteLog4("[Send]target=" + Channel.RemoteHost + ":" + Channel.RemotePort.ToString() + ",data=", data, LOGTYPE.INFO);
            }
        }

        /// <summary>
        /// 关闭客户端
        /// </summary>
        public virtual void Close()
        {
            Channel.Close();
        }

        public bool Connect(int _timeout)
        {
            try
            {
                bool result = false;
                Channel.Connect();
                if (connWaiter.WaitOne(_timeout))
                {
                    result = true;
                }
                else
                {
                    result = false;
                }
                return result;
            }
            catch (Exception e)
            {
                return false;
            }
        }
        #endregion
    }

}
