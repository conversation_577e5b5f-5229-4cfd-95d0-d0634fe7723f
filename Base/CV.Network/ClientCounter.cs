﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Network
{
    public class ClientCounter
    {
        DateTime CompareTime = new DateTime();
        uint SentCountCurrentSecond = 0;
        public uint SentCountLastSecond = 0;
        public ulong SentCountSum = 0;
        uint SendCountCurrentSecond = 0;
        public uint SendCountLastSecond = 0;
        public ulong SendCountSum = 0;
        uint ReceiveCountCurrentSecond = 0;
        public uint ReceiveCountLastSecond = 0;
        public ulong ReceiveCountSum = 0;
        public uint MaxDiffBetweenSendAndSent = 10000;
        public void RefreshSendCount()
        {
            if (DateTime.Now.Second != CompareTime.Second) RefreshCompareTime();
            SendCountCurrentSecond++;
        }
        public void RefreshSentCount()
        {
            if (DateTime.Now.Second != CompareTime.Second) RefreshCompareTime();
            SentCountCurrentSecond++;
        }
        public void RefreshReceiveCount()
        {
            if (DateTime.Now.Second != CompareTime.Second) RefreshCompareTime();
            ReceiveCountCurrentSecond++;
        }
        private void RefreshCompareTime()
        {
            SendCountSum += SendCountLastSecond;
            SendCountLastSecond = SendCountCurrentSecond;
            SendCountCurrentSecond = 0;
            SentCountSum += SentCountLastSecond;
            SentCountLastSecond = SentCountCurrentSecond;
            SentCountCurrentSecond = 0;
            ReceiveCountSum += ReceiveCountLastSecond;
            ReceiveCountLastSecond = ReceiveCountCurrentSecond;
            ReceiveCountCurrentSecond = 0;
            CompareTime = DateTime.Now;
        }
    }
}
