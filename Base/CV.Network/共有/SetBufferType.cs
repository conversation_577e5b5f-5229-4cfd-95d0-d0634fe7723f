﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Network
{
    /// <summary>
    /// 下次接收SetBuff类型
    /// </summary>
    public enum SetBufferType
    {
        /// <summary>
        /// 不改变(多用于默认模式，一个buff，每次接收从0赋值)
        /// </summary>
        Keep,
        /// <summary>
        /// 使用ChannelReceiveArg返回值(多用于buff不够解析，将下次接收的数据直接追加到buff后)
        /// </summary>
        UseChannelReceiveArg,
        /// <summary>
        /// 获取新的buff(多用于接收后的数据由其他线程处理，无需内存拷贝，此模式在不再使用buff时需要手动调用ChannelReceiveArg.Dispose，否则可能内存泄露)
        /// </summary>
        GetNew
    }
}
