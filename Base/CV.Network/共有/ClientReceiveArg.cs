﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Network
{
	/// <summary>
	/// 客户端收到数据参数
	/// </summary>
	public class ClientReceiveArg
	{
		/// <summary>
		/// 获取或设置接收到数据
		/// </summary>
		public byte[] Data { get; set; }

		public ClientReceiveArg()
		{
		}

		/// <summary>
		/// 构造函数
		/// </summary>
		/// <param name="data">接收数据</param>
		public ClientReceiveArg(byte[] data)
		{
			Data = data;
		}
	}
}
