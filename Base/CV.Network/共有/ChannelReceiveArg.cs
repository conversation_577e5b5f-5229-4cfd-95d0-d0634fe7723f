﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;

namespace Network
{
    /// <summary>
    /// 通道收到数据参数
    /// </summary>
    public class ChannelReceiveArg

#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
        : IDisposable
#endif
    {
        private byte[] data = null;
        /// <summary>
        /// 获取中设置接收到数据
        /// </summary>
        public byte[] Data
        {
            get
            {
                if (data == null)
                {
                    if (Buffer != null)
                    {
                        byte[] temp = new byte[BufferSize];
                        Array.Copy(Buffer, BufferOffset, temp, 0, BufferSize);
                        data = temp;
                    }
                }
                return data;
            }
            set
            {
                data = value;
            }
        }

        /// <summary>
        /// 直接使用网络接收buffer
        /// </summary>
        public byte[] Buffer { get; private set; }
        public int BufferOffset;
        public int BufferSize;

        /// <summary>
        /// 构造函数 设置网络接收buffer
        /// </summary>
        public ChannelReceiveArg(byte[] buffer, int offset, int size)
        {
            Buffer = buffer;
            BufferOffset = offset;
            BufferSize = size;
        }
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
        /// <summary>
        /// 表示下一次接收继续使用此buff
        /// </summary>
        public SetBufferType NextRec { get; set; }
        public bool IsDispose { get; protected set; }

        public void ChangeBuffer(SocketAsyncEventArgs e)
        {
            if (NextRec == SetBufferType.GetNew)
            {
                byte[] buffer = Managers.BufferManager.Get();
                e.SetBuffer(buffer, 0, buffer.Length);
            }
            else if (NextRec == SetBufferType.UseChannelReceiveArg)
            {
                e.SetBuffer(Buffer, BufferOffset, BufferSize);
            }
        }
        /// <summary>
        /// 调用会回收buff到池
        /// </summary>
        public void Dispose()
        {
            if (IsDispose)
            {
                return;
            }
            IsDispose = true;
            Managers.BufferManager.Return(Buffer);
        }
#endif
    }
}
