﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Network
{
    //#if NETFRAMEWORK
    public class SendBufferList : List<MySendBuffer>, IList<ArraySegment<byte>>
    {
        ArraySegment<byte> IList<ArraySegment<byte>>.this[int index]
        {
            get
            {
                return this[index].ToArraySegment();
            }

            set
            {
                this[index] = new MySendBuffer(value.Array, value.Offset, value.Count);
            }
        }

        public int AllDataCount { get; private set; }

        bool ICollection<ArraySegment<byte>>.IsReadOnly => true;

        public void AddItem(MySendBuffer item)
        {
            this.Add(item);
            AllDataCount += item.Count;
        }
        public void AddItem(byte[] Data, int Offset, int Count)
        {
            AddItem(new MySendBuffer(Data, Offset, Count));
        }

        void ICollection<ArraySegment<byte>>.Add(ArraySegment<byte> item)
        {
            AddItem(item.Array, item.Offset, item.Count);
        }

        bool ICollection<ArraySegment<byte>>.Contains(ArraySegment<byte> item)
        {
            throw new NotImplementedException();
        }

        void ICollection<ArraySegment<byte>>.CopyTo(ArraySegment<byte>[] array, int arrayIndex)
        {
            throw new NotImplementedException();
        }

        IEnumerator<ArraySegment<byte>> IEnumerable<ArraySegment<byte>>.GetEnumerator()
        {
            return this.ConvertAll<ArraySegment<byte>>(p => p.ToArraySegment()).GetEnumerator();

        }

        int IList<ArraySegment<byte>>.IndexOf(ArraySegment<byte> item)
        {
            throw new NotImplementedException();
        }

        void IList<ArraySegment<byte>>.Insert(int index, ArraySegment<byte> item)
        {
            throw new NotImplementedException();
        }

        bool ICollection<ArraySegment<byte>>.Remove(ArraySegment<byte> item)
        {
            throw new NotImplementedException();
        }
    }
    //#else
    //    public class SendBufferList : List<  Span<byte>>
    //    {
    //        public int AllDataCount { get; private set; }
    //        public void AddItem(ReadOnlySpan<byte> item)
    //        {
    //            this.Add(item);
    //            AllDataCount += item.Length;
    //        }
    //    }
    //#endif

    public class MySendBuffer
    {
        public MySendBuffer(byte[] Array, int Offset, int Count)
        {
            this.Array = Array;
            this.Offset = Offset;
            this.Count = Count;
        }
        public byte[] Array { get; set; }
        public int Offset { get; set; }

        public int Count { get; set; }

        public ArraySegment<byte> ToArraySegment()
        {
            return new ArraySegment<byte>(Array, Offset, Count);
        }
    }
}
