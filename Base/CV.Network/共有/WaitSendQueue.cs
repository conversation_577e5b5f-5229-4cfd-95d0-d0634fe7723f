﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Text;
using System.Threading;

namespace Network
{
    public class WaitSendQueue : ConcurrentQueue<MySendBuffer>
    {
        private int m_wait_send_data_size = 0;
        public int WaitSendDataSize
        {
            get
            {
                return m_wait_send_data_size;
            }
        }
        public void Add(byte[] Data, int Offset, int Count)
        {
            Add(new MySendBuffer(Data, Offset, Count));
        }
        public void Add(MySendBuffer item)
        {
            Interlocked.Add(ref m_wait_send_data_size, item.Count);
            this.Enqueue(item);
        }
        int _OnceSendBytesLimit = 1024 * 1024 * 50;
        /// <summary>
        /// 单次发送字节数限制(会超)
        /// </summary>
        public int OnceSendBytesLimit
        {
            get { return _OnceSendBytesLimit; }
            set
            {
                _OnceSendBytesLimit = value;
            }
        }
        public SendBufferList DequeueAll()
        {
            SendBufferList lst = new SendBufferList();
            var len = 0;
            while (this.TryDequeue(out var bts))
            {
                lst.AddItem(bts);
                len += bts.Count;
                if (len >= _OnceSendBytesLimit)
                {
                    break;
                }
            }
            if (lst.Count > 0)
                Interlocked.Add(ref m_wait_send_data_size, -lst.AllDataCount);
            return lst;
        }
    }
}
