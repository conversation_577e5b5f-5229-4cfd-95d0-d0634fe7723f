﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Network
{
    /// <summary>
    /// 通道数据发送完成事件
    /// </summary>
    public class ChannelSendArg
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="token">用户令牌</param>
        /// <param name="Buffer">缓存</param>
        /// <param name="Offset">偏移量</param>
        /// <param name="Count">数据量</param>
        public ChannelSendArg(object token, byte[] Buffer, int Offset, int Count)
        {
            this.Token = token;
            this.Buffer = Buffer;
            this.Offset = Offset;
            this.Count = Count;
            this.BytesTransferred = Count;
        }
        public ChannelSendArg(object token, SendBufferList BufferList)
        {
            this.Token = token;
            this.BufferList = BufferList;
            this.BytesTransferred = BufferList.AllDataCount;
        }
        /// <summary>
        /// 缓存
        /// </summary>
        public byte[] Buffer { get; private set; }
        /// <summary>
        /// 数据量
        /// </summary>
        public int Count { get; private set; }
        /// <summary>
        /// 偏移量
        /// </summary>
        public int Offset { get; private set; }
        /// <summary>
        /// 获取或设置用户令牌.
        /// </summary>
        /// <remarks>返回时是通道发送数据时给出的令牌</remarks>
        public object Token { get; set; }

        public SendBufferList BufferList { get; set; }
        public int BytesTransferred { get; private set; }
    }
}
