﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;

namespace Network
{
	/// <summary>
	/// 通道释放参数
	/// </summary>
	public class ChannelDisposeArg
	{
		/// <summary>
		/// 获取或设置要释放的通道
		/// </summary>
		public Channel Channel { get; set; }

		/// <summary>
		/// 获取或设置套接字错误。
		/// </summary>
		public SocketError SocketError { get; set; }

		public ChannelDisposeArg()
		{
		}

		/// <summary>
		/// 构造函数
		/// </summary>
		/// <param name="channel">释放通道</param>
		/// <param name="error">释放通道结果</param>
		public ChannelDisposeArg(Channel channel, SocketError error)
		{
			Channel = channel;
			SocketError = error;
		}
	}
}
