﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;

namespace Network
{
	/// <summary>
	/// 通道错误参数
	/// </summary>
	public class ChannelErrorArg
	{
		/// <summary>
		/// 获取或设置出错通道
		/// </summary>
		public Channel Channel { get; set; }

		/// <summary>
		/// 获取或设置套接字错误。
		/// </summary>
		public SocketError SocketError { get; set; }

		public ChannelErrorArg()
		{
		}

		/// <summary>
		/// 构造函数
		/// </summary>
		/// <param name="channel">异常通道</param>
		/// <param name="error">异常通道结果</param>
		public ChannelErrorArg(Channel channel, SocketError error)
		{
			Channel = channel;
			SocketError = error;
		}
	}
}
