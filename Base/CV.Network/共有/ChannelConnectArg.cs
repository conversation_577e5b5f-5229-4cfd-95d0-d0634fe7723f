﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;

namespace Network
{
	/// <summary>
	/// 通道连接参数
	/// </summary>
	public class ChannelConnectArg
	{
		/// <summary>
		/// 获取或设置连接的通道
		/// </summary>
		public Channel Channel { get; set; }
		/// <summary>
		/// 获取或设置套接字错误。
		/// </summary>
		public SocketError SocketError { get; set; }

		public ChannelConnectArg()
		{
		}
		
		/// <summary>
		/// 构造函数
		/// </summary>
		/// <param name="channel">连接通道</param>
		/// <param name="error">连接结果</param>
		public ChannelConnectArg(Channel channel, SocketError error)
		{
			Channel = channel;
			SocketError = error;
		}
	}
}
