﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using Microsoft.Extensions.ObjectPool;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;

namespace CV.Network.ObjectPool
{
    public abstract class AnyPolicy<T> : IPooledObjectPolicy<T>
    {
#if DEBUG
        private int crt = 0;
        private int ret = 0;
#endif
        public AnyPolicy()
        {
        }
        public T Create()
        {
#if DEBUG
            Interlocked.Increment(ref crt);
#endif
            return GetNewT();
        }

        public abstract T GetNewT();

        public bool Return(T obj)
        {
#if DEBUG
            Interlocked.Increment(ref ret);
#endif
            return true;
        }
    }
    public class DefaultAnyPolicy<T> : AnyPolicy<T> where T : new()
    {
        public override T GetNewT()
        {
            return new T();
        }
    }
}
#endif
