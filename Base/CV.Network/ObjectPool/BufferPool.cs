﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using Microsoft.Extensions.ObjectPool;
using System;
using System.Collections.Generic;
using System.Text;

namespace CV.Network.ObjectPool
{
    public class BufferPool : DefaultObjectPool<byte[]>
    {
        public BufferPool(int buffserSize = 8192) : base(new BufferPolicy(buffserSize))
        {

        }
        public BufferPool(int buffserSize, int maximumRetained) : base(new BufferPolicy(buffserSize), maximumRetained)
        {

        }
    }
}
#endif