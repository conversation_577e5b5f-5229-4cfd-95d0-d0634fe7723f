﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using Microsoft.Extensions.ObjectPool;
using System;
using System.Collections.Generic;
using System.Text;

namespace CV.Network.ObjectPool
{
    public class AnyPool<T> : DefaultObjectPool<T> where T : class
    {
        public AnyPool(AnyPolicy<T> policy) : base(policy)
        {

        }
        public AnyPool(AnyPolicy<T> policy, int maximumRetained) : base(policy, maximumRetained)
        {

        }
    }
}
#endif