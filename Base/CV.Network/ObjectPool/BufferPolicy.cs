﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using Microsoft.Extensions.ObjectPool;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;

namespace CV.Network.ObjectPool
{
    class BufferPolicy : IPooledObjectPolicy<byte[]>
    {
#if DEBUG
        private int crt = 0;
        private int ret = 0;
#endif
        public int BuffserSize { get; protected set; }
        public BufferPolicy(int buffserSize = 8192)
        {
            BuffserSize = buffserSize;
        }
        public byte[] Create()
        {
#if DEBUG
            Interlocked.Increment(ref crt);
#endif
            return new byte[BuffserSize];
        }

        public bool Return(byte[] obj)
        {
#if DEBUG
            Interlocked.Increment(ref ret);
#endif
            return true;
        }
    }
}
#endif
