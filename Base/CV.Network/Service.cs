﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;
using SQ.Base;

namespace Network
{
    /// <summary>
    /// 用户服务类基类
    /// </summary>
    public abstract class Service
    {
        #region 属性
        /// <summary>
        /// 获取通道服务列表,客户端服务启动前添加需要启动的通道服务
        /// </summary>
        public DictionaryLocker<object, Server> Servers { get; protected set; }
        /// <summary>
        /// 获取已连接客户端
        /// </summary>
        public DictionaryLocker<object, Client> Clients { get; protected set; }
        /// <summary>
        /// 客户端连接事件
        /// </summary>
        public virtual event EventClientConnect ClientConnect;
        /// <summary>
        /// 客户端释放事件
        /// </summary>
        public virtual event EventClientlDispose ClientDispose;
        /// <summary>
        /// 客户端异常事件
        /// </summary>
        public virtual event EventClientError ClientError;
        /// <summary>
        /// 用户令牌
        /// </summary>
        public object Tag { get; set; }
        #endregion

        #region 公共方法
        /// <summary>
        /// 构造方法
        /// </summary>
        public Service()
        {
            Servers = new DictionaryLocker<object, Server>();
            Clients = new DictionaryLocker<object, Client>();
        }

        /// <summary>
        /// 客户端服务初始化
        /// </summary>
        /// <returns>初始化结果.true为成功,否则失败</returns>
        public virtual bool Initialize()
        {
            if (Servers != null)
            {
                foreach (var server in Servers.Values)
                {
                    server.ChannelConnect += OnChannelConnect;
                    server.ChannelDispose += OnChannelDispose;
                    server.ChannelError += OnChannelError;
                }
            }
            return true;
        }

        /// <summary>
        /// 启动客户端服务
        /// </summary>
        /// <returns>启动结果.true为成功,否则失败</returns>
        public virtual bool Start()
        {
            bool result = true;
            foreach (var server in Servers.Values)
            {
                if (!server.Start())
                {
                    result = false;
                    break;
                }
            }
            return result;
        }

        /// <summary>
        /// 停止客户端服务
        /// </summary>
        public virtual void Stop()
        {
            foreach (var server in Servers.Values)
            {
                server.Stop();
            }

            Clients.Enter();
            Client[] clients = Clients.Values.ToArray();
            Clients.Exit();

            foreach (var client in clients)
            {
                client.Close();
            }
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 客户端连接事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnClientConnect(object sender, ClientConnectArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPService.OnClientConnect(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (ClientConnect != null)
            {
                try
                {
                    ClientConnect(sender, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Service event error", ex);
                }
            }

            Clients.Enter();
            Clients[arg.Client.Channel] = arg.Client;
            Clients.Exit();
#if FUNCOUTLOG
			Log.WriteLog4("TCPService.OnClientConnect(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 客户端释放事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnClientDispose(object sender, ClientDisposeArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPService.OnClientDispose(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (ClientDispose != null)
            {
                try
                {
                    ClientDispose(sender, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Service event error", ex);
                }
            }
            try
            {
                Clients.Enter();
                Clients.Remove(arg.Client.Channel);
                //Log.WriteLog4("[Service.OnClientDispose]Remove Client IP:" + arg.Client.Channel.RemoteHost,LOGTYPE.INFO);
            }
            finally
            {
                Clients.Exit();
            }
            //关闭客户端
            arg.Client.Close();
#if FUNCOUTLOG
			Log.WriteLog4("TCPService.OnClientDispose(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 客户端异常事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnClientError(object sender, ClientErrorArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPService.OnClientError(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (ClientError != null)
            {
                try
                {
                    ClientError(sender, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Service event error", ex);
                }
            }
            try
            {
                Clients.Enter();
                Clients.Remove(arg.Client.Channel);
            }
            finally
            {
                Clients.Exit();
            }
            //关闭客户端
            arg.Client.Close();
#if FUNCOUTLOG
			Log.WriteLog4("TCPService.OnClientError(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通道服务中通道连接事件回调
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        private void OnChannelConnect(object sender, ChannelConnectArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPService.OnChannelConnect(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (arg.SocketError == SocketError.Success)
            {
                Client client = CreateClientByChannel(arg.Channel);
                if (client != null)
                {
                    OnClientConnect(this, new ClientConnectArg(client));
                    try
                    {
                        arg.Channel.StartReceiveAsync();
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message == "等待连接超时！")
                        {
                            Clients.Remove(arg.Channel);
                        }
                        throw ex;
                    }
                }
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPService.OnChannelConnect(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通道服务中通道释放事件回调
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        private void OnChannelDispose(object sender, ChannelDisposeArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPService.OnChannelDispose(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (Clients.ContainsKey(arg.Channel))
            {
                OnClientDispose(this, new ClientDisposeArg(Clients[arg.Channel]));
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPService.OnChannelDispose(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通道服务中通道异常事件回调
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        private void OnChannelError(object sender, ChannelErrorArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPService.OnChannelError(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (Clients.ContainsKey(arg.Channel))
            {
                OnClientError(this, new ClientErrorArg(Clients[arg.Channel]));
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPService.OnChannelError(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }
        #endregion

        #region 内部方法
        /// <summary>
        /// 通过连接通道创建客户端
        /// </summary>
        /// <param name="channel">连接通道</param>
        /// <param name="client">子类已创建客户端.如果非空,则基类在现有客户端实例上处理</param>
        /// <returns>创建的客户端实例.非空进创建成功,否则失败</returns>
        protected abstract Client CreateClientByChannel(Channel channel, Client client = null);
        #endregion
    }
}
