﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using SQ.Base;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Text;

namespace Network
{
    /// <summary>
    /// SocketAsyncEventArgs池, 会分配Buffer（不要buffer的SocketAsyncEventArgs，不在这取）
    /// </summary>
    public class SocketAsyncEventArgsPool
    {
        CV.Network.ObjectPool.BufferPool BufferManager;

        private static SocketAsyncEventArgsPool m_instance = null;
        public static SocketAsyncEventArgsPool GetInstance()
        {
            if (m_instance == null)
            {
                m_instance = new SocketAsyncEventArgsPool();
            }

            return m_instance;
        }


        /// <summary>
        /// 初始化SocketAsyncArg对象池
        /// </summary>
        /// <param name="capacity">最大连接数</param>
        private SocketAsyncEventArgsPool()
        {
            BufferManager = new CV.Network.ObjectPool.BufferPool();
        }

        /// <summary>
        /// 将SocketAsyncEventArgs对象入栈
        /// </summary>
        /// <param name="item"></param>
        public void Push(SocketAsyncEventArgs item)
        {
            BufferManager.Return(item.Buffer);
        }

        /// <summary>
        ///  将SocketAsyncEventArgs对象出栈
        /// </summary>
        /// <returns></returns>
        public SocketAsyncEventArgs Pop()
        {
            try
            {
                SocketAsyncEventArgs args = new SocketAsyncEventArgs();
                //args.UserToken = UserToken;
                var buff = BufferManager.Get();
                args.SetBuffer(buff,0, buff.Length);
                return args;

            }
            catch (Exception ex)
            {
                ErrorLog.WriteLog4Ex("SocketAsyncEventArgsPool::Pop failed", ex);
            }
            return null;

        }

    }
}

#endif