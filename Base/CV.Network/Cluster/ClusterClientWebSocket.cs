﻿using Network;
using SQ.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;

namespace CV.Network.Cluster
{
    public class ClusterClientWebSocket
    {
        public delegate void DelegateClusterResult(string addr, int port);
        /// <summary>
        /// 单例
        /// </summary>
        public static ClusterClientWebSocket Instance = new ClusterClientWebSocket();
        static ClusterClientWebSocket()
        {

        }

        string m_addr = null;
        int m_port;
        Dictionary<string, ClusterClientInfo> m_channels = new Dictionary<string, ClusterClientInfo>();
        DelegateClusterResult m_delegate_server_info;
        /// <summary>
        /// 超时检查线程
        /// </summary>
        private ThreadWhile<ClusterClientWebSocket> m_auto_timeoutcheck = null;

        /// <summary>
        /// 设置集群代理端口
        /// </summary>
        /// <param name="addr"></param>
        /// <param name="port"></param>
        public void SetRemoteInfo(string addr, int port)
        {
            m_addr = addr;
            m_port = port;
        }

        /// <summary>
        /// 启动自动检查timeout线程
        /// </summary>
        public void StartAutoCheck()
        {
            if (m_auto_timeoutcheck != null)
                return;

            m_auto_timeoutcheck = new ThreadWhile<ClusterClientWebSocket>();
            m_auto_timeoutcheck.SleepMs = 2000;
            m_auto_timeoutcheck.Start((tag, cancellationToken) => { TimeoutCheck(); }, this, "ClusterTimeoutCheck");

        }

        /// <summary>
        /// 检查请求超时
        /// </summary>
        public void TimeoutCheck()
        {
            lock (m_channels)
            {
                var keys = m_channels.Keys.ToArray();
                foreach (var key in keys)
                {
                    if (m_channels.ContainsKey(key))
                    {
                        var item = m_channels[key];
                        if ((DateTime.Now - item.m_create_time).TotalSeconds > 10)
                        {
                            item.result(null, 0);
                            m_channels.Remove(key);
                            item.channel.Close();
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="Type"></param>
        /// <param name="Tag"></param>
        /// <returns></returns>
        public void GetBest(int Type, DelegateClusterResult fun_result, string Tag = null)
        {
            if (m_addr == null)
            {
                fun_result(null, 0);
                return;
            }
            try
            {
                WebSocketChannel websocket = new WebSocketChannel(m_addr, m_port);
                var id = Guid.NewGuid().ToString();
                websocket.Tag = id;
                ClusterClientInfo info = new ClusterClientInfo();
                info.type = Type;
                info.tag = Tag;
                info.channel = websocket;
                info.result = fun_result;

                lock (m_channels)
                    m_channels[id] = info;

                websocket.ChannelConnect += Cluster_ChannelConnect;
                websocket.ChannelDispose += Cluster_ChannelDispose;
                websocket.ChannelHandShake += Cluster_ChannelHandShake;
                websocket.DataReceive += Cluster_Receive;
                websocket.Connect();
            }
            catch (Exception ex)
            {
                ErrorLog.WriteLog4Ex("GetBest", ex);
            }

        }

        private void Cluster_ChannelConnect(object sender, ChannelConnectArg arg)
        {
            if (arg.SocketError == System.Net.Sockets.SocketError.Success)
            {

            }
            else
            {
                var key = ((WebSocketChannel)sender).Tag.ToString();

                lock (m_channels)
                {
                    if (m_channels.ContainsKey(key))
                    {
                        //连接集群失败
                        var info = m_channels[key];
                        info.result(null, 0);
                        m_channels.Remove(key);
                    }
                }
            }
        }

        private void Cluster_ChannelDispose(object sender, ChannelDisposeArg arg)
        {
            var key = ((WebSocketChannel)sender).Tag.ToString();
            lock (m_channels)
            {
                if (m_channels.ContainsKey(key))
                {
                    //断开连接
                    var info = m_channels[key];
                    info.result(null, 0);
                    m_channels.Remove(key);
                }
            }
        }

        private void Cluster_ChannelHandShake(object sender, ChannelReceiveArg arg)
        {
            ClusterClientInfo info;
            var key = ((WebSocketChannel)sender).Tag.ToString();
            if (m_channels.ContainsKey(key))
            {
                info = m_channels[key];
            }
            else
            {
                return;
            }

            //发送请求
            var send = info.MakeRequest();
            info.channel.Send(send, 0, send.Length);
        }

        private void Cluster_Receive(object sender, ChannelReceiveArg arg)
        {
            int remotePort = 0;
            string remoteHost = null;

            try
            {
                byte[] recv = new byte[arg.BufferSize];
                Array.Copy(arg.Buffer, arg.BufferOffset, recv, 0, arg.BufferSize);
                var conninfo = ByteHelper.GBKToString(recv);

                Regex reg = new Regex("(.+):(.+)");
                Match match = reg.Match(conninfo);
                if (match.Success)
                {
                    remoteHost = match.Groups[1].Value;
                    remotePort = int.Parse(match.Groups[2].Value);
                    //test
                    //remoteHost = "127.0.0.1";
                    //remotePort = 18000;
                }
            }
            catch (Exception ex)
            {
                ErrorLog.WriteLog4Ex("Cluster_Receive", ex);
            }
            finally
            {
                var key = ((WebSocketChannel)sender).Tag.ToString();
                lock (m_channels)
                {
                    if (m_channels.ContainsKey(key))
                    {
                        var info = m_channels[key];
                        info.result(remoteHost, remotePort);
                        m_channels.Remove(key);
                        info.channel.Close();
                    }
                }

            }

        }

        class ClusterClientInfo
        {
            public int type;
            public string tag;
            public WebSocketChannel channel;
            public DateTime m_create_time = DateTime.Now;
            public DelegateClusterResult result;

            public byte[] MakeRequest()
            {
                byte[] tmp_tag = ByteHelper.StringToGBK(tag);
                int leng = tmp_tag.Length + 1 + 4;

                byte[] send = new byte[leng];
                send[0] = 1;
                ByteHelper.IntToByte4(type, send, 1);
                Array.Copy(tmp_tag, 0, send, 5, tmp_tag.Length);

                return send;
            }
        }

    }
}
