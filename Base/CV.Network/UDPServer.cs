﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;
using Network;
using SQ.Base;

namespace Network
{
    public class UDPServer : Server
    {
        /// <summary>
        /// 连接,key:ip-port
        /// </summary>
        public Dictionary<string, UDPChannel> m_channels = new Dictionary<string, UDPChannel>();

        /// <summary>
        /// socket
        /// </summary>
        public Socket m_listen_socket = null;
        /// <summary>
        /// 监听ip
        /// </summary>
        public string m_listen_ip;
        /// <summary>
        /// 监听端口
        /// </summary>
        public int m_listen_port;

        public UDPServer(string ip, int port)
        {
            m_listen_ip = ip;
            m_listen_port = port;
        }

        public override bool Start()
        {
            try
            {
                if (m_listen_socket == null)
                {
                    m_listen_socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
                    m_listen_socket.Bind(new IPEndPoint(IPAddress.Parse(m_listen_ip), m_listen_port));

                    SocketAsyncEventArgs e = SocketAsyncEventArgsPool.GetInstance().Pop();
                    e.RemoteEndPoint = new IPEndPoint(IPAddress.Any, 0);
                    e.Completed += IO_Completed;
                    ProcessReceive(e);
                }
                return true;
            }
            catch (Exception ex)
            {
                Log.WriteLog4Ex("UDPServer启动失败", ex);
                return false;
            }
        }

        protected void IO_Completed(object sender, SocketAsyncEventArgs e)
        {
            //Debug.WriteLine("UDP IO_Completed");
            switch (e.LastOperation)
            {
                case SocketAsyncOperation.ReceiveFrom:
                    OnReceiveFrom(e);
                    break;
            }
        }

        private void ProcessReceive(SocketAsyncEventArgs e)
        {
            ((IPEndPoint)e.RemoteEndPoint).Address = IPAddress.Any;
            ((IPEndPoint)e.RemoteEndPoint).Port = 0;
            if (!m_listen_socket.ReceiveFromAsync(e))
            {
                OnReceiveFrom(e);
            }
        }

        private void OnReceiveFrom(SocketAsyncEventArgs e)
        {
            //判断socket中是否有数据在传输&&判断是否有异常
            if (e.BytesTransferred > 0 && e.SocketError == SocketError.Success)
            {
                string key = e.RemoteEndPoint.ToString();
                string remote_ip = ((IPEndPoint)e.RemoteEndPoint).Address.ToString();
                var remote_port = ((IPEndPoint)e.RemoteEndPoint).Port;

                //string key = remote_ip + "-" + remote_port.ToString();

                UDPChannel channel = null;
                bool is_new = false;
                lock (m_channels)
                {
                    if (!m_channels.TryGetValue(key, out channel))
                    {
                        channel = new UDPChannel(m_listen_socket, remote_ip, remote_port, () => {
                            lock (m_channels)
                            {
                                m_channels.Remove(key);
                            }
                        });
                        m_channels[key] = channel;
                        is_new = true;
                    }
                }

                if (is_new)
                {
                    OnChannelConnect(this, new ChannelConnectArg(channel, e.SocketError));
                }

                channel.OnReceive(e);

                ProcessReceive(e);
            }
            else if (e.BytesTransferred == 0)//通道断开 
            {
                e.Completed -= IO_Completed;
                SocketAsyncEventArgsPool.GetInstance().Push(e);
            }
            else  //通道出错
            {
                e.Completed -= IO_Completed;
                SocketAsyncEventArgsPool.GetInstance().Push(e);
            }
        }


    }
}

#endif