﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;
using System.Net;
using System.Threading;
using SQ.Base;
using System.Collections.Concurrent;

namespace Network
{
    /// <summary>
    /// TCP通道
    /// </summary>
    /// <remarks>内部方法注释请参照基类</remarks>
    public class TCPChannel : Channel
    {
        /// <summary>
        /// 已启动接收
        /// </summary>
        private bool IsStartReceive;
        private WaitSendQueue waitSendQueue = new WaitSendQueue();
        /// <summary>
        /// 当前SocketAsyncEventArgs发送个数
        /// </summary>
        private int _NowSendIocpCount = 0;

        bool m_is_iocp_send_complete = true;

        #region 属性
        /// <summary>
        /// 单次发送字节数限制(会超)
        /// </summary>
        public int OnceSendBytesLimit
        {
            get { return waitSendQueue.OnceSendBytesLimit; }
            set { waitSendQueue.OnceSendBytesLimit = value; }
        }
        public bool Connected
        {
            get
            {
                if (Socket != null && Socket.Connected)
                    return true;
                else
                    return false;
            }
        }
        public int WaitSendDataSize
        {
            get
            {
                return waitSendQueue.WaitSendDataSize;
            }
        }

        public int NowSendIocpCount
        {
            get
            {
                return _NowSendIocpCount;
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 使用远程IP地址和端口号创建TCP通道
        /// </summary>
        /// <param name="remoteHost"></param>
        /// <param name="remotePort"></param>
        public TCPChannel(string remoteHost, int remotePort)
            : base(remoteHost, remotePort)
        {

        }

        /// <summary>
        /// 使用已连接套接字创建TCP通道
        /// </summary>
        /// <param name="socket">已连接套接字</param>
        public TCPChannel(Socket socket)
            : base(socket)
        {
        }

        public override void StartReceiveAsync()
        {
            if (IsStartReceive)
            {
                return;
            }
            IsStartReceive = true;

            SocketAsyncEventArgs e = Managers.SocketArgManager.Allocate(true);
            e.Completed += IO_Completed;

            DateTime dt = DateTime.Now;
            while (!Connected)
            {
                if (SQ.Base.DateTimeHelper.DiffNowSec(dt) > 10)
                {
                    e.Completed -= IO_Completed;
                    Managers.SocketArgManager.Free(e, true);
                    e.SocketError = SocketError.ConnectionAborted;
                    //如果10秒都未连接成功
                    throw new Exception("等待连接超时！");
                }
                else
                {
                    Thread.Sleep(100);
                }
            }
            try
            {
                while (!Socket.ReceiveAsync(e))//如果同步接收完成，直接执行，不用等待IO_Completed
                {
                    if (e.SocketError == SocketError.Success && e.BytesTransferred > 0)
                    {
                        DoReceive(e, new ChannelReceiveArg(e.Buffer, e.Offset, e.BytesTransferred));
                    }
                    else if (e.BytesTransferred == 0)//通道断开
                    {
                        e.Completed -= IO_Completed;
                        Managers.SocketArgManager.Free(e, true);

                        OnDispose(this, new ChannelDisposeArg(this, e.SocketError));
                        break;
                    }
                    else//通道出错
                    {
                        e.Completed -= IO_Completed;
                        Managers.SocketArgManager.Free(e, true);

                        OnError(this, new ChannelErrorArg(this, e.SocketError));
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                e.Completed -= IO_Completed;
                Managers.SocketArgManager.Free(e, true);
            }
        }

        public override void Connect()
        {
#if FUNCINLOG
			Log.WriteLog4("TCPChannel.Connect() in.", LOGTYPE.DEBUG);
#endif
            Socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
            SocketAsyncEventArgs e = Managers.SocketArgManager.Allocate(false);
            e.RemoteEndPoint = Utils.GetEndPoint(RemoteHost, RemotePort);
            //new IPEndPoint(IPAddress.Parse(RemoteHost), RemotePort);
            e.Completed += IO_Completed;

            IsStartReceive = false;

            if (!Socket.ConnectAsync(e))
            {
                OnConnect(e);
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPChannel.Connect() out.", LOGTYPE.DEBUG);
#endif
        }


        public bool IsIocpSendComplete()
        {
            return m_is_iocp_send_complete;
        }
        //public override void Connect(int timeout) { 
        //}
        /// <summary>
        /// 是否存在乱序问题?发送完成事件肯定不能保证顺序
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <param name="token"></param>
        public override void Send(byte[] data, int offset, int count)
        {
            if (data == null)
            {
                Log.WriteLog4("[Send]data=null", null, LOGTYPE.INFO);
                return;
            }
            if (data != null && data.Length == 0)
            {
                Log.WriteLog4("[Send]data.Length == 0", null, LOGTYPE.INFO);
                return;
            }
            if (Connected)
            {
                m_is_iocp_send_complete = false;

                Interlocked.Add(ref this._WriteBytes, count);

                if (Interlocked.CompareExchange(ref _NowSendIocpCount, 1, 0) == 0)
                {
                    SendBufferList lst = waitSendQueue.DequeueAll();
                    lst.AddItem(data, offset, count);
                    SendIocp(lst);
                }
                else
                {
                    waitSendQueue.Add(data, offset, count);
                    //添加进队列后再判断一次，防止添加过程中OnSend执行完毕
                    if (Interlocked.CompareExchange(ref _NowSendIocpCount, 1, 0) == 0)
                    {
                        SendBufferList lst = waitSendQueue.DequeueAll();
                        SendIocp(lst);
                    }
                }
            }
        }
        private void SendIocp(SendBufferList lst, SocketAsyncEventArgs e = null)
        {
            if (e == null)
            {
                e = Managers.SocketArgManager.Allocate(false);
                e.Completed += IO_Completed;
            }
            e.BufferList = lst;
            if (!Socket.SendAsync(e))
            {
                OnSend(e);
            }
        }
        public override void Close()
        {
            IsStartReceive = false;
            try
            {
                Socket?.Shutdown(SocketShutdown.Both);
            }
            catch
            {
            }
            try
            {
                Socket?.Close();//Close会自动调用Dispose,建立连接失败时Connected为false,如果不Close,会存在连接资源不释放的问题。
            }
            catch
            {
            }
            try
            {
                Socket?.Dispose();//Close会自动调用Dispose,建立连接失败时Connected为false,如果不Close,会存在连接资源不释放的问题。
            }
            catch
            {
            }
            OnDispose(this, new ChannelDisposeArg(this, SocketError.Disconnecting));
        }
        protected override void OnDispose(object sender, ChannelDisposeArg arg)
        {
            base.OnDispose(sender, arg);
#if NETSTANDARD2_1_OR_GREATER || NET5_0_OR_GREATER
            waitSendQueue.Clear();
#else
            waitSendQueue.DequeueAll();
#endif
        }

        /// <summary>
        /// 直接调用socket发送(未调用完成端口)
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="size"></param>
        public virtual int SendDirect(byte[] data, int offset, int size)
        {
            int ret = -1;
            if (Socket.Connected)
            {
                try
                {
                    ret = Socket.Send(data, offset, size, SocketFlags.None);
                }
                catch (Exception ex)
                {
                    //Log.WriteLog4Ex("TCPChannel::SendDirect", ex);
                    ret = -1;
                }

            }

            return ret;
        }
        /// <summary>
        /// 设置socket阻塞模式
        /// </summary>
        /// <param name="Blocking"></param>
        public void SetSocketOptBlocking(bool Blocking)
        {
            if (Socket != null)
                Socket.Blocking = Blocking;
        }
        /// <summary>
        /// 是否关闭tcp的延迟算法
        /// </summary>
        /// <param name=""></param>
        public void SetSocketOptDelay(bool Delay)
        {
            if (Socket != null)
                Socket.NoDelay = Delay;
        }
        /// <summary>
        /// 设置socket的发送缓冲区大小
        /// </summary>
        /// <param name="BufferSize"></param>
        public void SetSocketOptSendBufferSize(int BufferSize)
        {
            if (Socket != null)
                Socket.SendBufferSize = BufferSize;
        }
        #endregion

        #region 异步请求结果处理
        /// <summary>
        /// 发送数据异步请求结果处理
        /// </summary>
        /// <param name="e"></param>
        private void OnSend(SocketAsyncEventArgs e)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPChannel.OnSend(e) in.", LOGTYPE.DEBUG);
#endif
            ChannelSendArg arg;

            if (e.BufferList != null)
            {
                arg = new ChannelSendArg(e.UserToken, (SendBufferList)e.BufferList);
                e.BufferList = null;
            }
            else
            {
                arg = new ChannelSendArg(e.UserToken, e.Buffer, e.Offset, e.Count);
                e.SetBuffer(null, 0, 0);
            }

            OnSend(this, arg);

            if (waitSendQueue.IsEmpty)
            {
                Interlocked.Exchange(ref _NowSendIocpCount, 0);
                e.Completed -= IO_Completed;
                Managers.SocketArgManager.Free(e, false);
                m_is_iocp_send_complete = true;
            }
            else
            {
                SendBufferList lst = waitSendQueue.DequeueAll();
                if (lst.Count > 0 && Connected)
                {
                    SendIocp(lst, e);
                }
                else
                {
                    Interlocked.Exchange(ref _NowSendIocpCount, 0);
                    e.Completed -= IO_Completed;
                    Managers.SocketArgManager.Free(e, false);
                }
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPChannel.OnSend(e) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 接收数据异步请求结果处理
        /// </summary>
        /// <param name="e"></param>
        private void EReceive(SocketAsyncEventArgs e)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPChannel.EReceive(e) in.", LOGTYPE.DEBUG);
#endif
            if (e.SocketError == SocketError.Success && e.BytesTransferred > 0)//收到数据
            {
                DoReceive(e, new ChannelReceiveArg(e.Buffer, e.Offset, e.BytesTransferred));
                bool flag = true;//防止回收两次造成e重用的问题
                while (Connected && !Socket.ReceiveAsync(e))//如果同步接收完成，直接执行，不用等待下一次IO_Completed
                {
                    if (e.SocketError == SocketError.Success && e.BytesTransferred > 0)
                    {
                        DoReceive(e, new ChannelReceiveArg(e.Buffer, e.Offset, e.BytesTransferred));
                    }
                    else if (e.BytesTransferred == 0)//通道断开
                    {
                        flag = false;
                        e.Completed -= IO_Completed;
                        Managers.SocketArgManager.Free(e, true);

                        OnDispose(this, new ChannelDisposeArg(this, e.SocketError));
                        //break;//无需break，下次循环Connected 为false
                    }
                    else//通道出错
                    {
                        flag = false;
                        e.Completed -= IO_Completed;
                        Managers.SocketArgManager.Free(e, true);

                        OnError(this, new ChannelErrorArg(this, e.SocketError));
                        //break;//无需break，下次循环Connected 为false
                    }
                }

                if (!Connected && flag)
                {
                    e.Completed -= IO_Completed;
                    Managers.SocketArgManager.Free(e, true);

                    OnDispose(this, new ChannelDisposeArg(this, e.SocketError));
                }
            }
            else if (e.BytesTransferred == 0)//通道断开
            {
                e.Completed -= IO_Completed;
                Managers.SocketArgManager.Free(e, true);

                OnDispose(this, new ChannelDisposeArg(this, e.SocketError));
            }
            else//通道出错
            {
                e.Completed -= IO_Completed;
                Managers.SocketArgManager.Free(e, true);

                OnError(this, new ChannelErrorArg(this, e.SocketError));
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPChannel.EReceive(e) out.", LOGTYPE.DEBUG);
#endif
        }

        protected virtual void DoReceive(SocketAsyncEventArgs e, ChannelReceiveArg arg)
        {
            OnReceive(this, arg);
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
            arg.ChangeBuffer(e);
#endif
        }

        /// <summary>
        /// 通道连接异步请求结果处理(通过IP地址和端口号创建通道时，通道连接返回后触发)
        /// </summary>
        /// <param name="e"></param>
        private void OnConnect(SocketAsyncEventArgs e)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPChannel.OnConnect(e) in.", LOGTYPE.DEBUG);
#endif
            if (e.SocketError == SocketError.Success)
            {
                StartReceiveAsync();
            }

            OnConnect(this, new ChannelConnectArg(this, e.SocketError));

            e.Completed -= IO_Completed;
            Managers.SocketArgManager.Free(e, false);

#if FUNCOUTLOG
			Log.WriteLog4("TCPChannel.OnConnect(e) out.", LOGTYPE.DEBUG);
#endif
        }
        #endregion

        #region 内部方法
        /// <summary>
        /// 异步请求回调处理方法
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void IO_Completed(object sender, SocketAsyncEventArgs e)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPChannel.IO_Completed(sender, e) in.", LOGTYPE.DEBUG);
#endif
            try
            {
                switch (e.LastOperation)
                {
                    case SocketAsyncOperation.Connect:
                        OnConnect(e);
                        break;
                    case SocketAsyncOperation.Receive:
                        EReceive(e);
                        break;
                    case SocketAsyncOperation.Send:
                        OnSend(e);
                        break;
                    default:
                        //e.SetBuffer(null, 0, 0);
                        e.Completed -= IO_Completed;
                        Managers.SocketArgManager.Free(e, false);
                        break;
                }
            }
            catch (Exception ex)
            {
                Log.WriteLog4Ex("TCPChannel.IO_Completed(sender, e) error", ex);
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPChannel.IO_Completed(sender, e) out.", LOGTYPE.DEBUG);
#endif
        }

        #endregion
    }
}
