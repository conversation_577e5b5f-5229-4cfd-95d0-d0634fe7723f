﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.1;netstandard2.0;net45</TargetFrameworks>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="SendQueue.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CV.Base\CV.Base.csproj" />
  </ItemGroup>
  <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.0'">
    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="8.0.5" />
  </ItemGroup>
  <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.1'">
    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="8.0.5" />
  </ItemGroup>

</Project>
