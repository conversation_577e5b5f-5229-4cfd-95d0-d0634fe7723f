﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;
using System.Net;
using SQ.Base;

namespace Network
{
    /// <summary>
    /// TCP通道服务
    /// </summary>
    /// <remarks>内部方法注释请参照基类</remarks>
    public class TCPServer : Server
    {
        #region 公共方法
        /// <summary>
        /// TCP通道服务构造函数
        /// </summary>
        /// <param name="localHost">本地绑定网卡地址</param>
        /// <param name="localPort">本地端口号</param>
        public TCPServer(string localHost, int localPort)
        {
            LocalHost = localHost;
            LocalPort = localPort;
        }

        /// <summary>
        /// 开启服务器
        /// </summary>
        /// <returns>成功返回true,否则返回false</returns>
        public override bool Start()
        {
            try
            {
                if (LocalSocket == null)
                {
                    LocalSocket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                    LocalSocket.Bind(new IPEndPoint(IPAddress.Parse(LocalHost), LocalPort));
                    LocalSocket.Listen(64);

                    SocketAsyncEventArgs e = Managers.SocketArgManager.Allocate(false);
                    e.Completed += IO_Completed;
                    if (LocalSocket != null && !LocalSocket.AcceptAsync(e))
                    {
                        OnConnect(e);
                    }
                    return base.Start();
                }
                else
                    return false;
            }
            catch (Exception ex)
            {
                Log.WriteLog4Ex("Server启动失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止服务器
        /// </summary>
        public override void Stop()
        {
            try
            {
                if (LocalSocket != null)
                {
                    LocalSocket.Close();
                    LocalSocket.Dispose();
                    LocalSocket = null;

                    base.Stop();
                }
                else
                    Log.WriteLog4("Server停止失败.服务已停止.", LOGTYPE.WARN);
            }
            catch (Exception ex)
            {
                Log.WriteLog4Ex("Server停止失败", ex);
            }
        }
        #endregion

        #region 异步请求结果处理
        /// <summary>
        /// 通道连接IO完成时调用
        /// </summary>
        /// <param name="e"></param>
        private void OnConnect(SocketAsyncEventArgs e)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPServer.OnConnect(e) in.", LOGTYPE.DEBUG);
#endif
            ConnectNew(e);

            while (LocalSocket != null && !LocalSocket.AcceptAsync(e))
            {
                ConnectNew(e);
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPServer.OnConnect(e) out.", LOGTYPE.DEBUG);
#endif
        }

        private void ConnectNew(SocketAsyncEventArgs e)
        {
            try
            {
                Channel channel = null;
                if (e.SocketError == SocketError.Success)
                    channel = CreateChannelBySocket(e.AcceptSocket);
                e.AcceptSocket = null;
                OnChannelConnect(this, new ChannelConnectArg(channel, e.SocketError));
            }
            catch (Exception ex)
            {
                Log.WriteLog4Ex("NewConnect", ex);
            }

        }
        #endregion

        #region 内部方法
        /// <summary>
        /// 异步请求回调处理方法
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void IO_Completed(object sender, SocketAsyncEventArgs e)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPServer.IO_Completed(sender, e) in.", LOGTYPE.DEBUG);
#endif
            try
            {
                switch (e.LastOperation)
                {
                    case SocketAsyncOperation.Accept:
                        OnConnect(e);
                        break;
                }
            }
            catch (Exception ex)
            {
                Log.WriteLog4Ex("TCPServer.IO_Completed(sender, e) error", ex);
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPServer.IO_Completed(sender, e) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通过连接套接字创建通道
        /// </summary>
        /// <param name="socket">连接套接字</param>
        /// <param name="channel">子类已创建通道.如果非空,则基类在现有通道实例上处理</param>
        /// <returns>创建的通道实例.非空进创建成功,否则失败</returns>
        protected override Channel CreateChannelBySocket(Socket socket, Channel channel = null)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPServer.CreateChannelBySocket(socket) in.", LOGTYPE.DEBUG);
#endif
            if (channel == null)
                channel = new TCPChannel(socket);
#if FUNCOUTLOG
			Log.WriteLog4("TCPServer.CreateChannelBySocket(socket) out.", LOGTYPE.DEBUG);
#endif
            return base.CreateChannelBySocket(socket, channel);
        }
        #endregion
    }
}
