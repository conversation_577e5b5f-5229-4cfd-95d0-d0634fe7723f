﻿
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using Network;
using SQ.Base;
using System;
using System.Collections.Generic;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CV.Network.SSL
{
    public class SSLWsHander
    {
        SSLStatus status = SSLStatus.Default;
        X509Certificate2 serverCertificate;
        SslStream ssl;
        WsStream stream;
        WebSocketChannel ws;
        ReaderWriterLockSlim readerWriterLock = new ReaderWriterLockSlim();

        byte[] buff;
        int flag;
        public SSLWsHander(WebSocketChannel ws, X509Certificate2 serverCertificate)
        {
            this.ws = ws;
            this.stream = new WsStream(this);
            this.serverCertificate = serverCertificate;
            ssl = new System.Net.Security.SslStream(stream, false, userCertificateValidationCallback, userCertificateSelectionCallback, EncryptionPolicy.AllowNoEncryption);
            ws.ChannelDispose += Ws_ChannelDispose;
            buff = new byte[32768];
            flag = 0;
        }

        private X509Certificate userCertificateSelectionCallback(object sender, string targetHost, X509CertificateCollection localCertificates, X509Certificate remoteCertificate, string[] acceptableIssuers)
        {
            return serverCertificate;
        }

        private bool userCertificateValidationCallback(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            return true;
            if (sslPolicyErrors != SslPolicyErrors.None && sslPolicyErrors != SslPolicyErrors.RemoteCertificateChainErrors)
                return false;
            if (sslPolicyErrors != SslPolicyErrors.RemoteCertificateChainErrors)
            {
                //不判断吊销证书
                foreach (X509ChainStatus s in chain.ChainStatus)
                {
                    Console.WriteLine("ValidateClientCertificate-chain.ChainStatus:\r\n{0}-{1}", s.Status, s.StatusInformation);
                    if (s.Status != X509ChainStatusFlags.OfflineRevocation && s.Status != X509ChainStatusFlags.RevocationStatusUnknown)
                    {
                        return false;
                    }
                }
            }
        }

        private void Ws_ChannelDispose(object sender, ChannelDisposeArg arg)
        {
            stream.Complete();
        }
        private async Task DoBeginRead(object sender)
        {
            if (Interlocked.CompareExchange(ref flag, 1, 0) == 0)
            {
                try
                {
                    var len = await ssl.ReadAsync(buff, 0, buff.Length);
                    ws.ReceiveHand(sender, new ChannelReceiveArg(buff, 0, len));
                    Interlocked.Exchange(ref flag, 0);
                    await DoBeginRead(sender);
                }
                catch (Exception EX)
                {
                    Interlocked.Exchange(ref flag, 0);
                }
            }
        }
        //private void DoBeginRead(object sender)
        //{
        //    if (Interlocked.CompareExchange(ref flag, 1, 0) == 0)
        //    {
        //        ssl.BeginRead(buff, 0, buff.Length, asy =>
        //        {
        //            try
        //            {
        //                var len = ssl.EndRead(asy);
        //                ws.ReceiveHand(sender, new ChannelReceiveArg(buff, 0, len));
        //                Interlocked.Exchange(ref flag, 0);
        //                DoBeginRead(sender);
        //            }
        //            catch (Exception EX)
        //            {
        //                Interlocked.Exchange(ref flag, 0);
        //            }
        //        }, null);
        //    }

        //}
        public void Receive(object sender, ChannelReceiveArg arg)
        {
#if DEBUG
            Console.WriteLine($"R[{status}]:" + SQ.Base.ByteHelper.BytesToHexString(arg.Buffer, arg.BufferOffset, arg.BufferSize));
#endif
            stream.SetNowChannelReceiveArg(arg);

            if (status == SSLStatus.Default)
            {
                AuthenticateAsServerAsync();
            }
            else
            {

                try
                {
                    readerWriterLock.EnterReadLock();
                    if (status == SSLStatus.Authenticated)
                    {
                        //Log.WriteLog4("SSL Authenticated", LOGTYPE.DEBUG);
                        DoBeginRead(sender);
                    }
#if DEBUG
                    else
                    {
                        Console.WriteLine("Authenticating");
                    }
#endif
                }
                finally
                {
                    readerWriterLock.ExitReadLock();
                }
            }
        }
        protected void AuthenticateAsServerAsync()
        {
            status = SSLStatus.Authenticating;
#if DEBUG
            Console.WriteLine("BeginAuthenticateAsServer");
#endif
            ssl.BeginAuthenticateAsServer(serverCertificate, false, System.Security.Authentication.SslProtocols.Tls12, false, ar =>
              {
                  try
                  {
#if DEBUG
                      Console.WriteLine("EndAuthenticateAsServer");
#endif
                      ssl.EndAuthenticateAsServer(ar);
                      readerWriterLock.EnterWriteLock();
                      status = SSLStatus.Authenticated;

                      var bts = new byte[2048];
                      var len = ssl.Read(bts, 0, bts.Length);
                      ws.ReceiveHand(ws, new ChannelReceiveArg(bts, 0, len));

                  }
                  catch (Exception ex)
                  {
                      status = SSLStatus.AuthenticatedError;
                      Log.WriteLog4Ex("AuthenticateAsServer", ex);
                      ws.Close();
                  }
                  finally
                  {
                      if (readerWriterLock.IsWriteLockHeld)
                      {
                          readerWriterLock.ExitWriteLock();
                      }
                  }

              }, ssl);


        }
        //        protected async void AuthenticateAsServerAsync()
        //        {
        //            status = SSLStatus.Authenticating;
        //#if DEBUG
        //            Console.WriteLine("BeginAuthenticateAsServer");
        //#endif
        //            await ssl.AuthenticateAsServerAsync(serverCertificate, false, System.Security.Authentication.SslProtocols.Tls12, false);

        //            try
        //            {
        //#if DEBUG
        //                Console.WriteLine("EndAuthenticateAsServer");
        //#endif
        //                readerWriterLock.EnterWriteLock();
        //                status = SSLStatus.Authenticated;

        //                var bts = new byte[2048];
        //                var len = ssl.Read(bts, 0, bts.Length);
        //                ws.ReceiveHand(ws, new ChannelReceiveArg(bts, 0, len));

        //            }
        //            catch (Exception ex)
        //            {
        //                status = SSLStatus.AuthenticatedError;
        //                Log.WriteLog4Ex("AuthenticateAsServer", ex);
        //                ws.Close();
        //            }
        //            finally
        //            {
        //                if (readerWriterLock.IsWriteLockHeld)
        //                {
        //                    readerWriterLock.ExitWriteLock();
        //                }
        //            }
        //        }

        object sslLock = new object();
        public int Send(byte[] data, int offset, int size)
        {
            lock (sslLock)
            {
                ssl.Write(data, offset, size);
                return size;
            }
        }

        public void SendRaw(byte[] data, int offset, int size)
        {
#if DEBUG
            Console.WriteLine("S:" + SQ.Base.ByteHelper.BytesToHexString(data, offset, size));
#endif
            ws.SendRaw(data, offset, size);
        }
    }
}
#endif