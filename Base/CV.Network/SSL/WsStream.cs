﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using Network;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace CV.Network.SSL
{
    public class WsStream : Stream
    {
        System.IO.Pipelines.Pipe pipe = new System.IO.Pipelines.Pipe();
        bool isComplete;
        public WsStream(SSLWsHander ssl)
        {
            this.ssl = ssl;
        }
        public long _Position;
        //private ChannelReceiveArg nowArg;
        private SSLWsHander ssl;

        public override bool CanRead => true;

        public override bool CanSeek => false;

        public override bool CanWrite => true;

        public override long Length => -1;

        public override long Position
        {
            get
            {
                return _Position;
            }
            set
            {
                //_Position = value;
            }
        }

        public override void Flush()
        {

        }


        public void SetNowChannelReceiveArg(ChannelReceiveArg data)
        {
            pipe.Writer.WriteAsync(new ReadOnlyMemory<byte>(data.Buffer, data.BufferOffset, data.BufferSize));
        }
        public override int Read(byte[] buffer, int offset, int count)
        {
            //if (isComplete)
            //{
            //    return 0;
            //}
            return pipe.Reader.AsStream().Read(buffer, offset, count);

            //if (!ws.Connected)
            //{
            //    return 0;
            //}
            //while (ms.le)
            //{

            //}
            //if (nowArg == null)
            //{
            //    slim.Reset();
            //    slim.WaitOne();
            //    slim.Reset();
            //}
            //var len = count > nowArg.BufferSize - offset ? nowArg.BufferSize : count;

            //Array.Copy(nowArg.Buffer, nowArg.BufferOffset + Position, buffer, offset, len);
            //Position += count;
            //return len;
        }

        public override long Seek(long offset, SeekOrigin origin)
        {
            return _Position;
        }

        public override void SetLength(long value)
        {
        }

        public override void Write(byte[] buffer, int offset, int count)
        {
            //nowArg = null;
            ssl.SendRaw(buffer, offset, count);
        }

        public void Complete()
        {
            isComplete = true;
            pipe.Reader.CancelPendingRead();
            pipe.Reader.Complete();
            pipe.Writer.CancelPendingFlush();
            pipe.Writer.Complete();
        }
    }
}

#endif