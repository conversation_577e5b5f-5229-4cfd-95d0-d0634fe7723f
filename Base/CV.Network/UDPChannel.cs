﻿using Network;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;

namespace Network
{
    public delegate void ChannelCloseCB();
    public class UDPChannel : Channel
    {
        ChannelCloseCB m_cb;
        IPEndPoint m_endpotin;
        public UDPChannel(Socket s, string ip, int port, ChannelCloseCB cb) : base(s, ip, port)
        {
            m_endpotin = new IPEndPoint(IPAddress.Parse(ip), port);
            m_cb = cb;
        }

        public void OnReceive(SocketAsyncEventArgs e)
        {
            OnReceive(this, new ChannelReceiveArg(e<PERSON><PERSON><PERSON><PERSON>, e.Offset, e.BytesTransferred));
        }


        public override void Close()
        {
            m_cb?.Invoke();
        }

        public override void StartReceiveAsync()
        {
            return;
        }
        public override void Connect()
        {
            throw new NotImplementedException();
        }

        public override void Send(byte[] data, int offset, int count)
        {
            Socket.SendTo(data, offset, count, SocketFlags.None, m_endpotin);
        }


    }
}
