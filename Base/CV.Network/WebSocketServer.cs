﻿using Network;
using SQ.Base;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;

namespace CV.Network
{
    public class WebSocketServer : TCPServer
    {

        public X509Certificate2 ServerCertificate { get; set; }

        public WebSocketServer(string localHost, int localPort, X509Certificate2 ServerCertificate
            )
            : base(localHost, localPort)
        {
            this.ServerCertificate = ServerCertificate;
        }

        protected override Channel CreateChannelBySocket(Socket socket, Channel channel = null)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPServer.CreateChannelBySocket(socket) in.", LOGTYPE.DEBUG);
#endif
            if (channel == null)
            {
#if NETFRAMEWORK
                channel = new WebSocketChannel(socket);
#else

                channel = new WebSocketChannel(socket)
                {
                    serverCertificate = ServerCertificate
                };
#endif
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPServer.CreateChannelBySocket(socket) out.", LOGTYPE.DEBUG);
#endif
            return base.CreateChannelBySocket(socket, channel);
        }


    }
}

