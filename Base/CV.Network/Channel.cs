﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;
using SQ.Base;
using System.Net;

namespace Network
{
    public delegate void EventChannelReceive(object sender, ChannelReceiveArg arg);
    public delegate void EventChannelSend(object sender, ChannelSendArg arg);
    public delegate void EventChannelConnect(object sender, ChannelConnectArg arg);
    public delegate void EventChannelDispose(object sender, ChannelDisposeArg arg);
    public delegate void EventChannelError(object sender, ChannelErrorArg arg);

    /// <summary>
    /// 连接通道
    /// </summary>
    public abstract class Channel : IDisposable
    {
        #region 属性
        public bool IsDispose { get; protected set; }
        /// <summary>
        /// 接受数据字节数
        /// </summary>
        public long ReceiveBytes { get; protected set; }
        protected long _SendBytes, _WriteBytes;
        /// <summary>
        /// 发送数据字节数(包含写入缓冲区的)
        /// </summary>
        public long WriteBytes
        {
            get
            {
                return _WriteBytes;
            }
        }
        /// <summary>
        /// 已发送数据字节数
        /// </summary>
        public long SendBytes { get { return _SendBytes; } }
        public long RecvBytes = 0;
        //private ChannelQueue SendQ;
        /// <summary>
        /// 套接字
        /// </summary>
        public Socket Socket { get; protected set; }
        /// <summary>
        /// 远程端口
        /// </summary>
        public int RemotePort { get; set; }
        /// <summary>
        /// 远程IP地址
        /// </summary>
        public string RemoteHost { get; set; }

        public int LocalPort { get; set; }

        /// <summary>
        /// 通道状态
        /// </summary>
        public ChannelState ChannelState { get; protected set; }
        /// <summary>
        /// 用户标识对象
        /// </summary>
        public object Tag { get; set; }
        /// <summary>
        /// 数据发送完成委托
        /// </summary>
        public EventChannelSend DataSend;
        /// <summary>
        /// 收到数据委托
        /// </summary>
        public EventChannelReceive DataReceive;
        /// <summary>
        /// 通道错误事件
        /// </summary>
        public event EventChannelError ChannelError;
        /// <summary>
        /// 通道断开事件
        /// </summary>
        public event EventChannelDispose ChannelDispose;
        /// <summary>
        /// 通道连接事件
        /// </summary>
        public event EventChannelConnect ChannelConnect;
        #endregion

        #region 公共方法
        /// <summary>
        /// 用已连接套接字构造通道
        /// </summary>
        /// <param name="socket">连接套接字</param>
        public Channel(Socket socket)
        {
            Socket = socket;

            IPEndPoint endPoint = socket.LocalEndPoint as IPEndPoint;
            LocalPort = endPoint.Port;

            endPoint = Socket.RemoteEndPoint as IPEndPoint;
            RemoteHost = endPoint.Address.ToString();
            RemotePort = endPoint.Port;

            //if (Utils.SQS != null)
            //{
            //    SendQ = Utils.GetSendQueue().AddChannelQueue(this);
            //}
        }

        public Channel(Socket socket, string remote_ip, int remote_port)
        {
            Socket = socket;

            IPEndPoint endPoint = socket.LocalEndPoint as IPEndPoint;
            LocalPort = endPoint.Port;

            RemoteHost = remote_ip;
            RemotePort = remote_port;
        }

        /// <summary>
        /// 用IP地址和端口号构造通道
        /// </summary>
        /// <param name="remoteHost">远程IP地址</param>
        /// <param name="remotePort">端口号</param>
        public Channel(string remoteHost, int remotePort)
        {
            RemoteHost = remoteHost;
            RemotePort = remotePort;

            //if (Utils.SQS != null)
            //{
            //    SendQ = Utils.GetSendQueue().AddChannelQueue(this);
            //}
        }

        /// <summary>
        /// 释放通道
        /// </summary>
        public abstract void Close();

        /// <summary>
        /// 连接
        /// </summary>
        public abstract void Connect();

        /// <summary>
        /// 抛送异步请求(Server类中创建通道之后调用,主动连接则不用调用)
        /// </summary>
        public abstract void StartReceiveAsync();



        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="token">令牌.数据发送完成后在发送数据参数中返回</param>
        public virtual void Send(byte[] data)
        {
            Send(data, 0, data.Length);
        }
        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="offset">偏移量</param>
        /// <param name="count">长度</param>
        /// <param name="token">令牌.数据发送完成后在发送数据参数中返回</param>
        public abstract void Send(byte[] data, int offset, int count);

        ///// <summary>
        ///// 获得发送队列长度
        ///// </summary>
        ///// <returns></returns>
        //public int GetSendQueueCount()
        //{
        //    return SendQ.DataQueue.Count;
        //}

        //public void SendAsync(byte[] data)
        //{
        //    SendAsync(data, 0, data.Length);
        //}
        //public void SendAsync(byte[] array, int offset, int count)
        //{
        //    if (SendQ.DataQueue.Count > 2000) SQ.Base.Log.WriteLog4("SendQ队列长度:" + SendQ.DataQueue.Count.ToString());
        //    SendQ.Enqueue(new ArraySegment<byte>(array, offset, count));
        //}
        #endregion

        #region 事件处理
        /// <summary>
        /// 通道连接事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnConnect(object sender, ChannelConnectArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("Channel.OnConnect(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (ChannelConnect != null)
            {
                try
                {
                    ChannelConnect(sender, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Channel event error", ex);
                }
            }
#if FUNCOUTLOG
			Log.WriteLog4("Channel.OnConnect(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通道释放事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnDispose(object sender, ChannelDisposeArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("Channel.OnDispose(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (ChannelDispose != null)
            {
                try
                {
                    ChannelDispose(sender, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Channel event error", ex);
                }
            }
#if FUNCOUTLOG
			Log.WriteLog4("Channel.OnDispose(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通道出错事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnError(object sender, ChannelErrorArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("Channel.OnError(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (ChannelError != null)
            {
                try
                {
                    ChannelError(sender, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Channel event error", ex);
                }
            }
#if FUNCOUTLOG
			Log.WriteLog4("Channel.OnError(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通道收到数据事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnReceive(object sender, ChannelReceiveArg arg)
        {
            if (arg != null && arg.Buffer != null)
            {
                ReceiveBytes += arg.BufferSize;
            }
#if FUNCINLOG
			Log.WriteLog4("Channel.OnReceive(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (DataReceive != null)
            {
                try
                {
                    DataReceive?.Invoke(sender, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Channel event error", ex);
                }
            }
#if FUNCOUTLOG
			Log.WriteLog4("Channel.OnReceive(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通道数据发送完成事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnSend(object sender, ChannelSendArg arg)
        {
            if (arg != null)
            {
                System.Threading.Interlocked.Add(ref this._SendBytes, arg.BytesTransferred);

            }
#if FUNCINLOG
			Log.WriteLog4("Channel.OnSend(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (DataSend != null)
            {
                try
                {
                    DataSend(sender, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Channel event error", ex);
                }
            }
#if FUNCOUTLOG
			Log.WriteLog4("Channel.OnSend(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }
        ~Channel()
        {
            Dispose();
        }
        public void Dispose()
        {
            if (IsDispose)
            {
                return;
            }
            IsDispose = true;
            //if (Utils.SQS != null)
            //{
            //    SendQ.RemoveChannelQueue();
            //}
        }
        #endregion
    }
}
