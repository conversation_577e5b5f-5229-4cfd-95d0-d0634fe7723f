﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;

namespace Network
{
    public class Utils
    {
        //static int p;
        //public static SendQueue[] SQS = null;
        //static bool Setuped = false;
        //public static void Initialize()
        //{
        //    if (SQS == null || !Setuped)
        //    {
        //        Setup();
        //    }
        //}

        //public static void Setup(int sendThread = 0)
        //{
        //    if (sendThread <= 0)
        //    {
        //        sendThread = Environment.ProcessorCount;
        //    }
        //    SQS = new Network.SendQueue[sendThread];
        //    for (int i = 0; i < sendThread; i++)
        //    {
        //        SQS[i] = new SendQueue();

        //        Thread th = new Thread(SQS[i].RunSend);
        //        th.IsBackground = true;
        //        th.Name = "RunSend" + i;
        //        th.Start();
        //    }


        //    Setuped = true;


        //}



        //public static SendQueue GetSendQueue()
        //{
        //    lock (SQS)
        //    {
        //        p++;
        //        if (p >= SQS.Length)
        //        {
        //            p = 0;
        //        }
        //        return SQS[p];
        //    }
        //}

        static System.Text.RegularExpressions.Regex reg = new System.Text.RegularExpressions.Regex(@"\d+\.\d+\.\d+\.\d+");
        public static EndPoint GetEndPoint(string Host, int Port)
        {
            if (IsIP(Host))
            {
                return new IPEndPoint(IPAddress.Parse(Host), Port);
            }
            else
            {
                return new DnsEndPoint(Host, Port);

            }
        }
        public static bool IsIP(string Host)
        {
            return reg.IsMatch(Host);
        }
        public static string DomainToIP(string Domain)
        {
            IPHostEntry hostEntry = Dns.GetHostEntry(Domain);
            return hostEntry.AddressList[0].ToString();
        }
    }
}
