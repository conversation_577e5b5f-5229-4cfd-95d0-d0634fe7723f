﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using CV.Network.ObjectPool;
#endif
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;

namespace Network
{
    /// <summary>
    /// 管理实例静态类
    /// </summary>
    internal static class Managers
    {
#if NETSTANDARD  || NET5_0_OR_GREATER  || NETCOREAPP
        /// <summary>
        /// 缓冲区管理实例
        /// </summary>
        public static BufferPool BufferManager { get; private set; }


        public static AnyPool<SocketAsyncEventArgs> ArgsManager { get; private set; }
#endif
        /// <summary>
        /// 套接字参数管理实例
        /// </summary>
        public static SocketArgManager SocketArgManager { get; private set; }

        static Managers()
        {
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
            BufferManager = new BufferPool(8192, 2000);
            ArgsManager = new AnyPool<SocketAsyncEventArgs>(new DefaultAnyPolicy<SocketAsyncEventArgs>(), 3000);
#endif
            SocketArgManager = new SocketArgManager(10);
        }
    }
}
