﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace Network
{
	/// <summary>
	/// 缓冲区管理类
	/// </summary>
	internal class BufferManager : ListLocker<byte[]>
	{
		protected int BufferSize { get; private set; }

		/// <summary>
		/// 初始化缓冲区
		/// </summary>
		/// <param name="capacity"></param>
		public BufferManager(int buffserSize = 8192, int capacity = 1024)
			: base(capacity)
		{
			BufferSize = buffserSize;
		}

		/// <summary>
		/// 申请缓冲区
		/// </summary>
		/// <returns>申请到缓冲区</returns>
		public byte[] Allocate()
		{
			return new byte[BufferSize];
		}

		/// <summary>
		/// 释放缓冲区
		/// </summary>
		/// <param name="data"></param>
		public void Free(byte[] data)
		{
			Add(data);
		}
	}
}
