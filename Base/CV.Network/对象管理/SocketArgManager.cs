﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;
using System.Threading;
using System.Collections.Concurrent;

namespace Network
{
    ///// <summary>
    ///// 套接字参数管理类
    ///// </summary>
    //internal class SocketArgManager3
    //{
    //    ListLocker<SocketAsyncEventArgs> NullSocketArgs { get; set; }
    //    ListLocker<SocketAsyncEventArgs> BufferSocketArgs { get; set; }
    //    BufferManager Buffer { get; set; }


    //    int i = 0;
    //    object lck = new object();

    //    public SocketArgManager3(int count)
    //    {
    //        Buffer = new BufferManager();
    //        NullSocketArgs = new ListLocker<SocketAsyncEventArgs>();
    //        BufferSocketArgs = new ListLocker<SocketAsyncEventArgs>();

    //        for (int i = 0; i < count; i++)
    //        {
    //            SocketAsyncEventArgs e = New(true);
    //            BufferSocketArgs.Add(e);

    //            e = New(false);
    //            NullSocketArgs.Add(e);
    //        }
    //    }

    //    /// <summary>
    //    /// 
    //    /// </summary>
    //    /// <returns></returns>
    //    protected SocketAsyncEventArgs New(bool haveBuffer)
    //    {
    //        SocketAsyncEventArgs e = new SocketAsyncEventArgs();
    //        if (haveBuffer)
    //        {
    //            byte[] buffer = Buffer.Allocate();
    //            e.SetBuffer(buffer, 0, buffer.Length);
    //        }
    //        return e;
    //    }

    //    /// <summary>
    //    /// 申请套接字参数
    //    /// </summary>
    //    /// <returns></returns>
    //    public SocketAsyncEventArgs Allocate(bool haveBuffer)
    //    {
    //        DateTime dtTag = DateTime.Now;
    //        string stringLog = "";
    //        SocketAsyncEventArgs e = null;

    //        if (haveBuffer)
    //        {

    //            BufferSocketArgs.Enter();
    //            if (BufferSocketArgs.Count > 0)
    //            {
    //                e = BufferSocketArgs[0];
    //                BufferSocketArgs.RemoveAt(0);
    //            }
    //            BufferSocketArgs.Exit();
    //        }
    //        else
    //        {
    //            NullSocketArgs.Enter();
    //            stringLog += " __ 入锁耗时" + (DateTime.Now - dtTag).Milliseconds.ToString();
    //            if (NullSocketArgs.Count > 0)
    //            {
    //                e = NullSocketArgs[0];
    //                NullSocketArgs.RemoveAt(0);
    //            }
    //            stringLog += " __ Remove耗时" + (DateTime.Now - dtTag).Milliseconds.ToString();
    //            NullSocketArgs.Exit();
    //        }
    //        stringLog = " __ 过锁耗时" + (DateTime.Now - dtTag).Milliseconds.ToString() + " __ NullSocketArgs.Count = " + NullSocketArgs.Count.ToString() + stringLog;
    //        if (e == null)
    //        {
    //            e = New(haveBuffer);
    //        }
    //        e.UserToken = getid();
    //        stringLog = "处理[TCPChannel.Send]总耗时(Milliseconds):" + (DateTime.Now - dtTag).Milliseconds.ToString() + stringLog;
    //        if ((DateTime.Now.Millisecond % 1000) < 2) SQ.Base.Log.WriteLog4(stringLog, SQ.Base.LOGTYPE.INFO);
    //        return e;
    //    }

    //    private int getid()
    //    {
    //        lock (lck)
    //        {
    //            return i++;
    //        }
    //    }

    //    /// <summary>
    //    /// 释放套接字参数
    //    /// </summary>
    //    /// <param name="e"></param>
    //    public void Free(SocketAsyncEventArgs e, bool haveBuffer)
    //    {
    //        e.AcceptSocket = null;

    //        if (haveBuffer)
    //        {
    //            BufferSocketArgs.Enter();
    //            BufferSocketArgs.Add(e);
    //            BufferSocketArgs.Exit();
    //        }
    //        else
    //        {
    //            NullSocketArgs.Enter();
    //            NullSocketArgs.Add(e);
    //            NullSocketArgs.Exit();
    //        }
    //    }
    //}
    ///// <summary>
    ///// .NET CORE有bug linux环境会获取之前的数据
    ///// </summary>
    //internal class SocketArgManager_old
    //{
    //    ConcurrentQueue<SocketAsyncEventArgs> NullSocketArgs { get; set; }
    //    ConcurrentQueue<SocketAsyncEventArgs> BufferSocketArgs { get; set; }
    //    BufferManager Buffer { get; set; }
    //    int i = 0;
    //    object lck = new object();

    //    public SocketArgManager_old(int count)
    //    {
    //        Buffer = new BufferManager();
    //        NullSocketArgs = new ConcurrentQueue<SocketAsyncEventArgs>();
    //        BufferSocketArgs = new ConcurrentQueue<SocketAsyncEventArgs>();

    //        for (int i = 0; i < count; i++)
    //        {
    //            SocketAsyncEventArgs e = New(true);
    //            BufferSocketArgs.Enqueue(e);

    //            e = New(false);
    //            NullSocketArgs.Enqueue(e);
    //        }
    //    }

    //    /// <summary>
    //    /// 
    //    /// </summary>
    //    /// <returns></returns>
    //    protected SocketAsyncEventArgs New(bool haveBuffer)
    //    {
    //        SocketAsyncEventArgs e = new SocketAsyncEventArgs();
    //        if (haveBuffer)
    //        {
    //            byte[] buffer = Buffer.Allocate();
    //            e.SetBuffer(buffer, 0, buffer.Length);
    //        }
    //        return e;
    //    }

    //    /// <summary>
    //    /// 申请套接字参数
    //    /// </summary>
    //    /// <returns></returns>
    //    public SocketAsyncEventArgs Allocate(bool haveBuffer)
    //    {
    //        SocketAsyncEventArgs e = null;

    //        if (haveBuffer)
    //        {
    //            BufferSocketArgs.TryDequeue(out e);
    //        }
    //        else
    //        {
    //            NullSocketArgs.TryDequeue(out e);
    //        }
    //        if (e == null)
    //        {
    //            e = New(haveBuffer);
    //        }
    //        e.UserToken = getid();
    //        return e;
    //    }

    //    private int getid()
    //    {
    //        lock (lck)
    //        {
    //            return i++;
    //        }
    //    }

    //    /// <summary>
    //    /// 释放套接字参数
    //    /// </summary>
    //    /// <param name="e"></param>
    //    public void Free(SocketAsyncEventArgs e, bool haveBuffer)
    //    {
    //        e.AcceptSocket = null;

    //        if (haveBuffer)
    //        {
    //            //e.SetBuffer(0, e.Buffer.Length);
    //            BufferSocketArgs.Enqueue(e);
    //        }
    //        else
    //        {
    //            NullSocketArgs.Enqueue(e);
    //        }
    //    }
    //}



    internal class SocketArgManager
    {

#if NETFRAMEWORK
        BufferManager Buffer { get; set; }
#endif
        int i = 0;
        object lck = new object();

        public SocketArgManager(int count)
        {
#if NETFRAMEWORK
            Buffer = new BufferManager();
#endif
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        protected SocketAsyncEventArgs New(bool haveBuffer)
        {
//#if NETSTANDARD
//            SocketAsyncEventArgs e = Managers.ArgsManager.Get();
//#else
            SocketAsyncEventArgs e = new SocketAsyncEventArgs();
//#endif
            if (haveBuffer)
            {
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
                byte[] buffer = Managers.BufferManager.Get();
#else
                byte[] buffer = Buffer.Allocate();
#endif
                e.SetBuffer(buffer, 0, buffer.Length);
            }
            return e;
        }

        /// <summary>
        /// 申请套接字参数
        /// </summary>
        /// <returns></returns>
        public SocketAsyncEventArgs Allocate(bool haveBuffer)
        {
            SocketAsyncEventArgs e = null;
            e = New(haveBuffer);
            e.UserToken = getid();
            return e;
        }

        private int getid()
        {
            lock (lck)
            {
                if (i == int.MaxValue)
                {
                    i = 0;
                    return i;
                }
                return i++;
            }
        }

        /// <summary>
        /// 释放套接字参数
        /// </summary>
        /// <param name="e"></param>
        public void Free(SocketAsyncEventArgs e, bool haveBuffer)
        {
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
            if (haveBuffer && e.Buffer != null)
            {
                Managers.BufferManager.Return(e.Buffer);
            }
#endif
            e.AcceptSocket = null;
            e.UserToken = null;

            FreeBuffer(e);
            e.Dispose();

#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
            //Managers.ArgsManager.Return(e);
#endif
        }

        public void FreeBuffer(SocketAsyncEventArgs e)
        {
            if (e.BufferList != null)
            {
                e.BufferList = null;
            }
            else if (e.Buffer != null)
            {
                e.SetBuffer(null, 0, 0);
            }
        }
    }
}
