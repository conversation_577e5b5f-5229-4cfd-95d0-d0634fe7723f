﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using Network;
using SQ.Base;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;

namespace CV.Network
{
    public delegate void UdpServerSimpleOnRecv(byte[] data, int offset, int size, EndPoint remote);
    public class UdpServerSimple
    {
        /// <summary>
        /// socket
        /// </summary>
        public Socket m_listen_socket = null;
        /// <summary>
        /// 监听ip
        /// </summary>
        public string m_listen_ip;
        /// <summary>
        /// 监听端口
        /// </summary>
        public int m_listen_port;

        public UdpServerSimpleOnRecv m_on_recv;

        public UdpServerSimple()
        {
 
        }

        public bool Start(string ip, int port, UdpServerSimpleOnRecv on_recv)
        {
            m_listen_ip = ip;
            m_listen_port = port;
            m_on_recv = on_recv;
            try
            {
                if (m_listen_socket == null)
                {
                    m_listen_socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
                    m_listen_socket.Bind(new IPEndPoint(IPAddress.Parse(m_listen_ip), m_listen_port));

                    SocketAsyncEventArgs e = SocketAsyncEventArgsPool.GetInstance().Pop();
                    e.RemoteEndPoint = new IPEndPoint(IPAddress.Any, 0);
                    e.Completed += IO_Completed;
                    ProcessReceive(e);
                }
                return true;
            }
            catch (Exception ex)
            {
                Log.WriteLog4Ex("UdpServerSimple启动失败", ex);
                return false;
            }
        }

        protected void IO_Completed(object sender, SocketAsyncEventArgs e)
        {
            //Debug.WriteLine("UDP IO_Completed");
            switch (e.LastOperation)
            {
                case SocketAsyncOperation.ReceiveFrom:
                    OnReceiveFrom(e);
                    break;
            }
        }

        private void ProcessReceive(SocketAsyncEventArgs e)
        {
            ((IPEndPoint)e.RemoteEndPoint).Address = IPAddress.Any;
            ((IPEndPoint)e.RemoteEndPoint).Port = 0;
            if (!m_listen_socket.ReceiveFromAsync(e))
            {
                OnReceiveFrom(e);
            }
        }

        private void OnReceiveFrom(SocketAsyncEventArgs e)
        {
            //判断socket中是否有数据在传输&&判断是否有异常
            if (e.BytesTransferred > 0 && e.SocketError == SocketError.Success)
            {
                m_on_recv?.Invoke(e.Buffer, e.Offset, e.BytesTransferred, e.RemoteEndPoint);

                ProcessReceive(e);
            }
            else if (e.BytesTransferred == 0)//通道断开 
            {
                ProcessReceive(e);
            }
            else  //通道出错
            {
                ProcessReceive(e);
            }
        }

        public int SendTo(byte[] data, int offset, int size, EndPoint remote)
        {
            if (m_listen_socket == null)
                return 0;

            return m_listen_socket.SendTo(data, offset, size, SocketFlags.None, remote);
        }
    }
}

#endif
