﻿using SQ.Base;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;

namespace CV.Network
{
    public enum WSOPCODE
    {
        WS_OPCODE_CONTINUE = 0x0,
        WS_OPCODE_TEXT = 0x1,
        WS_OPCODE_BINARY = 0x2,
        WS_OPCODE_CLOSE = 0x8,
        WS_OPCODE_PING = 0x9,
        WS_OPCODE_PONG = 0xa,
        WS_OPCODE_NONE
    };

    public class DataFrame
    {
        public bool fin;
        public bool rsv1;
        public bool rsv2;
        public bool rsv3;

        public WSOPCODE wscode;
        public bool mask;
        public int payload_len;
        public int data_offset;

        public byte[] masking_key;

        public int total_length;
    }

    public class WebSocketProtocol
    {
        #region 公共接口

        /// <summary>
        /// 客户端发送握手
        /// </summary>
        /// <returns></returns>
        static public byte[] SendHandShake()
        {
            StringBuilder response = new StringBuilder();
            response.Append("GET / HTTP/1.1" + "\r\n");
            response.Append("Origin: http://localhost:1416" + "\r\n");
            response.Append("Sec-WebSocket-Key: vDyPp55hT1PphRU5OAe2Wg==" + "\r\n");
            response.Append("Connection: Upgrade" + "\r\n");
            response.Append("Upgrade: Websocket" + "\r\n");
            response.Append("Sec-WebSocket-Version: 13" + "\r\n");
            response.Append("User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; rv:11.0) like Gecko" + "\r\n");
            response.Append("Host: localhost:8064" + "\r\n");
            response.Append("DNT: 1" + "\r\n");
            response.Append("Cache-Control: no-cache" + "\r\n");
            response.Append("Cookie: DTRememberName=admin" + "\r\n" + "\r\n");

            return Encoding.UTF8.GetBytes(response.ToString());
        }
        /// <summary>
        /// handshake
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        static public byte[] Handshake(string request, out string real_ip, out string real_port)
        {
            string websocket_key = GetClientWSKey(request, "Sec-WebSocket-Key:");
            real_ip = null;
            real_port = null;
            if (websocket_key == string.Empty)
            {
                return null;
            }
            real_ip = GetClientWSKey(request, "X-real-ip:");
            real_port = GetClientWSKey(request, "X-real-port:");

            string accept_kep = ProduceAcceptKey(websocket_key);
            StringBuilder response = new StringBuilder();
            response.Append("HTTP/1.1 101 Switching Protocols" + "\r\n");
            response.Append("Upgrade: WebSocket" + "\r\n");
            response.Append("Connection: Upgrade" + "\r\n");
            response.AppendFormat("Sec-WebSocket-Accept: {0}" + "\r\n" + "\r\n", accept_kep);
            //response.AppendFormat("WebSocket-Origin: {0}\r\n", GetClientWSKey(request, "Sec-WebSocket-Origin"));
            //response.AppendFormat("WebSocket-Location: {0}\r\n", GetClientWSKey(request, "Host"));


            return Encoding.UTF8.GetBytes(response.ToString());
        }

        static public int PickData(byte[] indata, int offset, int length, out int out_offset, out int out_size, out WSOPCODE ws_code)
        {
            out_offset = offset;
            out_size = 0;
            ws_code = WSOPCODE.WS_OPCODE_NONE;

            var df = AnalyzeHeader(indata, offset, length);

            if (df == null)
                return 0;

            out_offset = df.data_offset;
            out_size = df.payload_len;
            ws_code = df.wscode;

            PickDataV(indata, df);

            return df.total_length;
        }
        unsafe static public byte[] PackData(byte* indata, int length, WSOPCODE wscode = WSOPCODE.WS_OPCODE_BINARY)
        {
            byte[] outdata = null;
            int out_offset = 0;


            if (length < 126)
            {
                out_offset = 2;
                outdata = new byte[length + 2];
                outdata[0] = (byte)(0x80 | (byte)wscode);
                outdata[1] = (byte)length;
            }
            else if (length <= ushort.MaxValue)
            {
                out_offset = 4;
                outdata = new byte[length + 4];

                outdata[0] = (byte)(0x80 | (byte)wscode);
                outdata[1] = 0x7e;
                ByteHelper.UIntToByte2((ushort)length, outdata, 2);
                //ushort l = (ushort)length;
                //byte[] bl = BitConverter.GetBytes(l);
                //outdata[2] = bl[1];
                //outdata[3] = bl[0];
            }
            else
            {
                out_offset = 10;
                outdata = new byte[length + 10];

                outdata[0] = (byte)(0x80 | (byte)wscode);
                outdata[1] = 0x7f;
                ByteHelper.ULongToByte8((ulong)length, outdata, 2);

                //byte[] b2 = BitConverter.GetBytes((long)length);
                //outdata[2] = b2[7];
                //outdata[3] = b2[6];
                //outdata[4] = b2[5];
                //outdata[5] = b2[4];
                //outdata[6] = b2[3];
                //outdata[7] = b2[2];
                //outdata[8] = b2[1];
                //outdata[9] = b2[0];
            }
            Marshal.Copy((IntPtr)indata, outdata, out_offset, length);

            return outdata;
        }
        static public byte[] PackData(byte[] indata, int offset, int length, WSOPCODE wscode = WSOPCODE.WS_OPCODE_BINARY)
        {
            byte[] outdata = null;
            int out_offset = 0;


            if (length < 126)
            {
                out_offset = 2;
                outdata = new byte[length + 2];
                outdata[0] = (byte)(0x80 | (byte)wscode);
                outdata[1] = (byte)length;
            }
            else if (length <= ushort.MaxValue)
            {
                out_offset = 4;
                outdata = new byte[length + 4];

                outdata[0] = (byte)(0x80 | (byte)wscode);
                outdata[1] = 0x7e;
                ByteHelper.UIntToByte2((ushort)length, outdata, 2);
                //ushort l = (ushort)length;
                //byte[] bl = BitConverter.GetBytes(l);
                //outdata[2] = bl[1];
                //outdata[3] = bl[0];
            }
            else
            {
                out_offset = 10;
                outdata = new byte[length + 10];

                outdata[0] = (byte)(0x80 | (byte)wscode);
                outdata[1] = 0x7f;
                ByteHelper.ULongToByte8((ulong)length, outdata, 2);

                //byte[] b2 = BitConverter.GetBytes((long)length);
                //outdata[2] = b2[7];
                //outdata[3] = b2[6];
                //outdata[4] = b2[5];
                //outdata[5] = b2[4];
                //outdata[6] = b2[3];
                //outdata[7] = b2[2];
                //outdata[8] = b2[1];
                //outdata[9] = b2[0];
            }

            Buffer.BlockCopy(indata, offset, outdata, out_offset, length);

            return outdata;
        }

        #endregion

        #region handshake相关
        /// <summary>
        /// handshake获得key_name的内容
        /// </summary>
        /// <param name="request"></param>
        /// <param name="key_name"></param>
        /// <returns></returns>
        static public string GetClientWSKey(string request, string key_name)
        {
            int i = CultureInfo.InvariantCulture.CompareInfo.IndexOf(request, key_name, CompareOptions.IgnoreCase);
            if (i > 0)
            {
                i += key_name.Length;
                int j = request.IndexOf("\r\n", i);
                if (j > 0)
                    return request.Substring(i, j - i).Trim();
            }
            return string.Empty;
        }
        /// <summary>
        /// handshake生成Key
        /// </summary>
        /// <param name="websocket_key"></param>
        /// <returns></returns>
        static private string ProduceAcceptKey(string websocket_key)
        {
            string MagicKey = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11";
            Byte[] acceptKey = SHA1.Create().ComputeHash(Encoding.ASCII.GetBytes(websocket_key + MagicKey));
            return Convert.ToBase64String(acceptKey);
        }
        #endregion

        /// <summary>
        /// 分析获得头
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        static private DataFrame AnalyzeHeader(byte[] data, int offset, int length)
        {
            if (length < 2)
                return null;

            DataFrame df = new DataFrame();

            df.fin = (data[offset] & 0x80) == 0x80 ? true : false;
            df.rsv1 = (data[offset] & 0x40) == 0x40 ? true : false;
            df.rsv2 = (data[offset] & 0x20) == 0x20 ? true : false;
            df.rsv3 = (data[offset] & 0x10) == 0x10 ? true : false;

            df.wscode = (WSOPCODE)(data[0] & 0x0F);
            df.mask = (data[offset + 1] & 0x80) == 0x80 ? true : false;

            df.masking_key = new byte[4];

            int len = data[offset + 1] & 0x7F;
            switch (len)
            {
                case 126:

                    if (length < 4)
                        return null;

                    df.payload_len = (UInt16)(data[offset + 2] << 8 | data[offset + 3]);
                    if (df.mask)
                    {
                        if (length < df.payload_len + 8)
                            return null;

                        Buffer.BlockCopy(data, offset + 4, df.masking_key, 0, 4);
                        df.data_offset = offset + 8;
                        df.total_length = df.payload_len + 8;
                    }
                    else
                    {
                        if (length < df.payload_len + 4)
                            return null;

                        df.data_offset = offset + 4;
                        df.total_length = df.payload_len + 4;
                    }

                    break;
                case 127:
                    if (length < 10)
                        return null;
                    df.payload_len = (int)ByteHelper.Byte8ToULong(data, offset + 2);
                    //byte[] byteLen = new byte[8];
                    //Buffer.BlockCopy(data, 4, byteLen, 0, 8);
                    //df.payload_len = (int)BitConverter.ToUInt64(byteLen, 0);
                    if (df.mask)
                    {
                        if (length < df.payload_len + 14)
                            return null;

                        Buffer.BlockCopy(data, 10, df.masking_key, 0, 4);
                        df.data_offset = offset + 14;
                        df.total_length = df.payload_len + 14;
                    }
                    else
                    {
                        if (length < df.payload_len + 10)
                            return null;

                        df.data_offset = offset + 10;
                        df.total_length = df.payload_len + 10;
                    }

                    break;
                default:
                    df.payload_len = len;

                    if (df.mask)
                    {
                        if (length < df.payload_len + 6)
                            return null;

                        Buffer.BlockCopy(data, offset + 2, df.masking_key, 0, 4);
                        df.data_offset = offset + 6;
                        df.total_length = df.payload_len + 6;
                    }
                    else
                    {
                        if (length < df.payload_len + 2)
                            return null;

                        df.data_offset = offset + 2;
                        df.total_length = df.payload_len + 2;
                    }
                    break;
            }
            return df;
        }
        /// <summary>
        /// 反掩码
        /// </summary>
        /// <param name="data"></param>
        /// <param name="df"></param>
        static private void PickDataV(byte[] data, DataFrame df)
        {
            if (df.mask)
            {
                int j = 0;
                for (int i = df.data_offset; i < df.data_offset + df.payload_len; i++)
                {
                    data[i] ^= df.masking_key[j++];
                    if (j == 4)
                        j = 0;
                }
            }
        }

    }
}
