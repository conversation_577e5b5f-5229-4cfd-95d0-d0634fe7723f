﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;
using System.Net;
using SQ.Base;

namespace Network
{
    /// <summary>
    /// 连接服务类基类
    /// </summary>
    public abstract class Server
    {
        #region 属性
        /// <summary>
        /// 通道连接事件
        /// </summary>
        public virtual event EventChannelConnect ChannelConnect;
        /// <summary>
        /// 通道断开事件
        /// </summary>
        public virtual event EventChannelDispose ChannelDispose;
        /// <summary>
        /// 通道错误事件
        /// </summary>
        public virtual event EventChannelError ChannelError;
        /// <summary>
        /// 获取本地套接字
        /// </summary>
        public Socket LocalSocket { get; protected set; }
        /// <summary>
        /// 获取服务器本地监听IP地址
        /// </summary>
        public string LocalHost { get; protected set; }
        /// <summary>
        /// 获取服务器本地绑定或监听端口
        /// </summary>
        public int LocalPort { get; protected set; }
        /// <summary>
        /// 获取连接客户端管理实例
        /// </summary>
        public ListLocker<Channel> Channels { get; protected set; }
        /// <summary>
        /// 用户标识对象
        /// </summary>
        public object Tag { get; set; }
        #endregion

        #region 事件处理
        /// <summary>
        /// 通道连接事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnChannelConnect(object sender, ChannelConnectArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPServer.OnChannelConnect(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            //提前AddChannel 防止socket连接后立马断开 造成remove在add前造成Channel一直不释放
            //2020/6/15 由于ChannelDispose 和 ChannelError 事件需要开始接收数据后才会触发，所以提前AddChannel后不会存在乱序问题
            if (arg.SocketError == SocketError.Success)
            {
                try
                {
                    Channels.Enter();
                    Channels.Add(arg.Channel);
                }
                finally
                {
                    Channels.Exit();
                }
            }
            if (ChannelConnect != null)
            {
                try
                {
                    ChannelConnect(sender, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Server event error", ex);
                    if (ex.Message == "等待连接超时！")
                    {
                        arg.SocketError = SocketError.SocketError;
                    }
                }
            }

#if FUNCOUTLOG
			Log.WriteLog4("TCPServer.OnChannelConnect(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通道释放事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnChannelDispose(object sender, ChannelDisposeArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPServer.OnChannelDispose(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (ChannelDispose != null)
            {
                try
                {
                    ChannelDispose(this, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Server event error", ex);
                }
            }
            try
            {
                Channels.Enter();
                Channels.Remove(arg.Channel);
            }
            finally
            {
                Channels.Exit();
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPServer.OnChannelDispose(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }

        /// <summary>
        /// 通道异常事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="arg"></param>
        protected virtual void OnChannelError(object sender, ChannelErrorArg arg)
        {
#if FUNCINLOG
			Log.WriteLog4("TCPServer.OnChannelError(sender, arg) in.", LOGTYPE.DEBUG);
#endif
            if (ChannelError != null)
            {
                try
                {
                    ChannelError(this, arg);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("Network Server event error", ex);
                }
            }
            try
            {
                Channels.Enter();
                Channels.Remove(arg.Channel);
            }
            finally
            {
                Channels.Exit();
            }
#if FUNCOUTLOG
			Log.WriteLog4("TCPServer.OnChannelError(sender, arg) out.", LOGTYPE.DEBUG);
#endif
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 构造方法
        /// </summary>
        public Server()
        {
            Channels = new ListLocker<Channel>();
            LocalSocket = null;
        }

        /// <summary>
        /// 开启服务器
        /// </summary>
        /// <returns>成功返回true,否则返回false</returns>
        public virtual bool Start()
        {
            return true;
        }

        /// <summary>
        /// 停止服务器
        /// </summary>
        public virtual void Stop()
        {
            Channel[] channels;
            try
            {
                Channels.Enter();
                channels = Channels.ToArray();
                Channels.Clear();
            }
            finally
            {
                Channels.Exit();
            }

            foreach (var channel in channels)
            {
                channel.Close();
            }
        }
        #endregion

        #region 内部方法
        /// <summary>
        /// 通过连接套接字创建通道
        /// </summary>
        /// <param name="socket">连接套接字</param>
        /// <param name="channel">子类已创建通道.如果非空,则基类在现有通道实例上处理</param>
        /// <returns>创建的通道实例.非空进创建成功,否则失败</returns>
        protected virtual Channel CreateChannelBySocket(Socket socket, Channel channel = null)
        {
            if (channel == null)
                return null;
            else
            {
                channel.ChannelDispose += OnChannelDispose;
                channel.ChannelError += OnChannelError;
                return channel;
            }
        }
        #endregion
    }
}
