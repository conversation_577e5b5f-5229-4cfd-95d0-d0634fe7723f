﻿using CV.Network.SSL;
using Network;
using SQ.Base;
using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Text;

namespace CV.Network
{
    public delegate void EventChannelHandShake(object sender, ChannelReceiveArg arg);

    /// <summary>
    /// 数据包buffer
    /// </summary>
    public class MediaPackageBuffer
    {
        /// <summary>
        /// 接收数据buffer
        /// </summary>
        public byte[] m_buffer = new byte[10240];
        /// <summary>
        /// 接收数据buffer大小
        /// </summary>
        public int m_buffer_size = 10240;
        /// <summary>
        /// 已接收到的数据长度
        /// </summary>
        public int m_buffer_recv_size = 0;
        /// <summary>
        /// 允许的最大websocket包大小
        /// </summary>
        public int m_max_package_size = 1024 * 1024 * 2;

        /// <summary>
        /// 接收数据
        /// </summary>
        /// <param name="buffer"></param>
        /// <param name="offset"></param>
        /// <param name="size"></param>
        /// <returns>返回接收的大小</returns>
        public int Receive(byte[] buffer, int offset, int size)
        {
            //buffer大小是否足够
            if (size + m_buffer_recv_size > m_buffer_size)
            {
                //是否重新分配内存
                if (m_buffer_size < m_max_package_size)
                {
                    int renew_size = (size + m_buffer_recv_size > m_max_package_size) ? (m_max_package_size + 1024) : (size + m_buffer_recv_size) + 2048;
                    byte[] renew_buffer = new byte[renew_size];

                    Array.Copy(this.m_buffer, 0, renew_buffer, 0, this.m_buffer_recv_size);
                    m_buffer_size = renew_size;
                    this.m_buffer = renew_buffer;

                    size = (size + m_buffer_recv_size > m_buffer_size) ? (m_buffer_size - m_buffer_recv_size) : size;
                }
                else
                {
                    Log.WriteLog4("JTTCPClient::Receive buffer大小:" + (size + m_buffer_recv_size));
                    size = m_buffer_size - m_buffer_recv_size;
                }
            }
            Array.Copy(buffer, offset, this.m_buffer, this.m_buffer_recv_size, size);
            m_buffer_recv_size = m_buffer_recv_size + size;

            return size;
        }
        /// <summary>
        /// 剩余的buffer复制到最前面
        /// </summary>
        /// <param name="size"></param>
        public void ResidueCopy(int size)
        {
            if (size < m_buffer_recv_size && size > 0)
            {
                Array.Copy(m_buffer, m_buffer_recv_size - size, m_buffer, 0, size);
            }
            m_buffer_recv_size = size;
        }
    }

    public class WebSocketChannel : TCPChannel
    {
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP

        SSLWsHander wssslHander;

        public System.Security.Cryptography.X509Certificates.X509Certificate2 serverCertificate = null;
#endif

        /// <summary>
        /// 是否完成握手协议
        /// </summary>
        bool m_is_handshake_finish = false;
        /// <summary>
        /// 是否为客户端
        /// </summary>
        bool m_is_client = false;
        /// <summary>
        /// 数据buffer
        /// </summary>
        internal MediaPackageBuffer m_package_buffer = new MediaPackageBuffer();
        /// <summary>
        /// 客户端握手完成事件
        /// </summary>
        public event EventChannelHandShake ChannelHandShake;

        public WebSocketChannel(string remoteHost, int remotePort)
            : base(remoteHost, remotePort)
        {
        }

        /// <summary>
        /// 使用已连接套接字创建TCP通道
        /// </summary>
        /// <param name="socket">已连接套接字</param>
        public WebSocketChannel(Socket socket)
            : base(socket)
        {
        }

        public bool IsHandShake()
        {
            return m_is_handshake_finish;
        }

        public void SendText(byte[] data)
        {
            byte[] send = WebSocketProtocol.PackData(data, 0, data.Length, WSOPCODE.WS_OPCODE_TEXT);

#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
            if (wssslHander == null)
            {
                SendRaw(send, 0, send.Length);
            }
            else
            {
                wssslHander.Send(send, 0, send.Length);
            }
#else
                SendRaw(send, 0, send.Length);
#endif
        }

        public override void Send(byte[] data)
        {
            //byte[] send = WebSocketProtocol.PackData(data, 0, data.Length);

            Send(data, 0, data.Length);
        }
        public override void Send(byte[] data, int offset, int count)
        {
            //Log.WriteLog4("Send:", data, offset, count, LOGTYPE.DEBUG);
            byte[] send = WebSocketProtocol.PackData(data, offset, count);
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
            if (wssslHander == null)
            {
                SendRaw(send, 0, send.Length);
            }
            else
            {
                wssslHander.Send(send, 0, send.Length);
            }
#else
                SendRaw(send, 0, send.Length);
#endif
        }
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
        public unsafe void Send(byte* data, int count)
        {
            //Log.WriteLog4("Send:", data, offset, count, LOGTYPE.DEBUG);
            byte[] send = WebSocketProtocol.PackData(data, count);

            if (wssslHander == null)
            {
                SendRaw(send, 0, send.Length);
            }
            else
            {
                wssslHander.Send(send, 0, send.Length);
            }
        }
#endif
        public void SendRaw(byte[] data, int offset, int count)
        {
            //Log.WriteLog4("SendRaw:", data, offset, count, LOGTYPE.DEBUG);
            base.Send(data, offset, count);
        }

        public override int SendDirect(byte[] data, int offset, int size)
        {
            //Log.WriteLog4("SendDirect:", data, offset, size, LOGTYPE.DEBUG);
            int ret = -1;

            //byte[] send = WebSocketProtocol.PackData(data, offset, size);

            if (Socket != null && Socket.Connected)
            {
                try
                {
                    ret = Socket.Send(data, offset, size, SocketFlags.None);
                }
                catch (Exception ex)
                {
                    //Log.WriteLog4Ex("TCPChannel::SendDirect", ex);
                    ret = -1;
                }

            }

            return ret;
        }

        protected override void OnConnect(object sender, ChannelConnectArg arg)
        {
            m_is_client = true;

            if (arg != null && arg.SocketError == System.Net.Sockets.SocketError.Success)
            {
                byte[] data = WebSocketProtocol.SendHandShake();
                try
                {
                    Socket.Send(data, 0, data.Length, SocketFlags.None);
                }
                catch (Exception ex)
                {
                    Log.WriteLog4Ex("TCPChannel::SendDirect", ex);
                }
            }

            base.OnConnect(sender, arg);
        }

        protected override void OnReceive(object sender, ChannelReceiveArg arg)
        {
            //Log.WriteLog4(new StringBuilder("OnReceive:"), arg.Buffer, arg.BufferOffset, arg.BufferSize, LOGTYPE.DEBUG, true);
            if (arg != null && arg.Buffer != null)
            {
                ReceiveBytes += arg.BufferSize;
            }
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP

            if (wssslHander == null)
            {
                ReceiveHand(sender, arg);
            }
            else
            {
                wssslHander.Receive(sender, arg);
            }
#else
            ReceiveHand(sender, arg);
#endif
        }
        public void ReceiveHand(object sender, ChannelReceiveArg arg)
        {
            //Log.WriteLog4(new StringBuilder("ReceiveHand:"), arg.Buffer, arg.BufferOffset, arg.BufferSize, LOGTYPE.DEBUG, true);
            if (!m_is_handshake_finish)
            {
                if (m_is_client)
                {
                    //过掉handshak数据
                    if (ChannelHandShake != null)
                    {
                        try
                        {
                            ChannelHandShake(sender, arg);
                        }
                        catch (Exception ex)
                        {
                            ErrorLog.WriteLog4Ex("WebSocketChannel::OnRecevice", ex);
                        }
                    }
                }
                else
                {

                    string request = System.Text.Encoding.UTF8.GetString(arg.Buffer, arg.BufferOffset, arg.BufferSize);
                    var reponse = WebSocketProtocol.Handshake(request, out var realip, out var realport);
                    if (reponse == null)
                    {
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP

                        if (wssslHander == null && arg.BufferSize > 100 && serverCertificate != null)
                        {
                            wssslHander = new SSLWsHander(this, serverCertificate);
                            wssslHander.Receive(sender, arg);
                            return;
                        }
#endif


                        Log.WriteLog4("WebSocketChannel::OnReceive Handshake协议解析错误断开连接");
                        Close();
                        return;
                    }

#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP

                    if (wssslHander != null)
                    {

                        if (wssslHander.Send(reponse, 0, reponse.Length) < 0)
                        {

                            Log.WriteLog4("WebSocketChannel::OnReceive 发送握手协议失败 length:" + reponse.Length);
                            Close();
                            return;
                        }

                    }
                    else
                    {

                        if (SendDirect(reponse, 0, reponse.Length) < 0)
                        {
                            Log.WriteLog4("WebSocketChannel::OnReceive 发送握手协议失败 length:" + reponse.Length);
                            Close();
                            return;
                        }

                    }
#else

                    if (SendDirect(reponse, 0, reponse.Length) < 0)
                    {
                        Log.WriteLog4("WebSocketChannel::OnReceive 发送握手协议失败 length:" + reponse.Length);
                        Close();
                        return;
                    }
#endif
                }

                m_is_handshake_finish = true;
                return;
            }


            int ret;
            byte[] buffer = arg.Buffer;
            int arg_buffer_offset = arg.BufferOffset;
            int arg_buffer_size = arg.BufferSize;

            while (arg_buffer_size > 0)
            {
                ret = m_package_buffer.Receive(buffer, arg_buffer_offset, arg_buffer_size);

                arg_buffer_offset += ret;
                arg_buffer_size -= ret;

                byte[] data = m_package_buffer.m_buffer;
                int offset = 0;
                int size = m_package_buffer.m_buffer_recv_size;

                do
                {
                    int out_offset;
                    int out_size;
                    WSOPCODE ws_code;
                    ret = WebSocketProtocol.PickData(data, offset, size, out out_offset, out out_size, out ws_code);

                    offset += ret;
                    size -= ret;

                    if (ret < 0)
                    {
                        Log.WriteLog4("MediaPackgeRecv::RecevieWebsocekt PickData协议解析错误断开连接");
                        Close();
                        return;
                    }
                    else if (ret > 0)
                    {
                        if (ws_code != WSOPCODE.WS_OPCODE_BINARY && ws_code != WSOPCODE.WS_OPCODE_TEXT)
                        {
                            if (ws_code == WSOPCODE.WS_OPCODE_CLOSE)
                            {
                                Log.WriteLog4("MediaPackgeRecv::RecevieWebsocekt recv WSOPCODE.WS_OPCODE_CLOSE");
                                Close();
                                return;
                            }
                            continue;
                        }

                        base.OnReceive(sender, new ChannelReceiveArg(data, out_offset, out_size));
                    }
                    else if (ret == 0)
                    {
                        if (size > m_package_buffer.m_max_package_size)
                        {
                            Log.WriteLog4("FDTCPWebSocketClient::Receive 协议解析错误断开连接");
                            Close();
                            return;
                        }
                    }

                } while (ret > 0 && size > 0);

                m_package_buffer.ResidueCopy(size);
            }

        }
    }
}
