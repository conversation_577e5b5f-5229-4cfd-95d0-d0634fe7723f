﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;

namespace Network
{
    public class UDPClient
    {
        public Socket Socket { get; protected set; }
        EndPoint m_endpoint;

        public int LocalPort
        {
            get
            {
                if (Socket == null)
                    return 0;
                return ((IPEndPoint)Socket.LocalEndPoint).Port;
            }

        }

        /// <summary>
        /// 收到数据委托
        /// </summary>
        public Network.EventChannelReceive DataReceive;

        public UDPClient(string ip, int port)
        {
            Socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
            m_endpoint= Network.Utils.GetEndPoint(ip, port);
            //m_endpoint = new IPEndPoint(IPAddress.Parse(ip), port);
        }

        public void Start()
        {
            if (!Socket.Connected)
                Socket.Connect(m_endpoint);
            byte[] buffer = new byte[1700];
            SocketAsyncEventArgs e = new SocketAsyncEventArgs();
            e.Completed += IO_Completed;
            e.SetBuffer(buffer, 0, buffer.Length);
            e.RemoteEndPoint = m_endpoint;
            ProcessReceive(e);
        }

        public void Send(byte[] data, int offset, int size)
        {
            Socket?.SendTo(data, offset, size, SocketFlags.None, m_endpoint);
        }


        private void IO_Completed(object sender, SocketAsyncEventArgs e)
        {
            switch (e.LastOperation)
            {
                case SocketAsyncOperation.ReceiveFrom:
                    OnReceiveFrom(e);
                    break;
            }
        }

        private void OnReceiveFrom(SocketAsyncEventArgs e)
        {
            if (e.BytesTransferred > 0 && e.SocketError == SocketError.Success)
            {
                DataReceive(this, new Network.ChannelReceiveArg(e.Buffer, e.Offset, e.BytesTransferred));
                ProcessReceive(e);
            }
            else
            {

            }
        }

        private void ProcessReceive(SocketAsyncEventArgs e)
        {
            if (!Socket.ReceiveFromAsync(e))
            {
                OnReceiveFrom(e);
            }
        }

        public void Close()
        {
            Socket.Close();
        }
    }
}
