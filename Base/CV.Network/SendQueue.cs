﻿using SQ.Base;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace Network
{
    public class SendQueue
    {
        public ConcurrentDictionary<Channel, ChannelQueue> dit = new ConcurrentDictionary<Channel, ChannelQueue>();
        public object lck = new object();
        public bool SendAll()
        {
            bool flag = false;
            try
            {
                var arr = dit.ToArray();
                foreach (var item in arr)
                {
                    if (item.Value.SendAll())
                    {
                        flag = true;
                    }
                }
            }
            catch (Exception ex)
            {
            }
            return flag;
        }
        public ChannelQueue AddChannelQueue(Channel cl)
        {
            var cq = new ChannelQueue()
            {
                Cl = cl,
                SQU = this
            };
            lock (lck)
            {
                dit[cl] = cq;
            }
            return cq;
        }

        public void RemoveChannelQueue(Channel cl)
        {
            if (dit.ContainsKey(cl))
            {
                lock (lck)
                {
                    if (dit.ContainsKey(cl))
                    {
                        dit.TryRemove(cl, out var rm);
                    }
                }
            }
        }
        public void RunSend()
        {
            try
            {
                while (true)
                {
                    if (!SendAll())
                    {
                        Thread.Sleep(100);
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
    }
    public class ChannelQueue
    {
        public SendQueue SQU;
        public Channel Cl;
        public ConcurrentQueue<ArraySegment<byte>> DataQueue = new ConcurrentQueue<ArraySegment<byte>>();
        public void Enqueue(ArraySegment<byte> bts)
        {
            DataQueue.Enqueue(bts);
        }

        public bool SendAll()
        {
            try
            {
                if (DataQueue.Count > 0)
                {
                    List<ArraySegment<byte>> lst = new List<ArraySegment<byte>>();

                    while (DataQueue.TryDequeue(out var bts))
                    {
                        lst.Add(bts);
                    }
                    Cl.SendList(lst);
                    return true;
                }
            }
            catch (Exception ex)
            {
                ErrorLog.WriteLog4Ex("ChannelQueue.SendAll", ex);
            }
            return false;
        }
        public void RemoveChannelQueue()
        {
            SQU.RemoveChannelQueue(Cl);
        }
    }
}
