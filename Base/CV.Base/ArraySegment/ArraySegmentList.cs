﻿using System;
using System.Collections.Generic;
using System.Text;

namespace SQ.Base.ArraySegment
{
    public class ArraySegmentList<T>
    {
        public ArraySegmentList()
        {

        }
        public ArraySegmentList(ArraySegment<T> item)
        {
            Add(item);
        }
        int now_start, now_end, now_li;
        private bool GetIdx(int index, out int li, out int idx)
        {
            li = idx = -1;
            if (index >= now_start && index < now_end)
            {
                li = now_li;
                idx = index - now_start;
                return true;
            }
            var start = 0;
            for (int i = 0; i < lstIndex.Count; i++)
            {
                if (lstIndex[i] > index)
                {
                    li = i;
                    idx = index - start;
                    now_start = start;
                    now_end = lstIndex[i];
                    now_li = i;
                    return true;
                }
                else
                {
                    start = lstIndex[i];
                }
            }
            return false;
        }
        public T this[int index]
        {
            get
            {
                if (GetIdx(index, out var li, out var idx))
                {
                    var item = data[li];
                    return item.Array[item.Offset + idx];
                }
                throw new System.ArgumentOutOfRangeException();
            }
            set
            {
                if (GetIdx(index, out var li, out var idx))
                {
                    var item = data[li];
                    item.Array[item.Offset + idx] = value;
                }
                throw new System.ArgumentOutOfRangeException();
            }
        }
        /// <summary>
        /// data结束索引 lstIndex[1]表示data[0]+data[1]的大小 
        /// </summary>
        List<int> lstIndex = new List<int>();

        List<ArraySegment<T>> data = new List<ArraySegment<T>>();
        public int AllCount { get; protected set; }
        public void Add(ArraySegment<T> item)
        {
            if (item != null)
            {
                data.Add(item);
                AllCount += item.Count;
                lstIndex.Add(AllCount);
            }
        }

        public void AddRange(ArraySegmentList<T> lst)
        {
            foreach (var item in lst.data)
            {
                Add(item);
            }
        }
        public void Clear()
        {
            data.Clear();
            lstIndex.Clear();
            AllCount = 0;
            now_start = now_end = now_li = 0;
        }


        public bool CopyTo(int sourceIndex, T[] destinationArray, int destinationIndex, int length)
        {
#if NETSTANDARD  || NET5_0_OR_GREATER  || NETCOREAPP
            if (GetIdx(sourceIndex, out var si, out var sidx) && GetIdx(sourceIndex + length - 1, out var ei, out var eidx))
            {
                var span = new Span<T>(destinationArray, destinationIndex, length);
                while (si < ei)
                {
                    var sr = data[si].AsSpan().Slice(sidx);
                    sr.CopyTo(span);
                    //Array.Copy(data[si].Array, data[si].Offset+sidx,destinationArray,destinationIndex,)
                    si++;
                    sidx = 0;
                    span = span.Slice(sr.Length);
                }
                if (si == ei)
                {
                    data[si].AsSpan().Slice(sidx, span.Length).CopyTo(span);
                }
                return true;
            }
#endif
            return false;
        }

        public ArraySegmentList<T> Slice(int index, int length)
        {
            if (GetIdx(index, out var si, out var sidx) && GetIdx(index + length - 1, out var ei, out var eidx))
            {
                var ret = new ArraySegmentList<T>();
                while (si < ei)
                {
                    if (sidx == 0)
                    {
                        ret.Add(data[si]);
                        length -= data[si].Count;
                    }
                    else
                    {
                        ret.Add(new ArraySegment<T>(data[si].Array, data[si].Offset + sidx, data[si].Count - sidx));
                        length -= data[si].Count - sidx;
                        sidx = 0;
                    }
                    si++;
                }
                if (si == ei)
                {
                    if (sidx == 0 && length == data[si].Count)
                        ret.Add(data[si]);
                    else
                        ret.Add(new ArraySegment<T>(data[si].Array, data[si].Offset + sidx, length));
                }
                return ret;
            }
            throw new System.ArgumentOutOfRangeException();
        }
    }
}
