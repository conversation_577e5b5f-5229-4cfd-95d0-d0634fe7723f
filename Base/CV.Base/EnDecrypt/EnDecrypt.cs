﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace SQ.Base.EnDecrypt
{
    public class EnDecrypt
    {
        #region===========MD5加密(不可逆)

        /// <summary>
        /// MD5加密(不可逆)
        /// </summary>
        /// <param name="text">加密字符</param>
        /// <returns>返回加密字符</returns>
        public static string MD5(string text)
        {
            MD5 md5 = new MD5CryptoServiceProvider();
            byte[] inputText = Encoding.Default.GetBytes(text);
            byte[] buffer = md5.ComputeHash(inputText);
            md5.Clear();
            string str = string.Empty;
            for (int i = 0; i < buffer.Length; i++)
            {
                str += buffer[i].ToString("X").PadLeft(2, '0');
            }
            return str;
        }
        #endregion

        #region ==========加密(可逆)==========

        /// <summary>
        /// 加密(可逆)
        /// </summary>
        /// <param name="text">文本</param>
        /// <returns>加密(可逆)</returns>
        public static string Encrypt(string text)
        {
            return Encrypt(text, "cvnavi");
        }
        /// <summary>
        /// 加密(可逆)
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="sKey">key</param>
        /// <returns>加密(可逆)</returns>
        private static string Encrypt(string text, string sKey)
        {
            DESCryptoServiceProvider des = new DESCryptoServiceProvider();
            byte[] inpuText = Encoding.Default.GetBytes(text);


            des.IV = Encoding.Default.GetBytes(MD5(sKey).Substring(0, 8));
            des.Key = Encoding.Default.GetBytes(MD5(sKey).Substring(0, 8));

            MemoryStream ms = new MemoryStream();
            CryptoStream cs = new CryptoStream(ms, des.CreateEncryptor(), CryptoStreamMode.Write);
            cs.Write(inpuText, 0, inpuText.Length);
            cs.FlushFinalBlock();
            StringBuilder sb = new StringBuilder();
            foreach (byte b in ms.ToArray())
            {
                //16进制转换
                sb.AppendFormat("{0:x2}", b);
            }
            return sb.ToString().ToUpper();
        }

        /// <summary>
        /// SHA512 加密
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static string SHA512Hash(string text, string salt, int iterations)
        {
            using (SHA512 sha512 = SHA512.Create())
            {
                byte[] bytes_sha512_in = Encoding.UTF8.GetBytes(salt + text);
                byte[] bytes_sha512_out = sha512.ComputeHash(bytes_sha512_in);
                iterations -= 1;
                if (iterations > 0)
                {
                    for (int i = 0; i < iterations; i++)
                    {
                        bytes_sha512_out = sha512.ComputeHash(bytes_sha512_in);
                    }
                }
                string str_sha512_out = BitConverter.ToString(bytes_sha512_out);
                str_sha512_out = str_sha512_out.Replace("-", "");
                return str_sha512_out.ToLower();
            }
        }

        #endregion


        #region ============解密(可逆)=========
        /// <summary>
        /// 解密(可逆)
        /// </summary>
        /// <param name="text">文本</param>
        /// <returns>解密(可逆)</returns>
        public static string Decrypt(string text)
        {
            return Decrypt(text, "cvnavi");
        }
        /// <summary>
        /// 解密(可逆)
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="sKey">key</param>
        /// <returns>解密(可逆)</returns>
        private static string Decrypt(string text, string sKey)
        {
            string strPwd = "";
            try
            {
                DESCryptoServiceProvider des = new DESCryptoServiceProvider();

                byte[] inputText = new byte[text.Length / 2];
                int i, x;
                for (x = 0; x < text.Length / 2; x++)
                {
                    //16进制转换
                    i = Convert.ToInt32(text.Substring(x * 2, 2), 16);
                    inputText[x] = (byte)i;
                }
                des.IV = Encoding.Default.GetBytes(MD5(sKey).Substring(0, 8));
                des.Key = Encoding.Default.GetBytes(MD5(sKey).Substring(0, 8));

                MemoryStream ms = new MemoryStream();

                CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write);

                cs.Write(inputText, 0, inputText.Length);
                cs.FlushFinalBlock();

                strPwd = Encoding.Default.GetString(ms.ToArray()).ToLower();
            }
            catch
            {
                strPwd = "密文有误";
            }
            return strPwd;

        }
        #endregion
    }
}
