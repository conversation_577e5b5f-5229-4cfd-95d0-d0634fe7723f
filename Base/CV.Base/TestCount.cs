﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SQ.Base
{
    public class TestCount
    {
        int _Count = 0;
        /// <summary>
        /// 以原子操作的形式递增指定变量的值并存储结果。
        /// </summary>
        public void Increment()
        {
            System.Threading.Interlocked.Increment(ref _Count);
        }
        public void Decrement()
        {
            System.Threading.Interlocked.Decrement(ref _Count);
        }
        public int GetCount()
        {
            return _Count;
        }
        public override string ToString()
        {
            return _Count.ToString();
        }
    }
}
