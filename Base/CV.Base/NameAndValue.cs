﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SQ.Base
{
    /// <summary>
    /// 名值类型
    /// </summary>
    public class NameAndValue
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="name">名称</param>
        /// <param name="value">值</param>
        public NameAndValue(string name, object value)
        {
            this.Name = name;
            this.Value = value;
        }
        /// <summary>
        /// 名称
        /// </summary>
        public string Name
        {
            get;
            set;
        }
        /// <summary>
        /// 值
        /// </summary>
        public object Value
        {
            get;
            set;
        }


        public static NameAndValue NewNameAndValue(string name, object value)
        {
            return new NameAndValue(name, value);
        }
    }
}
