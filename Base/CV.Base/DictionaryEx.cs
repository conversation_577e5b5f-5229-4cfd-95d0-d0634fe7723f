﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Xml;
using System.Xml.Schema;
using System.Xml.Serialization;

namespace SQ.Base
{
    /// <summary>
    /// Dictionary扩展
    /// 支持XML序列化;支持默认对象(无法根据键获对象时返回默认对象，如果没有明确指定默认对象时将自动将第一个对象作为默认对象)
    /// </summary>
    /// <typeparam name="TKey">键</typeparam>
    /// <typeparam name="TVal">值</typeparam>
    [Serializable]
    public class DictionaryEx<TKey, TVal> : Dictionary<TKey, TVal>, IXmlSerializable
    {
        public readonly ReaderWriterLock ReadWritLocker = new ReaderWriterLock();
        #region 默认值处理
        /// <summary>
        /// 默认值
        /// </summary>
        private TVal m_defautValue;
        /// <summary>
        /// 字典内是否有默认值
        /// </summary>
        private bool hasDefault = false;

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="isdefault"></param>
        public void Add(TKey key, TVal value,bool isdefault=false)
        {
            base.Add(key, value);
            if (this.Count==0 && !hasDefault)
            {
                m_defautValue = value;
            }
            if (isdefault)
            {
                m_defautValue = value;
            }
        }

        /// <summary>
        /// 获取对象或默认值
        /// </summary>
        /// <param name="key">键</param>
        /// <returns></returns>
        public TVal GetItemOrDefault(TKey key)
        {
            TVal result; 
            if (!TryGetValue(key,out result))
            {
                result = m_defautValue;
            }
            return result;
        }

        /// <summary>
        /// 设置默认值
        /// </summary>
        /// <param name="value"></param>
        public void SetDefaut(TVal value)
        {
            m_defautValue = value;
            hasDefault = true;
        }
        #endregion

        #region Xml序列化
        public void WriteXml(XmlWriter write)
        {
            XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
            ns.Add("", "");
            XmlSerializer KeySerializer = new XmlSerializer(typeof(TKey));
            XmlSerializer ValueSerializer = new XmlSerializer(typeof(TVal));
            foreach (KeyValuePair<TKey, TVal> kv in this)
            {
                write.WriteStartElement("item");
                write.WriteStartElement("key");
                KeySerializer.Serialize(write, kv.Key,ns);
                write.WriteEndElement();
                write.WriteStartElement("value");
                ValueSerializer.Serialize(write, kv.Value,ns);
                write.WriteEndElement();
                write.WriteEndElement();
            }
        }

        public void ReadXml(XmlReader reader)
        {
            reader.Read();
            XmlSerializer KeySerializer = new XmlSerializer(typeof(TKey));
            XmlSerializer ValueSerializer = new XmlSerializer(typeof(TVal));

            while (reader.NodeType != XmlNodeType.EndElement)
            {
                reader.ReadStartElement("item");
                reader.ReadStartElement("key");
                TKey tk = (TKey)KeySerializer.Deserialize(reader);
                reader.ReadEndElement();
                reader.ReadStartElement("value");
                TVal vl = (TVal)ValueSerializer.Deserialize(reader);
                reader.ReadEndElement();
                reader.ReadEndElement();
                this.Add(tk, vl);
                reader.MoveToContent();
            }
            reader.ReadEndElement();
        }
        public XmlSchema GetSchema()
        {
            return null;
        }
        #endregion
    }
}
