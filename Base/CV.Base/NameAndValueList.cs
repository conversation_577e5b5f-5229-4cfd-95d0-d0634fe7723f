﻿using System;
using System.Collections.Generic;

namespace SQ.Base
{
    /// <summary>
    /// 名值类型
    /// </summary>
    public class NameAndValueList : List<NameAndValue>
    {
        public void AddNew(string name, object value, bool Escape = true)
        {
            if (Escape)
            {
                value = EscapeVal(value);
            }
            Add(NameAndValue.NewNameAndValue(name, value));
        }

        private object EscapeVal(object value)
        {
            if (value == null)
                return DBNull.Value;
            if (value.GetType() == typeof(UInt64) || value.GetType() == typeof(UInt32) || value.GetType() == typeof(UInt16))
            {
                value = Convert.ToInt32(value);
            }
            return value;
        }
    }
}
