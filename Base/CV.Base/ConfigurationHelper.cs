﻿using System;
using System.Collections.Generic;
using System.Text;

#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using Microsoft.Extensions.Configuration;
#endif

namespace SQ.Base
{
    public class ConfigurationHelper
    {
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
        public static IConfigurationRoot Config
        {
            get
            {
                if (config == null)
                {
                    ReLoad();
                }
                return config;
            }
        }

        public static void ReLoad()
        {
            var configBasePath = FileHelp.GetMyConfPath() + "/appsettings.json";
            var builder = new ConfigurationBuilder().AddJsonFile(configBasePath);
            config = builder.Build();
        }
        private static IConfigurationRoot config;

        public static string GetAppSetting(string key)
        {
            return Config.GetSection("AppSettings:" + key).Value;
        }
        public static string GetConnectionString(string key)
        {
            return Config.GetConnectionString(key);
        }
        public static IConfigurationSection GetSection(string key)
        {
            return Config.GetSection(key);
        }
#endif

#if NETFRAMEWORK
        public static string GetAppSetting(string key)
        {
            return System.Configuration.ConfigurationManager.AppSettings[key];
        }
        public static string GetConnectionString(string key)
        {
            return System.Configuration.ConfigurationManager.ConnectionStrings[key].ConnectionString;
        }
        public static object GetSection(string key)
        {
            return System.Configuration.ConfigurationManager.GetSection(key);
        }
#endif
    }
}