﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace SQ.Base.Logs
{
    public class FileLogReader
    {
        /// <summary>
        /// 从文件结尾获取指定行数日志
        /// </summary>
        /// <param name="filePath">日志文件目录</param>
        /// <param name="line">最大行数</param>
        /// <returns></returns>
        public static List<string> GetFileLogs(string filePath, int line)
        {
            List<string> data = new List<string>(line);
            if (!Path.IsPathRooted(filePath))
            {
                filePath = SQ.Base.FileHelp.GetMyConfPath() + filePath;
            }
            if (System.IO.File.Exists(filePath))
            {
                using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    List<byte> lstbyte = new List<byte>();

                    var bufferSize = 4096;
                    if (fs.Length < 4096)
                    {
                        fs.Seek(0, SeekOrigin.Begin);
                    }
                    else
                    {
                        fs.Seek(-bufferSize, SeekOrigin.End);
                    }
                    byte[] bts = new byte[bufferSize];
                    bool flag = true;
                    while (line > 0)
                    {
                        var len = fs.Read(bts, 0, bufferSize);
                        if (len == 0)
                        {
                            break;
                        }
                        for (int i = len - 1; i >= 0; i--)
                        {
                            //换行符
                            if (bts[i] == 0xA)
                            {
                                if (flag && lstbyte.Count == 0)
                                {
                                    //第一个空行 忽略
                                    flag = false;
                                    continue;
                                }
                                //去掉/r
                                if (lstbyte.Count > 0 && lstbyte[lstbyte.Count - 1] == 0xD)
                                {
                                    lstbyte.RemoveAt(lstbyte.Count - 1);
                                }
                                if (lstbyte.Count > 0)
                                {
                                    //if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                                    //{
                                    //    data.Add(Encoding.GetEncoding("gb2312").GetString(lstbyte.ToArray()));
                                    //}
                                    //else
                                    {
                                        data.Add(Encoding.Default.GetString(lstbyte.ToArray()));
                                    }
                                }
                                else
                                {
                                    data.Add("");
                                }
                                line--;
                                if (line <= 0)
                                {
                                    break;
                                }
                                lstbyte.Clear();
                            }
                            else
                            {
                                lstbyte.Insert(0, bts[i]);
                            }
                        }
                        if (len != bufferSize || line <= 0)
                        {
                            break;
                        }
                        if (fs.Position - bufferSize < bufferSize)
                        {
                            bufferSize = (int)(fs.Position - bufferSize);
                        }
                        fs.Seek(-bufferSize * 2, SeekOrigin.Current);
                    }
                    fs.Close();
                }
            }
            return data;
        }
        /// <summary>
        /// 获取当前log4net写的文件路径
        /// </summary>
        /// <param name="i">第几个Appender</param>
        /// <returns></returns>
        public static string GetNowLog4netFile(int i = 0)
        {
            var logger = SQ.Base.Log.Log4.Logger as log4net.Repository.Hierarchy.Logger;
            if (logger != null && logger.Appenders.Count > i)
            {
                var ap = logger.Appenders[i] as log4net.Appender.FileAppender;
                if (ap != null)
                {
                    return ap.File;
                }
            }
            return null;
        }

    }
}
