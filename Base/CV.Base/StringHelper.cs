﻿using System;
using System.Text;
using System.Text.RegularExpressions;

namespace SQ.Base
{
    public static class StringHelper
    {
        /// <summary>
        /// 过滤危险字符
        /// </summary>
        /// <param name="input_text">要过滤的字符串</param>
        /// <param name="max_length">字符串最大长度</param>
        /// <returns></returns>
        public static string InputText(this string input_text)
        {
            return InputText(input_text, input_text.Length);
        }
        /// <summary>
        /// 过滤危险字符
        /// </summary>
        /// <param name="input_text">要过滤的字符串</param>
        /// <param name="max_length">字符串最大长度</param>
        /// <returns></returns>
        public static string InputText(this string input_text, int max_length)
        {
            StringBuilder tmp_str = new StringBuilder();

            if ((input_text != null) && (input_text != String.Empty))//清除字符串空格
            {
                input_text = input_text.Trim();

                if (input_text.Length > max_length)   //长度太大时只取到最大长度
                    input_text = input_text.Substring(0, max_length);

                for (int i = 0; i < input_text.Length; i++)
                {
                    switch (input_text[i])
                    {
                        case '"':
                            tmp_str.Append("");
                            break;
                        case '<':
                            tmp_str.Append("");
                            break;
                        case '>':
                            tmp_str.Append("");
                            break;
                        case '{':
                            tmp_str.Append("");
                            break;
                        case '}':
                            tmp_str.Append("");
                            break;
                        default:
                            tmp_str.Append(input_text[i]);
                            break;
                    }
                }
                tmp_str.Replace("'", " ");
            }
            return tmp_str.ToString();

        }

        //全角转半角
        /// <summary>
        /// 全角转半角
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string ToDBC(this string input)
        {
            char[] c = input.ToCharArray();
            for (int i = 0; i < c.Length; i++)
            {
                if (c[i] == 12288)
                {
                    c[i] = (char)32;
                    continue;
                }
                if (c[i] > 65280 && c[i] < 65375)
                    c[i] = (char)(c[i] - 65248);
            }
            return new string(c);
        }

        /// <summary>
        /// 加密指定字符串
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string Encrypt(this string input)
        {
            //当前直接返回
            //return input;
            return SQ.Base.EnDecrypt.EnDecrypt.MD5(input);


            //System.Web.Security.FormsAuthentication.HashPasswordForStoringInConfigFile(input, System.Web.Configuration.FormsAuthPasswordFormat.MD5.ToString());
        }
        /// <summary>
        /// 获取单个字符ASCII码
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static byte Asc(this string str)
        {
            return System.Text.Encoding.ASCII.GetBytes(str)[0];
        }
        /// <summary>
        /// 获取整个字符串ASCII码
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static byte[] AscAll(this string str)
        {
            return System.Text.Encoding.ASCII.GetBytes(str);
        }

        public static bool IsRightIP(this string ip)
        {
            if (Regex.IsMatch(ip, "[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}"))
            {
                string[] ips = ip.Split('.');
                for (int i = 0; i < ips.Length; i++)
                {
                    if (int.Parse(ips[i]) > 256)
                    {
                        return false;
                    }
                }
                return true;
            }
            else
            {
                return false;
            }
        }

        public static string Encrypt(this string input, byte key)
        {
            var data = Encoding.UTF8.GetBytes(input);
            byte[] encrydata = new byte[data.Length];
            for (int i = 0; i < data.Length; i++)
            {
                encrydata[i] = (byte)(data[i] ^ key);
            }
            return Convert.ToBase64String(encrydata);
        }

        public static string Decrypt(this string input, byte key)
        {
            var data = Convert.FromBase64String(input);
            byte[] decryptdata = new byte[data.Length];
            for (int i = 0; i < data.Length; i++)
            {
                decryptdata[i] = (byte)(data[i] ^ key);
            }
            return Encoding.UTF8.GetString(decryptdata);
        }

        public static byte[] ToUtf8Bytes(this string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return new byte[0];
            }
            else
            {
                return Encoding.UTF8.GetBytes(value);
            }
        }


        public static string StrFixLen(this string str, int len, bool TrimStart0 = false)
        {
            if (TrimStart0)
            {
                str = str.TrimStart('0');
            }
            if (str.Length < len)
            {
                str = str.PadLeft(len, '0');
            }
            else if (str.Length > len)
            {
                str = str.Substring(0, len);
            }
            return str;
        }
        /// <summary>
        /// 忽略大小写匹配
        /// </summary>
        /// <param name="str"></param>
        /// <param name="cp"></param>
        /// <returns></returns>
        public static bool IgnoreEquals(this string str, string cp)
        {
            if (str == null)
            {
                return cp == null;
            }
            else
            {
                return str.Equals(cp, StringComparison.CurrentCultureIgnoreCase);
            }
        }
        public static string DeEncryptByBase64(this string base64)
        {
            var buff = Convert.FromBase64String(base64);
            return System.Text.Encoding.UTF8.GetString(buff);
        }
        public static string EncryptToBase64(this string str)
        {
            var buff = Encoding.UTF8.GetBytes(str);
            return Convert.ToBase64String(buff);
        }
        /// <summary>  
        /// 非法字符转换  
        /// </summary>  
        /// <param name="str">携带(特殊字符)字符串</param>  
        /// <returns></returns>  
        public static string SafeReplace(this string str)
        {
            char[] codes = { ',', '\'', ';', ':', '/', '?', '<', '>', '.', '#', '%','&','?',
                             '^', '\\', '@', '*', '~', '`', '$', '{', '}', '[', ']' ,'"'};
            for (int i = 0; i < codes.Length; i++)
            {
                str = str.Replace(codes[i], char.MinValue);
            }

            return str;
        }
    }
}
