﻿using System;
using System.IO;
using System.Net;
using System.Text;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.IO.Compression;
using System.Threading.Tasks;
using System.Threading;
using System.Collections.Generic;
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using System.Net.Http;
#endif

namespace SQ.Base
{
    public class HttpHelper
    {
        #region 委托 事件
        public delegate void dgtProgValueChanged(long Value);
        /// <summary>
        /// 进度改变事件
        /// </summary>
        public event dgtProgValueChanged OnProgValueChanged;
        #endregion

        #region 属性
        /// <summary>
        /// 代理
        /// </summary>
        public WebProxy Proxy { get; set; }
        /// <summary>
        /// Cookie
        /// </summary>
        public CookieContainer UserCookie { get; set; }
        /// <summary>
        /// 重试次数
        /// </summary>
        public int IAfreshTime { get; set; }
        /// <summary>
        /// 错误次数
        /// </summary>
        public int IErrorTime { get; private set; }

        long m_ProgValue = 0;
        /// <summary>
        /// 当前读取字节
        /// </summary>
        public long ProgValue
        {
            get { return m_ProgValue; }
            private set
            {
                m_ProgValue = value;
                if (OnProgValueChanged != null)
                {
                    OnProgValueChanged(value);
                }
            }
        }
        /// <summary>
        /// 待读取最大字节
        /// </summary>
        public long ProgMaximum { get; private set; }

        #endregion

        #region 方法
        public void DownloadFile(string URL, string fileName = null)
        {
            _GetHTML(URL, "*/*", System.Text.Encoding.UTF8, 1024, fileName: fileName);
        }
        #region Get

        /// <summary>
        /// 获取HTML
        /// </summary>
        /// <param name="URL">地址</param>
        /// <param name="Accept">Accept请求头</param>
        /// <returns>Html代码</returns>
        public string GetHTML(string URL, string Accept)
        {
            return GetHTML(URL, Accept, System.Text.Encoding.UTF8);
        }
        public string GetHTML(string URL, string Accept, string Referer)
        {
            return _GetHTML(URL, Accept, System.Text.Encoding.UTF8, 1024, Referer);
        }
        /// <summary>
        /// 获取HTML
        /// </summary>
        /// <param name="URL">地址</param>
        /// <param name="Accept">Accept请求头</param>
        /// <param name="encoding">字符编码</param>
        /// <returns>Html代码</returns>
        public string GetHTML(string URL, string Accept, Encoding encoding)
        {
            return GetHTML(URL, Accept, encoding, 1024);
        }
        /// <summary>
        /// 获取HTML
        /// </summary>
        /// <param name="URL">地址</param>
        /// <param name="Accept">Accept请求头</param>
        /// <param name="encoding">字符编码</param>
        /// <param name="bufflen">数据包大小</param>
        /// <returns>Html代码</returns>
        public string GetHTML(string URL, string Accept, Encoding encoding, int bufflen)
        {
            IErrorTime = 0;
            return _GetHTML(URL, Accept, encoding, bufflen);
        }
        /// <summary>
        /// 获取HTML
        /// </summary>
        /// <param name="URL">地址</param>
        /// <param name="headers">请求头</param>
        /// <param name="Accept">Accept请求头</param>
        /// <param name="bufflen">数据包大小</param>
        /// <returns>Html代码</returns>
        public string GetHTML(string URL, DictionaryEx<string, string> headers, string Accept, int bufflen = 1024)
        {
            return _GetHTML(URL, headers, Accept, Encoding.UTF8, bufflen);
        }
        /// <summary>
        /// 获取HTML
        /// </summary>
        /// <param name="URL">地址</param>
        /// <param name="Accept">Accept请求头</param>
        /// <param name="encoding">字符编码</param>
        /// <param name="bufflen">数据包大小</param>
        /// <returns>Html代码</returns>
        public string GetHTML(string URL, DictionaryEx<string, string> headers, string Accept, Encoding encoding, int bufflen = 1024)
        {
            IErrorTime = 0;
            return _GetHTML(URL, headers, Accept, encoding, bufflen);
        }
        public X509Certificate ClientCert { get; set; }
        //static X509Certificate GetCertificate()
        //{
        //    var thumbPrint = "ThumbPrint";

        //    var certificateStore = new X509Store();
        //    certificateStore.Open(OpenFlags.ReadOnly);

        //    foreach (var storeCertificate in certificateStore.Certificates)
        //    {
        //        if (storeCertificate.Thumbprint.ToLower(System.Globalization.CultureInfo.CurrentCulture) == thumbPrint.ToLower(System.Globalization.CultureInfo.CurrentCulture))
        //        {
        //            return storeCertificate;
        //        }
        //    }
        //    certificateStore.Close();
        //    return null;
        //}
        /// <summary>
        /// 获取HTML
        /// </summary>
        /// <param name="URL">地址</param>
        /// <param name="Accept">Accept请求头</param>
        /// <param name="encoding">字符编码</param>
        /// <param name="bufflen">数据包大小</param>
        /// <returns>Html代码</returns>
        private string _GetHTML(string URL, string Accept, Encoding encoding, int bufflen, string Referer = null, string fileName = null)
        {
            HttpWebRequest MyRequest = null;// = (HttpWebRequest)HttpWebRequest.Create(URL);
            try
            {
                MyRequest = CreateRequest(URL);

                MyRequest.Proxy = Proxy;
                MyRequest.Accept = Accept;
                MyRequest.Referer = Referer;
                MyRequest.KeepAlive = false;
                if (UserCookie == null)
                {
                    UserCookie = new CookieContainer();
                }
                MyRequest.Headers.Add("Accept-Language", "zh-CN,zh;q=0.8");
                MyRequest.Headers.Add("Accept-Encoding", "gzip");
                MyRequest.CookieContainer = UserCookie;
                HttpWebResponse MyResponse = (HttpWebResponse)MyRequest.GetResponse();
                var html = _GetHTML(MyResponse, encoding, bufflen, fileName);
                MyRequest = null;
                return html;
            }
            catch (System.Net.WebException erro)
            {
                Log.WriteLog4Ex("_GetHTML", erro);
                if (erro.Status == WebExceptionStatus.Timeout && IAfreshTime - IErrorTime > 0)
                {
                    IErrorTime++;
                    return _GetHTML(URL, Accept, encoding, bufflen, fileName);
                }
                return null;
            }
            catch (System.Exception erro)
            {
                Log.WriteLog4Ex("_GetHTML", erro);
                if (erro.Message.Contains("连接") && IAfreshTime - IErrorTime > 0)
                {
                    IErrorTime++;
                    return _GetHTML(URL, Accept, encoding, bufflen, fileName);
                }
                return null;
            }
            finally
            {
                if (MyRequest != null)
                {
                    MyRequest.Abort();
                }

            }
        }

        private HttpWebRequest CreateRequest(string URL)
        {
            HttpWebRequest MyRequest;
            //如果是发送HTTPS请求 
            if (URL.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                ServicePointManager.CheckCertificateRevocationList = false;
                ServicePointManager.CheckCertificateRevocationList = false;
#if NET45
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
#endif
                //ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11;
                //ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                MyRequest = HttpWebRequest.CreateHttp(URL);
                MyRequest.ProtocolVersion = HttpVersion.Version11;
                MyRequest.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);

                if (ClientCert != null)
                {
                    MyRequest.ClientCertificates.Add(ClientCert);
                }
            }
            else
            {
                MyRequest = HttpWebRequest.CreateHttp(URL);
            }

            return MyRequest;
        }

        /// <summary>
        /// 获取HTML
        /// </summary>
        /// <param name="URL">地址</param>
        /// <param name="Accept">Accept请求头</param>
        /// <param name="encoding">字符编码</param>
        /// <param name="bufflen">数据包大小</param>
        /// <returns>Html代码</returns>
        private string _GetHTML(string URL, DictionaryEx<string, string> headers, string Accept, Encoding encoding, int bufflen, string Referer = null, string fileName = null)
        {
            HttpWebRequest MyRequest = null;// = (HttpWebRequest)HttpWebRequest.Create(URL);
            try
            {
                MyRequest = CreateRequest(URL);

                MyRequest.Proxy = Proxy;
                MyRequest.Accept = Accept;
                MyRequest.Referer = Referer;
                if (UserCookie == null)
                {
                    UserCookie = new CookieContainer();
                }
                MyRequest.Headers.Add("Accept-Language", "zh-CN,zh;q=0.8");
                MyRequest.Headers.Add("Accept-Encoding", "gzip");
                MyRequest.CookieContainer = UserCookie;
                SetHeadersByDict(headers, MyRequest);

                HttpWebResponse MyResponse = (HttpWebResponse)MyRequest.GetResponse();

                var html = _GetHTML(MyResponse, encoding, bufflen, fileName);
                MyRequest = null;
                return html;
            }
            catch (System.Net.WebException erro)
            {
                Log.WriteLog4Ex("_GetHTML", erro);
                if (erro.Status == WebExceptionStatus.Timeout && IAfreshTime - IErrorTime > 0)
                {
                    IErrorTime++;
                    return _GetHTML(URL, Accept, encoding, bufflen);
                }
                return null;
            }
            catch (System.Exception erro)
            {
                Log.WriteLog4Ex("_GetHTML", erro);
                if (erro.Message.Contains("连接") && IAfreshTime - IErrorTime > 0)
                {
                    IErrorTime++;
                    return _GetHTML(URL, Accept, encoding, bufflen);
                }
                return null;
            }
            finally
            {
                if (MyRequest != null)
                {
                    MyRequest.Abort();
                }
            }
        }

        private void SetHeadersByDict(DictionaryEx<string, string> headers, HttpWebRequest MyRequest)
        {
            if (headers != null && headers.Count > 0)
            {
                foreach (var item in headers)
                {
                    switch (item.Key)
                    {
                        case "Referer":
                            MyRequest.Referer = item.Value;
                            break;
                        case "Accept":
                            MyRequest.Accept = item.Value;
                            break;
                        case "Connection":
                            MyRequest.Connection = item.Value;
                            break;
                        case "Content-Length":
                            MyRequest.ContentLength = Convert.ToInt64(item.Value);
                            break;
                        case "Content-Type":
                            MyRequest.ContentType = item.Value;
                            break;
                        case "Expect":
                            MyRequest.Expect = item.Value;
                            break;
                        case "Date":
                            MyRequest.Date = Convert.ToDateTime(item.Value);
                            break;
                        case "Host":
                            MyRequest.Host = item.Value;
                            break;
                        case "If-Modified-Since":
                            MyRequest.IfModifiedSince = Convert.ToDateTime(item.Value);
                            break;
                        case "Transfer-Encoding":
                            MyRequest.TransferEncoding = item.Value;
                            break;
                        case "User-Agent":
                            MyRequest.UserAgent = item.Value;
                            break;
                        default:
                            if (!WebHeaderCollection.IsRestricted(item.Key))
                                MyRequest.Headers.Set(item.Key, item.Value);
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// 获取HTML
        /// </summary>
        /// <param name="MyResponse"></param>
        /// <param name="encoding">字符编码</param>
        /// <param name="bufflen">数据包大小</param>
        /// <returns></returns>
        private string _GetHTML(HttpWebResponse MyResponse, Encoding encoding, int bufflen, string fileName = null)
        {
            using (MyResponse)
            {
                using (Stream stream = MyResponse.GetResponseStream())
                {
                    Stream MyStream;
                    if (MyResponse.ContentEncoding != null && MyResponse.ContentEncoding.ToLower().Contains("gzip"))
                    {
                        MyStream = new GZipStream(stream, CompressionMode.Decompress);
                    }
                    else
                    {
                        MyStream = stream;
                    }
                    FileStream fileStream = null;
                    if (fileName != null)
                    {
                        var tmpPath = System.IO.Path.GetTempPath() + "/httpdown";
                        if (!Directory.Exists(tmpPath))
                        {
                            Directory.CreateDirectory(tmpPath);
                        }
                        tmpPath += "/" + Path.GetFileName(fileName) + "." + DateTime.Now.Ticks + ".tmp";

                        fileStream = File.Create(tmpPath);
                    }

                    ProgMaximum = MyResponse.ContentLength;
                    string result = null;
                    long totalDownloadedByte = 0;
                    byte[] by = new byte[bufflen];
                    int osize = MyStream.Read(by, 0, by.Length);
                    System.Collections.Generic.List<byte> lst = new System.Collections.Generic.List<byte>(1024);
                    while (osize > 0)
                    {
                        if (fileStream != null)
                        {
                            fileStream.Write(by, 0, osize);
                        }
                        totalDownloadedByte = osize + totalDownloadedByte;
                        for (int i = 0; i < osize; i++)
                        {
                            lst.Add(by[i]);
                        }
                        ProgValue = totalDownloadedByte;
                        osize = MyStream.Read(by, 0, by.Length);

                    }
                    result = encoding.GetString(lst.ToArray());
                    MyStream.Close();
                    if (fileStream != null)
                    {
                        fileStream.Flush();
                        fileStream.Close();
                        File.Move(fileStream.Name, fileName);
                    }
                    //Log.WriteLog4(result, LOGTYPE.DEBUG);
                    return result;
                }
            }
        }
        #endregion


        #region GetImg

        public System.Drawing.Bitmap Getimg(string URL, string Accept)
        {
            return _GetBit(URL, Accept);
        }
        /// <summary>
        /// 获取HTML
        /// </summary>
        /// <param name="URL">地址</param>
        /// <param name="Accept">Accept请求头</param>
        /// <returns>Html代码</returns>
        private System.Drawing.Bitmap _GetBit(string URL, string Accept)
        {
            HttpWebRequest MyRequest = (HttpWebRequest)HttpWebRequest.Create(URL);
            MyRequest.Proxy = Proxy;
            MyRequest.Accept = Accept;
            if (UserCookie == null)
            {
                UserCookie = new CookieContainer();
            }
            MyRequest.CookieContainer = UserCookie;
            HttpWebResponse MyResponse = (HttpWebResponse)MyRequest.GetResponse();
            return _GetBit(MyResponse);
        }

        /// <summary>
        /// 获取图像
        /// </summary>
        /// <param name="MyResponse"></param>
        /// <returns></returns>
        private System.Drawing.Bitmap _GetBit(HttpWebResponse MyResponse)
        {
            using (Stream MyStream = MyResponse.GetResponseStream())
            {
                return new System.Drawing.Bitmap(MyStream);
            }
        }

        #endregion

        #region Post
        /// <summary>
        /// 回发(字符编码默认UTF-8)
        /// </summary>
        /// <param name="URL">回发地址</param>
        /// <param name="PostData">参数</param>
        /// <returns>Html代码</returns>
        public string PostPage(string URL, string PostData)
        {
            return PostPage(URL, PostData, System.Text.Encoding.UTF8);
        }

        public string PostPage(string URL, string PostData, string Referer)
        {
            return _PostPage(URL, PostData, System.Text.Encoding.UTF8, null, Referer);
        }
        /// <summary>
        /// 回发
        /// </summary>
        /// <param name="URL">回发地址</param>
        /// <param name="PostData">参数</param>
        /// <param name="encoding">字符编码</param>
        /// <returns>Html代码</returns>
        public string PostPage(string URL, string PostData, Encoding encoding)
        {
            return PostPage(URL, PostData, encoding, null);
        }
        /// <summary>
        /// 回发
        /// </summary>
        /// <param name="URL">回发地址</param>
        /// <param name="PostData">参数</param>
        /// <param name="encoding">字符编码</param>
        /// <returns>Html代码</returns>
        public string PostPage(string URL, string PostData, Encoding encoding, string ContentType)
        {
            IErrorTime = 0;
            return _PostPage(URL, PostData, encoding, ContentType);
        }
        public string PostPage(string URL, string PostData, Encoding encoding = null, string ContentType = null, string Referer = null, string Accept = "text/html,application/xhtml+xml,application/xml")
        {
            if (encoding == null)
            {
                encoding = System.Text.Encoding.UTF8;
            }
            return _PostPage(URL, PostData, encoding, ContentType, Referer, Accept);
        }

        public string PostPage(string URL, string PostData, DictionaryEx<string, string> headers = null, Encoding encoding = null, string ContentType = null, string Referer = null, string Accept = "text/html,application/xhtml+xml,application/xml")
        {
            if (encoding == null)
            {
                encoding = System.Text.Encoding.UTF8;
            }
            return _PostPage(URL, PostData, headers, encoding, ContentType, Referer, Accept);
        }
        /// <summary>
        /// 回发
        /// </summary>
        /// <param name="URL">回发地址</param>
        /// <param name="PostData">参数</param>
        /// <param name="encoding">字符编码</param>
        /// <returns>Html代码</returns>
        private string _PostPage(string URL, string PostData, Encoding encoding, string ContentType, string Referer = null, string Accept = "text/html,application/xhtml+xml,application/xml")
        {
            try
            {
                if (ContentType == null)
                {
                    ContentType = "application/x-www-form-urlencoded";
                }
                HttpWebRequest MyRequest = CreateRequest(URL);
                MyRequest.Proxy = Proxy;
                if (UserCookie == null)
                {
                    UserCookie = new CookieContainer();
                }
                MyRequest.ServicePoint.Expect100Continue = false;
                MyRequest.CookieContainer = UserCookie;
                MyRequest.Method = "POST";
                MyRequest.ContentType = ContentType;
                MyRequest.Headers.Add("Accept-Language", "zh-cn,zh");
                MyRequest.Headers.Add("Accept-Charset", "GB2312,utf-8");
                MyRequest.Headers.Add("Accept-Encoding", "gzip");
                //MyRequest.Headers.Add("Accept-Encoding", "gzip, deflate");
                MyRequest.Accept = Accept;
                MyRequest.KeepAlive = true;
                MyRequest.UserAgent = "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:********) Gecko/20101203 Firefox/3.6.13 (.NET CLR 3.5.30729)";
                MyRequest.Referer = Referer;
                if (PostData == null)
                {
                    PostData = "";
                }
                byte[] b = encoding.GetBytes(PostData);
                MyRequest.ContentLength = b.Length;
                using (System.IO.Stream sw = MyRequest.GetRequestStream())
                {
                    try
                    {
                        sw.Write(b, 0, b.Length);
                    }
                    catch
                    {
                    }
                }
                HttpWebResponse MyResponse = (HttpWebResponse)MyRequest.GetResponse();
                return _GetHTML(MyResponse, encoding, 1024);
            }
            catch (System.Net.WebException erro)
            {
                Log.WriteLog4Ex("_PostPage", erro);
                if (erro.Status == WebExceptionStatus.Timeout && IAfreshTime - IErrorTime > 0)
                {
                    IErrorTime++;
                    return _PostPage(URL, PostData, encoding, ContentType);
                }
                return null;
            }
            catch (System.Exception erro)
            {
                Log.WriteLog4Ex("_PostPage", erro);
                if (erro.Message.Contains("连接") && IAfreshTime - IErrorTime > 0)
                {
                    IErrorTime++;
                    return _PostPage(URL, PostData, encoding, ContentType);
                }
                else //添加判断，如果首次无法连接到服务器的话，返回null 登录页面判断是否为null
                {
                    return null;
                }
                //出现异常 返回null ,不在throw 抛出异常
            }
        }
        /// <summary>
        /// 回发
        /// </summary>
        /// <param name="URL">回发地址</param>
        /// <param name="PostData">参数</param>
        /// <param name="encoding">字符编码</param>
        /// <returns>Html代码</returns>
        private string _PostPage(string URL, string PostData, DictionaryEx<string, string> headers, Encoding encoding, string ContentType, string Referer = null, string Accept = "text/html,application/xhtml+xml,application/xml")
        {
            try
            {
                if (ContentType == null)
                {
                    ContentType = "application/x-www-form-urlencoded";
                }
                HttpWebRequest MyRequest = CreateRequest(URL);
                MyRequest.Proxy = Proxy;
                if (UserCookie == null)
                {
                    UserCookie = new CookieContainer();
                }
                MyRequest.ServicePoint.Expect100Continue = false;
                MyRequest.CookieContainer = UserCookie;
                MyRequest.Method = "POST";
                MyRequest.ContentType = ContentType;
                MyRequest.Headers.Add("Accept-Language", "zh-cn,zh");
                MyRequest.Headers.Add("Accept-Charset", "GB2312,utf-8");
                MyRequest.Headers.Add("Accept-Encoding", "gzip");
                //MyRequest.Headers.Add("Accept-Encoding", "gzip, deflate");
                MyRequest.Accept = Accept;
                MyRequest.KeepAlive = true;
                MyRequest.UserAgent = "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:********) Gecko/20101203 Firefox/3.6.13 (.NET CLR 3.5.30729)";
                MyRequest.Referer = Referer;

                SetHeadersByDict(headers, MyRequest);
                if (PostData == null)
                {
                    PostData = "";
                }
                byte[] b = encoding.GetBytes(PostData);
                MyRequest.ContentLength = b.Length;
                using (System.IO.Stream sw = MyRequest.GetRequestStream())
                {
                    try
                    {
                        sw.Write(b, 0, b.Length);
                    }
                    catch
                    {
                    }
                }
                HttpWebResponse MyResponse = (HttpWebResponse)MyRequest.GetResponse();
                return _GetHTML(MyResponse, encoding, 1024);
            }
            catch (System.Net.WebException erro)
            {
                Log.WriteLog4Ex("_PostPage", erro);
                if (erro.Status == WebExceptionStatus.Timeout && IAfreshTime - IErrorTime > 0)
                {
                    IErrorTime++;
                    return _PostPage(URL, PostData, encoding, ContentType);
                }
                return null;
            }
            catch (System.Exception erro)
            {
                Log.WriteLog4Ex("_PostPage", erro);
                if (erro.Message.Contains("连接") && IAfreshTime - IErrorTime > 0)
                {
                    IErrorTime++;
                    return _PostPage(URL, PostData, encoding, ContentType);
                }
                else //添加判断，如果首次无法连接到服务器的话，返回null 登录页面判断是否为null
                {
                    return null;
                }
                //出现异常 返回null ,不在throw 抛出异常
            }
        }
        #endregion

        private static bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        {
            return true; //总是接受 
        }

        #region cookie
        public string GetCookieStr(Uri url)
        {
            if (UserCookie == null)
            {
                return "";
            }
            return UserCookie.GetCookieHeader(url);
            //StringBuilder line = new StringBuilder();
            //foreach (var cookie in UserCookie.GetCookies(url))
            //    line.AppendLine(cookie.ToString());
            //return line.ToString();
        }

        public CookieCollection GetCookieCollection(Uri url)
        {
            if (UserCookie == null)
            {
                return null;
            }
            return UserCookie.GetCookies(url);
        }
        #endregion
        #endregion

        #region IPAddress
        ///<summary>  
        /// 传入域名返回对应的IP
        ///</summary>  
        ///<param name="url">Url</param>  
        ///<returns></returns>  
        public static string getIPByUrl(string url)
        {
            url = url.Replace("http://", "").Replace("https://", "");
            IPHostEntry hostEntry = Dns.GetHostEntry(url);
            IPEndPoint ipEndPoint = new IPEndPoint(hostEntry.AddressList[0], 0);
            return ipEndPoint.Address.ToString();
        }
        #endregion
    }
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP

    public class HttpHelperByHttpClient
    {
        private static HttpClient _http;
        private static HttpClient http
        {
            get
            {
                if (_http == null)
                {
                    _http = GetNewHttp();
                }
                return _http;
            }
        }

        private static HttpClient GetNewHttp()
        {
            var http = new HttpClient();
            http.DefaultRequestHeaders.Add("User-Agent", @"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36");
            http.DefaultRequestHeaders.Add("Accept", @"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
            http.DefaultRequestHeaders.Add("Accept-Encoding", "gzip");
            //_http.DefaultRequestHeaders.Add("User-Agent", @"Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)");
            //_http.DefaultRequestHeaders.Add("Accept", @"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");

            return http;
        }

        public async static Task<string> HttpRequestHtml(string Url, bool isPost, CancellationToken token, DictionaryEx<string, string> headers = null, string data = null, Encoding encoding = null, byte[] btsdata = null)
        {
            if (encoding == null)
            {
                encoding = Encoding.UTF8;
            }
            byte[] bts;
            if (btsdata != null)
            {
                bts = await HttpRequest(Url, isPost, headers, btsdata, token).ConfigureAwait(false);
            }
            else if (data == null)
            {
                bts = await HttpRequest(Url, isPost, headers, null, token).ConfigureAwait(false);
            }
            else
            {
                bts = await HttpRequest(Url, isPost, headers, encoding.GetBytes(data), token).ConfigureAwait(false);
            }
            if (bts == null)
            {
                return null;
            }
            return encoding.GetString(bts);
        }

        public static Task<byte[]> HttpRequestHtml2(string Url, bool isPost, CancellationToken token, DictionaryEx<string, string> headers = null, string data = null, Encoding encoding = null, bool errLog = true)
        {
            if (data == null)
            {
                return HttpRequest(Url, isPost, headers, null, token, errLog);
            }
            else
            {
                return HttpRequest(Url, isPost, headers, encoding.GetBytes(data), token, errLog);
            }
        }



        public async static Task<byte[]> HttpRequest(string Url, bool isPost, DictionaryEx<string, string> headers, byte[] data, CancellationToken token, bool errLog = true)
        {
            byte[] result = null;
            try
            {
                HttpResponseMessage message = await HttpRequestRaw(Url, isPost, headers, data, token, errLog);
                if (message != null && message.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    using (message)
                    {
                        using (Stream responseStream = await message.Content.ReadAsStreamAsync().ConfigureAwait(false))
                        {
                            if (responseStream != null)
                            {
                                Stream MyStream;
                                if (message.Content.Headers.ContentEncoding != null
                                    && message.Content.Headers.ContentEncoding.Contains("gzip"))
                                {
                                    MyStream = new GZipStream(responseStream, CompressionMode.Decompress);
                                }
                                else
                                {
                                    MyStream = responseStream;
                                }

                                using (MyStream)
                                {
                                    var ms = new MemoryStream();
                                    await MyStream.CopyToAsync(ms);
                                    result = ms.ToArray();

                                    //var bufflen = 1024;
                                    //byte[] by = new byte[bufflen];
                                    //int osize = await MyStream.ReadAsync(by, 0, by.Length);
                                    //System.Collections.Generic.List<byte> lst = new System.Collections.Generic.List<byte>(bufflen);
                                    //while (osize > 0)
                                    //{
                                    //    for (int i = 0; i < osize; i++)
                                    //    {
                                    //        lst.Add(by[i]);
                                    //    }
                                    //    osize = await MyStream.ReadAsync(by, 0, by.Length);

                                    //}
                                    //result = lst.ToArray();
                                    MyStream.Close();
                                }
                            }
                        }
                    }
                }

            }
            catch (System.Exception ex)
            {
                if (errLog)
                    ErrorLog.WriteLog4Ex("HttpRequest", ex);
            }
            return result;
        }



        public async static Task<HttpResponseMessage> HttpRequestRaw(string Url, bool isPost, DictionaryEx<string, string> headers, byte[] data, CancellationToken token, bool errLog = true)
        {
            try
            {
                HttpResponseMessage message = null;
                if (isPost)
                {
                    using (Stream dataStream = new MemoryStream(data ?? new byte[0]))
                    {
                        using (HttpContent content = new StreamContent(dataStream))
                        {
                            Dictionary<string, string> dtmp = new Dictionary<string, string>();
                            if (headers != null && headers.Count > 0)
                            {
                                foreach (var header in headers)
                                {
                                    if (!content.Headers.TryAddWithoutValidation(header.Key, header.Value))
                                    {
                                        dtmp.Add(header.Key, header.Value);
                                    }
                                }
                            }
                            if (content.Headers.ContentType == null)
                            {
                                content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
                            }
                            if (dtmp.Count > 0)
                            {
                                using (var httptmp = GetNewHttp())
                                {
                                    foreach (var header in dtmp)
                                    {
                                        httptmp.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
                                    }
                                    message = await httptmp.PostAsync(Url, content, token).ConfigureAwait(false);
                                }
                            }
                            else
                            {
                                message = await http.PostAsync(Url, content, token).ConfigureAwait(false);
                            }
                        }
                    }
                }
                else
                {
                    if (headers != null && headers.Count > 0)
                    {
                        using (var httptmp = GetNewHttp())
                        {
                            foreach (var header in headers)
                            {
                                httptmp.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
                            }
                            message = await httptmp.GetAsync(Url, token).ConfigureAwait(false);
                        }
                    }
                    else
                    {
                        message = await http.GetAsync(Url, token).ConfigureAwait(false);
                    }
                }
                return message;

            }
            catch (System.Exception ex)
            {
                if (errLog)
                    ErrorLog.WriteLog4Ex("HttpRequest", ex);
                return null;
            }
        }
    }
#endif
}