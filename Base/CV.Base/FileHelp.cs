﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace SQ.Base
{
    public class FileHelp
    {
        public enum GetDirectoryType
        {
            ByAppDomain,
            ByAssembly
        }
        public static string GetCurrentDirectory(GetDirectoryType type = GetDirectoryType.ByAppDomain)
        {
            switch (type)
            {
                case GetDirectoryType.ByAppDomain:
                    return AddEndPath(AppDomain.CurrentDomain.BaseDirectory);
                case GetDirectoryType.ByAssembly:

                    return AddEndPath(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location));
                default:
                    return AddEndPath(AppDomain.CurrentDomain.BaseDirectory);
            }
        }
        public static string GetCurrentDirectoryByAssembly()
        {
            return GetCurrentDirectory(GetDirectoryType.ByAssembly);
        }
        public static string AddEndPath(string path)
        {
            if (!path.EndsWith("/") && !path.EndsWith("\\"))
            {
                path += "/";
            }
            return path;
        }
        /// <summary>
        /// 获取配置文件路径 优先取环境变量中MyDataPath值 如果找不到则取当前路径
        /// </summary>
        /// <returns></returns>
        public static string GetMyConfPath()
        {
            var path = Environment.GetEnvironmentVariable("MyDataPath");
            if (string.IsNullOrWhiteSpace(path))
            {
                path = GetCurrentDirectory();
            }
            return AddEndPath(path);
        }
        /// <summary>
        ///程序数据路径- C:\ProgramData
        /// </summary>
        /// <returns></returns>
        public static string GetCommonApplicationData()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
        }

        /// <summary>
        /// 用户数据路径
        /// </summary>
        /// <returns></returns>
        public static string GetApplicationData()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        }

        /// <summary>
        /// 用户数据本地路径
        /// </summary>
        /// <returns></returns>
        public static string GetLocalApplicationData()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
        }

        /// <summary>
        /// 用户路径
        /// </summary>
        /// <returns></returns>
        public static string GetUserProfile()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
        }

        /// <summary>
        /// 用户的图片路径
        /// </summary>
        /// <returns></returns>
        public static string GetMyPictures()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.MyPictures);
        }

        /// <summary>
        /// 用户的视频路径
        /// </summary>
        /// <returns></returns>
        public static string GetMyVideos()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.MyVideos);
        }

        /// <summary>
        /// 用户的文档路径
        /// </summary>
        /// <returns></returns>
        public static string GetMyDocuments()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        }

        /// <summary>
        /// IE保护模式下的低权限操作路径(Temporary Internet Files/Low)
        /// 参考：https://blog.csdn.net/xt_xiaotian/article/details/5336809
        /// </summary>
        /// <returns></returns>
        public static string GetTemporaryInternetFiles()
        {
            return GetLocalApplicationData() + "/Microsoft/Windows/Temporary Internet Files/Low";
        }

        /// <summary>
        /// IE保护模式下的低权限操作路径(%userprofile%/AppData/LocalLow)
        /// 参考：https://blog.csdn.net/xt_xiaotian/article/details/5336809
        /// </summary>
        /// <returns></returns>
        public static string GetAppDataLocalLow()
        {
            return GetUserProfile() + "/AppData/LocalLow";
        }

        /// <summary>
        /// 获取系统字体文件路径
        /// </summary>
        /// <returns></returns>
        public static string GetFonts()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.Fonts);
        }

        /// <summary>
        /// 二进制文件读取
        /// </summary>
        /// <param name="FileUrl">文件路径</param>
        /// <returns></returns>
        public static byte[] BinaryRead(string FileUrl)
        {
            List<byte> lst = new List<byte>();
            try
            {
                //文件路径
                String filename = FileUrl;
                //打开文件
                FileStream FStream;
                if (File.Exists(filename))
                {
                    FStream = new FileStream(filename, FileMode.Open);
                }
                else
                {
                    return null;
                }
                int BufferSize = 1048576; //每次读取的字节数 每次读取1MB
                byte[] Buffer = new byte[BufferSize];
                long FileLength = FStream.Length;//文件流的长度
                int ReadCount = (int)(FileLength / BufferSize) + 1; //需要对文件读取的次数
                                                                    //数据读取
                BinaryReader BWrite = new BinaryReader(FStream);
                //br.BaseStream.Seek(0, SeekOrigin.Begin);
                //while (br.BaseStream.Position < br.BaseStream.Length){}
                for (int a = 0; a < ReadCount; a++)
                {
                    Buffer = BWrite.ReadBytes(BufferSize);
                    lst.AddRange(Buffer);
                }
                BWrite.Close();
                BWrite.Close();
                return lst.ToArray();
            }
            catch (System.Exception ex)
            {
                Log.WriteLog4Ex("FileHelp.BinaryRead", ex);
                return null;
            }
        }

        /// <summary>
        /// 二进制文件写入
        /// </summary>
        /// <param name="Bts"></param>
        /// <param name="DirectoryUrl">文件目录路径</param>
        /// <param name="FileName">文件名称</param>
        /// <returns></returns>
        public static bool BinaryWrite(byte[] Bts, string DirectoryUrl, string FileName)
        {
            try
            {
                //文件路径
                string Filepath = DirectoryUrl + "/" + FileName;
                //目录创建
                if (!Directory.Exists(DirectoryUrl))
                    Directory.CreateDirectory(DirectoryUrl);
                //文件创建
                FileStream FStream;
                if (File.Exists(Filepath))
                {
                    FStream = new FileStream(Filepath, FileMode.Append);
                }
                else
                {
                    FStream = new FileStream(Filepath, FileMode.Create);
                }
                //数据写入
                BinaryWriter BWrite = new BinaryWriter(FStream);
                BWrite.Write(Bts);
                BWrite.Close();
                FStream.Close();
                return true;
            }
            catch (System.Exception ex)
            {
                Log.WriteLog4Ex("FileHelp.BinaryWrite", ex);
                return false;
            }
        }

        /// <summary>
        /// 二进制文件删除
        /// </summary>
        /// <param name="FileUrl">文件路径</param>
        public static void FileDelete(string FileUrl)
        {
            try
            {
                //文件路径
                String filename = FileUrl;
                //删除文件
                if (File.Exists(filename))
                {
                    File.Delete(filename);
                }
            }
            catch (System.Exception ex)
            {
                Log.WriteLog4Ex("FileHelp.FileDelete", ex);
            }
        }

        public static string GetHSize(long blen)
        {
            if (blen < 0x400)
            {
                return blen + " B";
            }
            var dblen = Convert.ToDouble(blen);
            if (blen < 0x100000)
            {
                return Math.Round(dblen / 1024, 2) + " KB";
            }
            if (blen < 0x40000000)
            {
                return Math.Round(dblen / 0x100000, 2) + " MB";
            }
            if (blen < 0x10000000000)
            {
                return Math.Round(dblen / 0x40000000, 2) + " GB";
            }
            if (blen < 0x4000000000000)
            {
                return Math.Round(dblen / 0x10000000000, 2) + " TB";
            }
            if (blen < 0x1000000000000000)
            {
                return Math.Round(dblen / 0x4000000000000, 2) + " PB";
            }
            return "Max";

        }
    }
}
