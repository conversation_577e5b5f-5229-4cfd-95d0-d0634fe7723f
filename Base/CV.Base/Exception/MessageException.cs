﻿using System.Runtime.Serialization;
using System;

namespace SQ.Base.Exception
{
    /// <summary>
    /// 消息错误
    /// </summary>
    [Serializable]
    public class MessageException : System.Exception
    {
        #region 构造
        public MessageException()
            : base()
        {
        }

        public MessageException(string message)
            : base(message)
        {
        }

        protected MessageException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }

        public MessageException(string message, System.Exception innerException)
            : base(message, innerException)
        {
        }
        #endregion

        #region 静态方法
        /// <summary>
        /// 创建一个消息错误
        /// </summary>
        /// <returns></returns>
        public static MessageException NewMessageException()
        {
            return new MessageException();
        }
        /// <summary>
        /// 创建一个消息错误
        /// </summary>
        /// <param name="message">消息</param>
        /// <returns></returns>
        public static MessageException NewMessageException(string message)
        {
            return new MessageException(message);
        }
        /// <summary>
        /// 创建一个消息错误
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public static MessageException NewMessageException(SerializationInfo info, StreamingContext context)
        {
            return new MessageException(info, context);
        }
        /// <summary>
        /// 创建一个消息错误
        /// </summary>
        /// <param name="message"></param>
        /// <param name="innerException"></param>
        /// <returns></returns>
        public static MessageException NewMessageException(string message, System.Exception innerException)
        {
            return new MessageException(message, innerException);
        }

        /// <summary>
        /// 抛出一个消息错误
        /// </summary>
        public static void ThrowMessageException()
        {
            throw NewMessageException();
        }
        /// <summary>
        /// 抛出一个消息错误
        /// </summary>
        /// <param name="message"></param>
        public static void ThrowMessageException(string message)
        {
            throw NewMessageException(message);
        }
        public static void ThrowMessageException(SerializationInfo info, StreamingContext context)
        {
            throw NewMessageException(info, context);
        }
        /// <summary>
        /// 抛出一个消息错误
        /// </summary>
        /// <param name="message"></param>
        /// <param name="innerException"></param>
        public static void ThrowMessageException(string message, System.Exception innerException)
        {
            throw NewMessageException(message, innerException);
        }
        #endregion


    }
}
