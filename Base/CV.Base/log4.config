<?xml version="1.0"?>
<configuration>
  <log4net>
    <root>
      <level value="DEBUG" />

    </root>
    <logger name="SQ">
      <level value="DEBUG"/>
      <appender-ref ref="RollingFileAppender" />
    </logger>
    <logger name="ErrLog">
      <level value="DEBUG"/>
      <appender-ref ref="LogForError" />
    </logger>
    <appender name="LogFileAppender" type="log4net.Appender.FileAppender" >
      <param name="File" value="LogFile/log-file.txt" />
      <param name="AppendToFile" value="true" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%t] %-5p %c [%x]  - %m%n" />
      </layout>
    </appender>
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender" >
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%t] %-5p %c [%x] - %m%n" />
      </layout>
    </appender>
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <!--默认为UTF-8编码-->
      <param name="Encoding" value="utf-8" />
      <!--日志文件名-->
      <file value="LogFile/log-file.txt"/>
      <!--是否在文件中追加-->
      <appendToFile value="true"/>
      <!--按照文件的大小进行变换日志文件-->
      <rollingStyle value="Size"/>
      <!--最大变换数量-->
      <maxSizeRollBackups value="100"/>
      <!--最大文件大小-->
      <maximumFileSize value="10MB"/>
      <!--日志文件名是否为静态-->
      <staticLogFileName value="false"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%d [%t] %-5p %c [%x]  - %m%n" />
      </layout>
    </appender>
    <appender name="LogForError" type="log4net.Appender.RollingFileAppender">
      <!--默认为UTF-8编码-->
      <param name="Encoding" value="utf-8" />
      <!--日志文件名-->
      <file value="Error_log/"/>
      <!--<datePattern value="&quot;yyyyMMdd.txt&quot;"/>-->
      <param name="DatePattern" value="yyyyMMdd'.log'" />
      <!--是否在文件中追加-->
      <appendToFile value="true"/>
      <!--按照文件的大小进行变换日志文件-->
      <rollingStyle value="Composite" />
      <!--最大变换数量-->
      <maxSizeRollBackups value="100"/>
      <!--最大文件大小-->
      <maximumFileSize value="10MB"/>
      <!--日志文件名是否为静态-->
      <staticLogFileName value="false"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%d [%t] %-5p %c [%x]  - %m%n" />
      </layout>
    </appender>

  </log4net>
</configuration>
