﻿#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
using System;
using System.Buffers;
using System.Collections.Generic;
using System.IO;
using System.IO.Pipelines;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace SQ.Base.Files
{
    public class PipeFileWrite
    {
        System.IO.Pipelines.Pipe pipe = new System.IO.Pipelines.Pipe();
        System.IO.FileStream fs;
        bool startWrite = false, enableSeek;
        private Task taskRead;
        byte[] lbuf;
        public void InitFile(string path, bool EnableSeek = false)
        {
            fs = new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.Read, 4096, true);
            enableSeek = EnableSeek;
            if (EnableSeek)
            {
                lbuf = new byte[4];
            }
        }
        /// <summary>
        /// 写入数据 未做线程安全
        /// </summary>
        /// <param name="buffer"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        public void Write(byte[] buffer, int offset, int count)
        {
            if (buffer.Length > 0)
            {
                var sm = pipe.Writer.AsStream();
                if (enableSeek)
                {
                    ByteHelper.IntToByte4(buffer.Length, lbuf, 0);
                    sm.Write(lbuf, 0, 4);
                }
                sm.Write(buffer, offset, count);

                if (!startWrite)
                {
                    startWrite = true;
                    taskRead = DoRead();
                }
            }
        }
#if NETSTANDARD2_1_OR_GREATER || NET5_0_OR_GREATER
        public void Write(ReadOnlySpan<byte> buffer)
        {
            if (buffer.Length > 0)
            {
                var sm = pipe.Writer.AsStream();
                if (enableSeek)
                {
                    ByteHelper.IntToByte4(buffer.Length, lbuf, 0);
                    sm.Write(lbuf, 0, 4);
                }
                sm.Write(buffer);

                if (!startWrite)
                {
                    startWrite = true;
                    taskRead = DoRead();
                }
            }
        }
#endif
        public void Seek(long offset)
        {
            if (enableSeek)
            {
                var bts = new byte[9];
                bts[0] = 0xff;
                ByteHelper.ULongToByte8((ulong)offset, bts, 1);
                pipe.Writer.AsStream().Write(bts, 0, bts.Length);
            }
        }
        public async Task SafeStop()
        {
            await pipe.Writer.FlushAsync();
            pipe.Writer.Complete();
            if (taskRead != null)
            {
                await taskRead;
            }
        }

        async Task DoRead()
        {
            try
            {

                while (startWrite)
                {
                    var b = await pipe.Reader.ReadAsync();
                    if (enableSeek)
                    {
                        ReadOnlySequence<byte> buffer = b.Buffer;
                        while (buffer.Length > 4)
                        {
                            if (buffer.First.Span[0] == 0xff)
                            {
                                //Seek
                                if (buffer.Length >= 9)
                                {
                                    var lb = buffer.Slice(1, 8);
                                    var offset = ByteHelper.Byte8ToULong(lb.ToArray(), 0);
                                    fs.Seek((long)offset, SeekOrigin.Begin);
                                    buffer = buffer.Slice(lb.End);
                                }
                            }
                            else
                            {
                                var lb = buffer.Slice(0, 4);
                                var len = ByteHelper.Byte4ToInt(lb.ToArray(), 0);
                                if (buffer.Length >= len + 4)
                                {
                                    var data = buffer.Slice(4, len);

                                    foreach (ReadOnlyMemory<byte> memory in data)
                                    {
#if NETSTANDARD2_0
                    await fs.WriteAsync(memory.ToArray(), 0, memory.Length);
#elif NETSTANDARD2_1_OR_GREATER || NET5_0_OR_GREATER
                                        await fs.WriteAsync(memory);
#endif
                                    }
                                    buffer = buffer.Slice(data.End);
                                }
                                else
                                    break;
                            }
                        }
                        pipe.Reader.AdvanceTo(buffer.Start, buffer.End);
                    }
                    else
                    {

                        foreach (ReadOnlyMemory<byte> memory in b.Buffer)
                        {
#if NETSTANDARD2_0
                    await fs.WriteAsync(memory.ToArray(), 0, memory.Length);
#elif NETSTANDARD2_1_OR_GREATER  || NET5_0_OR_GREATER
                            await fs.WriteAsync(memory);
#endif
                        }
                        pipe.Reader.AdvanceTo(b.Buffer.End);
                    }

                    if (b.IsCompleted || b.IsCanceled)
                    {
                        break;
                    }
                }
                await fs.FlushAsync();
#if NETSTANDARD2_0
            fs.Dispose();
#elif NETSTANDARD2_1_OR_GREATER || NET5_0_OR_GREATER
                await fs.DisposeAsync();
#endif
                startWrite = false;
            }
            catch (System.Exception ex)
            {

            }
        }
    }
}
#else

using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace SQ.Base.Files
{
    public class PipeFileWrite
    {
        System.IO.FileStream fs;
        public void InitFile(string path)
        {
            fs = new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.Read, 4096, true);
        }
        /// <summary>
        /// 写入数据 未做线程安全
        /// </summary>
        /// <param name="buffer"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        public void Write(byte[] buffer, int offset, int count)
        {
            fs.Write(buffer, offset, count);

        }
        public async Task SafeStop()
        {

            await fs.FlushAsync();
            fs.Dispose();
        }
        public void Flush()
        {
            fs.Flush();
        }
    }
}
#endif