﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace SQ.Base
{
    public class ThreadHelper
    {
        public static ThreadStart GetSafeThreadStart(ThreadStart start)
        {
            return () =>
            {
                try
                {
                    start();
                }
                catch (System.Exception ex)
                {
                    Log.WriteLog4Ex("ThreadRun ThreadName" + Thread.CurrentThread.Name, ex);
                }

            };
        }
        public static ParameterizedThreadStart GetSafeParameterizedThreadStart(ParameterizedThreadStart start)
        {
            return (object obj) =>
            {
                try
                {
                    start(obj);
                }
                catch (System.Exception ex)
                {
                    Log.WriteLog4Ex("ThreadRun ThreadName" + Thread.CurrentThread.Name, ex);
                }
            };
        }



    }

    public delegate void ThreadRun<T>(T tag, CancellationToken cancellationToken);
    /// <summary>
    /// 自循环线程
    /// </summary>
    public class ThreadWhile<T>
    {

        public CancellationTokenSource cancelStop = new CancellationTokenSource();
        public Thread thread;
        ThreadRun<T> OnRun;
        public ThreadRun<T> OnFirstRun;

        public T Tag { get; protected set; }
        public bool IsRun { get; protected set; }
        bool CanRun = false;
        public int SleepMs { get; set; }
        public void Run()
        {
            try
            {
                OnFirstRun?.Invoke(Tag, cancelStop.Token);
                while (CanRun && !cancelStop.IsCancellationRequested)
                {
                    try
                    {
                        cancelStop.Token.ThrowIfCancellationRequested();
                        OnRun(Tag, cancelStop.Token);
                    }
                    catch (System.Exception ex)
                    {
                        ErrorLog.WriteLog4Ex("ThreadWhile OnRun ThreadName" + Thread.CurrentThread.Name, ex);
                    }
                    if (SleepMs > 0)
                    {
                        Sleep_Ms(SleepMs);
                    }
                }

            }
            catch (System.Exception ex)
            {
                ErrorLog.WriteLog4Ex("ThreadWhile ThreadName" + Thread.CurrentThread.Name, ex);
            }
            IsRun = false;
        }
        /// <summary>
        /// 睡眠X毫秒(会每隔10秒唤醒一次，防止线程取消长时间不退出)
        /// </summary>
        /// <param name="ms"></param>
        public void Sleep_Ms(int ms)
        {
            if (CanRun && !cancelStop.IsCancellationRequested)
            {
                if (ms > 10000)
                {
                    var sdate = DateTime.Now.AddMilliseconds(ms);
                    while (CanRun && !cancelStop.IsCancellationRequested)
                    {
                        int dmsec = (int)(sdate - DateTime.Now).TotalMilliseconds;
                        if (dmsec > 1000)
                        {
                            Thread.Sleep(1000);
                        }
                        else if (dmsec > 0)
                        {
                            Thread.Sleep(dmsec);
                        }
                        else
                        {
                            break;
                        }
                    }

                }
                else
                {
                    Thread.Sleep(ms);
                }
            }
        }

        public bool StartIfNotRun(ThreadRun<T> OnRun, T tag, string Name = null)
        {
            if (!IsRun)
            {
                return Start(OnRun, tag, Name);
            }
            return false;
        }
        public bool Start(ThreadRun<T> OnRun, T tag, string Name = null)
        {
            CanRun = true;
            IsRun = true;
            this.OnRun = OnRun;
            this.Tag = tag;
            thread = new Thread(Run);
            thread.IsBackground = true;
            if (Name != null)
            {
                thread.Name = Name;
            }
            else
            {
                thread.Name = "ThreadWhile_" + DateTime.Now.Ticks;
            }
            thread.Start();
            return true;
        }
        public bool Stop()
        {
            if (IsRun)
            {
                Abort();
                Join();
                IsRun = false;
                return true;
            }
            return false;
        }


        /// <summary>
        /// 终止线程，抛出System.OperationCanceledException异常。注意 必须线程中有检查CancellationToken才会抛出异常
        /// </summary>
        public void Abort()
        {
            CanRun = false;
            cancelStop.Cancel();
        }
        public void Join()
        {
            while (thread.IsAlive && thread.ManagedThreadId != Thread.CurrentThread.ManagedThreadId)
            {
                Thread.Sleep(100);
            }
        }

    }
}
