﻿using System;
using System.Text;
using System.IO;
using SQ.Base.ArraySegment;

namespace SQ.Base
{
    public static class ByteHelper
    {
        public static Encoding EncodingGB18030 = Encoding.GetEncoding(54936);

        /// <summary>
        /// 将UInt16转化为byte2
        /// </summary>
        /// <param name="i">数值</param>
        /// <param name="bt1">高位</param>
        /// <param name="bt2">低位</param>
        public static void UIntToByte2(UInt16 i, ref byte bt1, ref byte bt2)
        {
            if (i > 255)
            {
                bt1 = Convert.ToByte((0xff00 & i) >> 8);
            }
            else
            {
                bt1 = 0;
            }
            bt2 = Convert.ToByte(0xff & i);
        }

        /// <summary>
        /// 将UInt16转化为byte2
        /// </summary>
        /// <param name="i">数值</param>
        /// <param name="bt1">高位</param>
        /// <param name="bt2">低位</param>
        public static void UIntToByte2(UInt16 i, ref byte[] bts, int offset)
        {
            UIntToByte2(i, bts, offset);
        }
        public static void UIntToByte2(UInt16 i, byte[] bts, int offset)
        {
            if (i > 255)
            {
                bts[0 + offset] = Convert.ToByte((0xff00 & i) >> 8);
            }
            else
            {
                bts[0 + offset] = 0;
            }
            bts[1 + offset] = Convert.ToByte(0xff & i);
        }
        /// <summary>
        /// 将Uint32转化为byte4
        /// </summary>
        /// <param name="i">数值</param>
        /// <param name="bt1">最高位</param>
        /// <param name="bt2">次高位</param>
        /// <param name="bt3">次低位</param>
        /// <param name="bt4">最低位</param>
        /// <remarks></remarks>
        public static void UIntToByte4(uint i, ref byte bt1, ref byte bt2, ref byte bt3, ref byte bt4)
        {
            var bts = BitConverter.GetBytes(i);
            bt1 = bts[3];
            bt2 = bts[2];
            bt3 = bts[1];
            bt4 = bts[0];

            //bt1 = Convert.ToByte((0xff000000 & i) >> 24);
            //bt2 = Convert.ToByte((0xff0000 & i) >> 16);
            //bt3 = Convert.ToByte((0xff00 & i) >> 8);
            //bt4 = Convert.ToByte(0xff & i);
        }

        /// <summary>
        /// 将Uint32转化为byte4
        /// </summary>
        /// <param name="i">数值</param>
        /// <param name="bt1">最高位</param>
        /// <param name="bt2">次高位</param>
        /// <param name="bt3">次低位</param>
        /// <param name="bt4">最低位</param>
        /// <remarks></remarks>
        public static void UIntToByte4(uint i, byte[] bts, int offset)
        {
            var btsTmp = BitConverter.GetBytes(i);

            for (int index = 0; index < 4; index++)
            {
                bts[index + offset] = btsTmp[3 - index];
            }
        }
        public static void IntToByte2(Int16 Val, byte[] bts, int offset)
        {
            byte[] btsTemp = null;
            btsTemp = BitConverter.GetBytes(Val);
            Int16 index = 0;
            while (index < 2)
            {
                bts[index + offset] = btsTemp[1 - index];
                index += 1;
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="Val"></param>
        /// <param name="bts"></param>
        /// <param name="offset"></param>
        public static void IntToByte4(int Val, byte[] bts, int offset)
        {
            byte[] btsTemp = null;
            btsTemp = BitConverter.GetBytes(Val);
            int index = 0;
            while (index < 4)
            {
                bts[index + offset] = btsTemp[3 - index];
                index += 1;
            }
        }
        /// <summary>
        /// Byte2转int32
        /// </summary>
        /// <param name="bts"></param>
        /// <param name="i"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static Int16 Byte2ToInt(byte[] bts, int offset)
        {
            byte[] btsTemp = new byte[2];
            Int32 index = 0;
            while (index < 2)
            {
                btsTemp[1 - index] = bts[index + offset];
                index += 1;
            }
            return BitConverter.ToInt16(btsTemp, 0);
        }
        public static Int16 Byte2ToInt(ArraySegmentList<byte> bts, int offset)
        {
            byte[] btsTemp = new byte[2];
            Int32 index = 0;
            while (index < 2)
            {
                btsTemp[1 - index] = bts[index + offset];
                index += 1;
            }
            return BitConverter.ToInt16(btsTemp, 0);
        }
        /// <summary>
        /// Byte2转Uint16
        /// </summary>
        /// <param name="bt1"></param>
        /// <param name="bt2"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static ushort Byte2ToUInt(byte bt1, byte bt2)
        {
            return (ushort)(bt1 << 8 | bt2);
        }
        public static int Byte3ToInt(byte[] bts, int offset)
        {
            return (bts[offset] << 16 | bts[offset + 1] << 8 | bts[offset + 2]);
        }
        public static int Byte3ToInt(ArraySegmentList<byte> bts, int offset)
        {
            return (bts[offset] << 16 | bts[offset + 1] << 8 | bts[offset + 2]);
        }

        /// <summary>
        /// Byte8转ULong
        /// </summary>
        /// <param name="bt1"></param>
        /// <param name="bt2"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static ulong Byte8ToULong(byte bt1, byte bt2, byte bt3, byte bt4, byte bt5, byte bt6, byte bt7, byte bt8)
        {
            return BitConverter.ToUInt64(new byte[] { bt8, bt7, bt6, bt5, bt4, bt3, bt2, bt1 }, 0);
            //return (ulong)(bt1 << 56 | bt2 << 48 | bt3 << 40 | bt4 << 32 | bt5 << 24 | bt6 << 16 | bt7 << 8 | bt8);
        }
        public static ulong Byte8ToULong(byte[] bts, int offset)
        {
            return Byte8ToULong(bts[offset], bts[offset + 1], bts[offset + 2], bts[offset + 3],
                bts[offset + 4], bts[offset + 5], bts[offset + 6], bts[offset + 7]);
        }
        /// <summary>
        /// ULong转Byte8
        /// </summary>
        /// <param name="i"></param>
        /// <param name="bts"></param>
        /// <param name="offset"></param>
        public static void ULongToByte8(ulong i, byte[] bts, int offset)
        {
            ULongToByte8(i, ref bts[offset], ref bts[offset + 1], ref bts[offset + 2], ref bts[offset + 3], ref bts[offset + 4], ref bts[offset + 5], ref bts[offset + 6], ref bts[offset + 7]);
        }
        /// <summary>
        /// ULong转Byte8
        /// </summary>
        /// <param name="i"></param>
        /// <param name="bt1"></param>
        /// <param name="bt2"></param>
        /// <param name="bt3"></param>
        /// <param name="bt4"></param>
        /// <param name="bt5"></param>
        /// <param name="bt6"></param>
        /// <param name="bt7"></param>
        /// <param name="bt8"></param>
        public static void ULongToByte8(ulong i, ref byte bt1, ref byte bt2, ref byte bt3, ref byte bt4, ref byte bt5, ref byte bt6, ref byte bt7, ref byte bt8)
        {
            var bts = BitConverter.GetBytes(i);
            bt1 = bts[7];
            bt2 = bts[6];
            bt3 = bts[5];
            bt4 = bts[4];
            bt5 = bts[3];
            bt6 = bts[2];
            bt7 = bts[1];
            bt8 = bts[0];


            //bt1 = Convert.ToByte((0xff00000000000000 & i) >> 56);
            //bt2 = Convert.ToByte((0xff000000000000 & i) >> 48);
            //bt3 = Convert.ToByte((0xff0000000000 & i) >> 40);
            //bt4 = Convert.ToByte((0xff00000000 & i) >> 32);
            //bt5 = Convert.ToByte((0xff000000 & i) >> 24);
            //bt6 = Convert.ToByte((0xff0000 & i) >> 16);
            //bt7 = Convert.ToByte((0xff00 & i) >> 8);
            //bt8 = Convert.ToByte(0xff & i);
        }

        /// <summary>
        /// Byte4转Uint32
        /// </summary>
        /// <param name="bt1"></param>
        /// <param name="bt2"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static uint Byte4ToUInt(byte bt1, byte bt2, byte bt3, byte bt4)
        {
            return BitConverter.ToUInt32(new byte[] { bt4, bt3, bt2, bt1 }, 0);
            //return (uint)(bt1 << 24 | bt2 << 16 | bt3 << 8 | bt4);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="bts"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static uint Byte4ToUInt(byte[] bts, int offset)
        {
            return ByteHelper.Byte4ToUInt(bts[offset], bts[offset + 1], bts[offset + 2], bts[offset + 3]);
        }
        public static int Byte4ToInt(byte[] bts, int offset)
        {
            return ByteHelper.Byte4ToInt(bts[offset], bts[offset + 1], bts[offset + 2], bts[offset + 3]);
        }
        /// <summary>
        /// Byte4转int32
        /// </summary>
        /// <param name="bt1"></param>
        /// <param name="bt2"></param>
        /// <param name="bt3"></param>
        /// <param name="bt4"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static int Byte4ToInt(byte bt1, byte bt2, byte bt3, byte bt4)
        {
            byte[] bts = new byte[] {
            bt4,
            bt3,
            bt2,
            bt1
        };
            return BitConverter.ToInt32(bts, 0);
        }
        /// <summary>
        /// Byte2转Uint16
        /// </summary>
        /// <param name="bts"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static UInt16 Byte2ToUInt(byte[] bts, int offset)
        {
            return Byte2ToUInt(bts[offset], bts[offset + 1]);
        }
        /// <summary>
        /// Float转byte4
        /// </summary>
        /// <param name="str">需转化的值</param>
        /// <param name="bts">存放数组</param>
        /// <param name="offset">存放起始位置</param>
        /// <remarks></remarks>
        public static void FloatToByte4(string str, byte[] bts, int offset)
        {
            FloatToByte4(Convert.ToSingle(str), bts, offset);
        }
        /// <summary>
        /// Float转byte4
        /// </summary>
        /// <param name="val">需转化的值</param>
        /// <param name="bts">存放数组</param>
        /// <param name="offset">存放起始位置</param>
        /// <remarks></remarks>
        public static void FloatToByte4(float val, byte[] bts, int offset)
        {
            byte[] btsTemp = null;
            btsTemp = BitConverter.GetBytes(val);
            Int32 index = 0;
            while (index < 4)
            {
                bts[index + offset] = btsTemp[3 - index];
                index += 1;
            }
        }
        /// <summary>
        /// Byte4转Float
        /// </summary>
        /// <param name="bts">存放数组</param>
        /// <param name="offset">存放起始位置</param>
        /// <remarks></remarks>
        public static float Byte4ToFloat(byte[] bts, int offset)
        {
            byte[] btsTemp = new byte[4];
            Int32 index = 0;
            while (index < 4)
            {
                btsTemp[3 - index] = bts[index + offset];
                index += 1;
            }
            return BitConverter.ToSingle(btsTemp, 0);
        }


        /// <summary>
        /// Double转byte8
        /// </summary>
        /// <param name="val">需转化的值</param>
        /// <param name="bts">存放数组</param>
        /// <param name="offset">存放起始位置</param>
        /// <remarks></remarks>
        public static void DoubleToByte8(double val, byte[] bts, int offset)
        {
            byte[] btsTemp = null;
            btsTemp = BitConverter.GetBytes(val);
            Int32 index = 0;
            while (index < 8)
            {
                bts[index + offset] = btsTemp[7 - index];
                index += 1;
            }
        }

        /// <summary>
        /// byte8转Double
        /// </summary>
        /// <param name="bts">存放数组</param>
        /// <param name="offset">存放起始位置</param>
        /// <remarks></remarks>
        public static double Byte8ToDouble(byte[] bts, int offset)
        {
            byte[] btsTemp = new byte[8];
            Int32 index = 0;
            while (index < 8)
            {
                btsTemp[7 - index] = bts[index + offset];
                index += 1;
            }
            return BitConverter.ToDouble(btsTemp, 0);
        }
        /// <summary>
        /// 字符串转GBK编码
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static byte[] StringToGBK(string str)
        {
            if (string.IsNullOrEmpty(str))
                return new byte[0];
            //if (Add0)
            //    return System.Text.Encoding.GetEncoding("GBK").GetBytes(str + "\0");
            //else
            return System.Text.Encoding.GetEncoding("GBK").GetBytes(str);
        }

#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
        /// <summary>
        /// core需注册GBK编码
        /// </summary>
        public static void RegisterGBKEncoding()
        {
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        }
#else
        public static void RegisterGBKEncoding() { }
#endif

        ///// <summary>
        ///// 填充GBK编码
        ///// </summary>
        ///// <param name="str"></param>
        ///// <param name="bts">填充的byte数组</param>
        ///// <param name="index">偏移量</param>
        ///// <param name="Add0">是否在最后补0</param>
        ///// <returns>填充长度</returns>
        //public static int FillGBK(string str, byte[] bts, int offset)
        //{
        //    var len = FillSGBK(str, bts, offset);
        //    if (Add0)
        //    {
        //        bts[offset + len] = 0;
        //        return len + 1;
        //    }
        //    return len;
        //}
        /// <summary>
        /// 填充GBK编码
        /// </summary>
        /// <param name="str"></param>
        /// <param name="bts">填充的byte数组</param>
        /// <param name="offset">偏移量</param>
        /// <param name="length">长度</param>
        /// <returns></returns>
        public static int FillSGBK(string str, byte[] bts, int offset, int length = 0)
        {
            int len = 0;
            if (str != null)
            {
                var tmp = StringToGBK(str);
                //填充数据
                for (int i = 0; i < tmp.Length && i < length; i++)
                {
                    bts[offset + i] = tmp[i];
                    len += 1;
                }
            }
            //补0
            for (int i = len; i < length; i++)
            {
                bts[offset + i] = 0;
            }
            return len > length ? len : length;
        }
        public static int FillSGBK_H0(string str, byte[] bts, int offset, int length = 0)
        {
            int len = 0;
            if (str != null)
            {
                var tmp = ByteHelper.StringToGBK(str);
                len = Math.Min(tmp.Length, length);
                Array.Copy(tmp, 0, bts, offset + length - len, len);
            }
            //补0
            for (int i = 0; i < length - len; i++)
            {
                bts[offset + i] = 0;
            }
            return len > length ? len : length;
        }
        public static int FillHex(string str, byte[] bts, int offset, int length = 0)
        {
            int len = 0;
            int index = 0;
            if (str != null)
            {
                var tmp = HexStringToBytes(str);
                if (length > tmp.Length)
                {
                    index = length - tmp.Length;
                }
                //填充数据
                for (int i = 0; i < tmp.Length; i++)
                {
                    bts[offset + i + index] = tmp[i];
                }
                len = tmp.Length;
            }
            //补0
            for (int i = 0; i < index; i++)
            {
                bts[offset + i] = 0;
            }
            return len > length ? len : length;
        }


        /// <summary>
        /// GBK编码转字符串
        /// </summary>
        /// <param name="bts">填充的byte数组</param>
        /// <param name="offset">偏移量</param>
        /// <returns></returns>
        public static string GBKToString(byte[] bts, int offset = 0)
        {
            return SGBKToString(bts, offset, bts.Length - offset);
        }
        ///// <summary>
        ///// GBK编码转字符串
        ///// </summary>
        ///// <param name="bts">填充的byte数组</param>
        ///// <param name="offset">偏移量</param>
        ///// <param name="count">填充长度</param>
        ///// <returns></returns>
        //public static string GBKToString(byte[] bts, int offset, out int count)
        //{
        //    count = 0;
        //    for (int i = offset; i < bts.Length; i++)
        //    {
        //        count++;
        //        if (bts[i] == 0)
        //            break;
        //    }
        //    return SGBKToString(bts, offset, count - 1);
        //}
        /// <summary>
        /// GBK编码转字符串(定长字符串)
        /// </summary>
        /// <param name="bts"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        public static string SGBKToString(byte[] bts, int offset, int count)
        {
            return System.Text.Encoding.GetEncoding("GBK").GetString(bts, offset, count).Replace("\0", "");
        }
        /// <summary>
        /// HEX字符串转BYTE数组
        /// </summary>
        /// <param name="hex"></param>
        /// <returns></returns>
        public static byte[] HexStringToBytes(string hex)
        {
            int len = (hex.Length / 2);
            byte[] result = new byte[len];
            for (int i = 0; i < len; i++)
            {
                int pos = i * 2;
                result[i] = Convert.ToByte(hex[pos].ToString() + hex[pos + 1].ToString(), 16);
            }
            return result;
        }
        /// <summary>
        /// BYTE数组转HEX字符串
        /// </summary>
        /// <param name="bts"></param>
        /// <returns></returns>
        public static string BytesToHexString(this byte[] bts)
        {
            return BytesToHexString(bts, 0, bts.Length);
        }
        /// <summary>
        /// BYTE数组转HEX字符串
        /// </summary>
        /// <param name="bts"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        public static string BytesToHexString(byte[] bts, int offset, int count)
        {
            StringBuilder sb = new StringBuilder(bts.Length * 2);
            return BytesToHexString(sb, bts, offset, count).ToString();
        }
#if NETSTANDARD || NET5_0_OR_GREATER || NETCOREAPP
        public static string BytesToHexString(this Memory<byte> bts)
        {
            return BytesToHexString(bts, 0, bts.Length);
        }
        public static string BytesToHexString(Memory<byte> bts, int offset, int count)
        {
            StringBuilder sb = new StringBuilder(bts.Length * 2);
            return BytesToHexString(sb, bts, offset, count).ToString();
        }
        public static StringBuilder BytesToHexString(StringBuilder sb, Memory<byte> bts, int offset, int count)
        {
            for (int i = 0; i < count; i++)
            {
                sb.Append(bts.Span[i + offset].ToString("X2"));
            }
            return sb;
        }
#endif
        public static StringBuilder BytesToHexString(StringBuilder sb, byte[] bts, int offset, int count)
        {
            for (int i = 0; i < count; i++)
            {
                sb.Append(bts[i + offset].ToString("X2"));
            }
            return sb;
        }
        public static StringBuilder BytesToHexString(StringBuilder sb, byte[] bts, int offset, int count, bool AddSpace)
        {
            for (int i = 0; i < count; i++)
            {
                sb.Append(bts[i + offset].ToString("X2"));
                if (AddSpace)
                {
                    sb.Append(' ');
                }
            }
            return sb;
        }
        /// <summary>
        /// BYTE数组转HEX字符串
        /// </summary>
        /// <param name="bts"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <param name="AddSpace"></param>
        /// <returns></returns>
        public static string BytesToHexString(byte[] bts, int offset, int count, bool AddSpace)
        {
            StringBuilder sb = new StringBuilder(bts.Length * 2);
            for (int i = 0; i < count; i++)
            {
                sb.Append(bts[i + offset].ToString("X2"));
                if (AddSpace)
                {
                    sb.Append(' ');
                }
            }
            return sb.ToString();
        }

        public static int Base64StrToInt(string strBase64)
        {
            var bts = Convert.FromBase64String(strBase64);
            return Byte4ToInt(bts[0], bts[1], bts[2], bts[3]);
        }

#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
        /// <summary>
        /// BYTE数组转图片
        /// </summary>
        /// <param name="bts"></param>
        /// <returns></returns>
        public static System.Drawing.Image BytesToImage(byte[] bts)
        {
            try
            {
                System.IO.MemoryStream stream = new MemoryStream(bts);
                System.Drawing.Image img = System.Drawing.Image.FromStream(stream);
                return img;
            }
            catch
            {

                return null;
            }
        }
#endif

        public static string ToUtf8String(this byte[] value)
        {
            if (value == null)
            {
                return null;
            }
            return Encoding.UTF8.GetString(value);
        }
        public static string ToUtf8String(this byte[] value, int index, int count)
        {
            if (value == null || count <= 0)
            {
                return null;
            }
            return Encoding.UTF8.GetString(value, index, count);
        }

        public static string ToUtf8StringUseSQL(this byte[] value)
        {
            if (value == null)
            {
                return "NULL";
            }
            else
                return "'" + Encoding.UTF8.GetString(value).Replace("'", "''") + "'";
        }
    }
}