﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;

namespace SQ.Base.Queue
{
    public delegate void dlgWorkItemExecute<T>(T tag, CancellationToken cancellationToken);
    public delegate void dlgWorkItemThreadAbort<T>(T tag, ThreadAbortException abortException);
    public delegate void dlgWorkItemTimeOutAbort<T>(T tag, OperationCanceledException timeoutException);
    public delegate void dlgWorkItemDispose();
    public class DefaultWorkItem<T> : IWorkItemCanAbort<T>
    {
        dlgWorkItemExecute<T> execute; dlgWorkItemThreadAbort<T> abort; dlgWorkItemTimeOutAbort<T> timeout; dlgWorkItemDispose dispose;
        public DefaultWorkItem(dlgWorkItemExecute<T> execute, dlgWorkItemThreadAbort<T> abort = null, dlgWorkItemTimeOutAbort<T> timeout = null, dlgWorkItemDispose dispose = null)
        {
            this.execute = execute;
            this.abort = abort;
            this.timeout = timeout;
            this.dispose = dispose;
        }


        public void Dispose()
        {
            dispose?.Invoke();
        }

        public void Execute(T tag, CancellationToken cancellationToken)
        {
            execute?.Invoke(tag, cancellationToken);
        }

        public void ThreadAbort(T tag, ThreadAbortException abortException)
        {
            abort?.Invoke(tag, abortException);
        }

        public void ThreadAbort(T tag, OperationCanceledException timeoutException)
        {
            timeout?.Invoke(tag, timeoutException);
        }
    }
}
