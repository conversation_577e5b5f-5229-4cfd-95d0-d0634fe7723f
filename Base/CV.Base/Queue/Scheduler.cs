﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace SQ.Base.Queue
{

    /// <summary>
    /// 先进先出调度器
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class Scheduler<T> :
#if NETFRAMEWORK
        //45环境BlockingCollection有性能问题
        SchedulerBase<T>
#else
        SchedulerBlockingCollection<T>
#endif
    {
        public Scheduler()
            : this(1)
        {
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SchedulerCount">调度器执行线程数</param>
        public Scheduler(int SchedulerCount)
            : this(SchedulerCount, 0)
        {
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SchedulerCount">调度器内部执行线程数</param>
        /// <param name="ExecuteTimeoutMSec">Execute执行超时时间（毫秒） 小于1不超时</param>
        public Scheduler(int SchedulerCount, int ExecuteTimeoutMSec)
            : base(SchedulerCount, ExecuteTimeoutMSec)
        {
        }
        ConcurrentQueue<IWorkItem<T>> mQueues_Source = new ConcurrentQueue<IWorkItem<T>>();

        public override void Clear()
        {
            while (mQueues_Source.TryDequeue(out var item))
            {

            }
        }
#if NETFRAMEWORK
        protected override IWorkItem<T> GetItem()
        {
            if (mQueues_Source.TryDequeue(out var item))
            {
                return item;
            }
            else
            {
                return null;
            }
        }
        public override void Add(IWorkItem<T> item)
        {
            mQueues_Source.Enqueue(item);
        }
        protected override int GetCount()
        {
            return mQueues_Source.Count;
        }
#else
        protected override BlockingCollection<IWorkItem<T>> CreateBlockingCollection()
        {
            return new BlockingCollection<IWorkItem<T>>(mQueues_Source);

            //if (mQueues_Source is ConcurrentQueue<IWorkItem<T>>)
            //{
            //    var queue = (ConcurrentQueue<IWorkItem<T>>)mQueues_Source;
            //    while (queue.TryDequeue(out var item))
            //    {

            //    }
            //}
            //else if (mQueues_Source is ConcurrentStack<IWorkItem<T>>)
            //{
            //    var queue = (ConcurrentStack<IWorkItem<T>>)mQueues_Source;
            //    queue.Clear();
            //}
            //throw new NotImplementedException();
        }
#endif
    }
    /// <summary>
    /// 后进先出调度器
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class DeScheduler<T> : SchedulerBlockingCollection<T>
    {
        public DeScheduler()
            : this(1)
        {
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SchedulerCount">调度器执行线程数</param>
        public DeScheduler(int SchedulerCount)
            : this(SchedulerCount, 0)
        {
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SchedulerCount">调度器内部执行线程数</param>
        /// <param name="ExecuteTimeoutMSec">Execute执行超时时间（毫秒） 小于1不超时</param>
        public DeScheduler(int SchedulerCount, int ExecuteTimeoutMSec)
            : base(SchedulerCount, ExecuteTimeoutMSec)
        {
        }
        ConcurrentStack<IWorkItem<T>> mQueues_Source = new ConcurrentStack<IWorkItem<T>>();
        protected override BlockingCollection<IWorkItem<T>> CreateBlockingCollection()
        {
            return new BlockingCollection<IWorkItem<T>>(mQueues_Source);
            //{
            //    if (isFifo)
            //    {
            //        mQueues_Source = new ConcurrentQueue<IWorkItem<T>>();
            //        mQueues = new BlockingCollection<IWorkItem<T>>(mQueues_Source);
            //    }
            //    else
            //    {
            //    }
            //}
            //if (mQueues_Source is ConcurrentQueue<IWorkItem<T>>)
            //{
            //    var queue = (ConcurrentQueue<IWorkItem<T>>)mQueues_Source;
            //    while (queue.TryDequeue(out var item))
            //    {

            //    }
            //}
            //else if (mQueues_Source is ConcurrentStack<IWorkItem<T>>)
            //{
            //    var queue = (ConcurrentStack<IWorkItem<T>>)mQueues_Source;
            //    queue.Clear();
            //}
            //throw new NotImplementedException();
        }

        public override void Clear()
        {
            mQueues_Source.Clear();
        }
    }

    /// <summary>
    /// 调度器基类基于BlockingCollection
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public abstract class SchedulerBlockingCollection<T> : SchedulerBase<T>
    {

        /// <summary>
        /// 
        /// </summary>
        /// <param name="SchedulerCount">调度器内部执行线程数</param>
        /// <param name="ExecuteTimeoutMSec">Execute执行超时时间（毫秒） 小于1不超时</param>
        public SchedulerBlockingCollection(int SchedulerCount, int ExecuteTimeoutMSec)
            : base(SchedulerCount, ExecuteTimeoutMSec)
        {
            mQueues = CreateBlockingCollection();
        }
        protected abstract BlockingCollection<IWorkItem<T>> CreateBlockingCollection();

        BlockingCollection<IWorkItem<T>> mQueues;
        public override void Add(IWorkItem<T> item)
        {
            mQueues.Add(item);
        }

        protected override IWorkItem<T> GetItem()
        {
            return mQueues.Take(cancelStop.Token);
            //if (mQueues.TryTake(out var item, -1, cancelStop.Token))
            //{
            //    return item;
            //}
            //return null;
        }

        protected override int GetCount()
        {
            return mQueues.Count;
        }

        public override void Stop()
        {
            mQueues.CompleteAdding();
            base.Stop();
        }
    }

    /// <summary>
    /// 调度器检查接口
    /// </summary>
    internal interface ICheckScheduler
    {
        void Check();
    }
    /// <summary>
    /// 检查调度器
    /// </summary>
    internal class CheckScheduler
    {
        internal static CheckScheduler Singleton;
        static CheckScheduler()
        {
            Singleton = new CheckScheduler();
            Singleton.thCheck.SleepMs = 1000;
        }

        List<ICheckScheduler> lst = new List<ICheckScheduler>();
        ThreadWhile<object> thCheck = new ThreadWhile<object>();
        /// <summary>
        /// 检查线程
        /// </summary>
        //System.Threading.Thread thCheck;
        public void Add(ICheckScheduler chk)
        {
            lst.Add(chk);
            Start();
        }
        public void Remove(ICheckScheduler chk)
        {
            lst.Remove(chk);
            if (lst.Count == 0)
            {
                Stop();
            }
        }
        void Start()
        {
            thCheck.StartIfNotRun(OnRun, null, "CheckScheduler");
        }
        /// <summary>
        /// 检查Execute超时
        /// </summary>
        /// <param name="cancellationToken"></param>
        private void OnRun(object Tag, CancellationToken cancellationToken)
        {
            if (lst.Count == 0)
            {
                thCheck.Stop();
                return;
            }
            cancellationToken.ThrowIfCancellationRequested();
            foreach (var item in lst)
            {
                cancellationToken.ThrowIfCancellationRequested();
                item.Check();
            }
        }

        void Stop()
        {
            thCheck.Stop();
        }
    }

    /// <summary>
    /// 调度器基类 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public abstract class SchedulerBase<T> : ICheckScheduler, IDisposable
    {
        /// <summary>
        /// 中止执行信号量
        /// </summary>
        protected CancellationTokenSource cancelStop = new CancellationTokenSource();
        public string SchedulerName;
        public T[] Tags;
        /// <summary>
        /// 工作线程数
        /// </summary>
        public int WorkThreadCount
        {
            get
            {
                return _WorkThreadCount;
            }
        }
        private int _WorkThreadCount;
        /// <summary>
        ///  Execute执行超时时间
        /// </summary>
        public int ExecuteTimeoutMSec;
        /// <summary>
        /// 调度器工作线程数
        /// </summary>
        int mySchedulerCount;
        /// <summary>
        /// 执行Execute开始时间
        /// </summary>
        protected DateTime[] ExecuteStartTimes;
        /// <summary>
        /// 指示当前是否在执行Execute
        /// </summary>
        protected bool[] Executed;
        ///// <summary>
        ///// 指示当前执行的key是什么(如果有key)
        ///// </summary>
        //protected string[] ExecutedKey;
        /// <summary>
        /// 是否已经加入检查线程
        /// </summary>
        bool IsAddToCheck = false;

        /// <summary>
        /// 执行线程
        /// </summary>
        System.Threading.Thread[] ths;

        CancellationTokenSource[] cancellStopThread;

        ///// <summary>
        ///// 工作线程数锁
        ///// </summary>
        //object WorkThreadCountLock = new object();

        ///// <summary>
        ///// 暂停操作信号量
        ///// </summary>
        //ManualResetEvent PauseSignal = new ManualResetEvent(true);
        ///// <summary>
        ///// 执行信号量
        ///// </summary>
        //AutoResetEvent OnRunPauseSignal = new AutoResetEvent(false);
        public SchedulerBase()
            : this(1)
        {
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SchedulerCount">调度器执行线程数</param>
        public SchedulerBase(int SchedulerCount)
            : this(SchedulerCount, 0)
        {
            //mySchedulerCount = SchedulerCount;
            ////ths = new Thread[SchedulerCount];
            ////for (int i = 0; i < SchedulerCount; i++)
            ////{
            ////    ths[i] = new System.Threading.Thread(Run);
            ////}
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SchedulerCount">调度器内部执行线程数</param>
        /// <param name="ExecuteTimeoutMSec">Execute执行超时时间（毫秒） 小于1不超时</param>
        public SchedulerBase(int SchedulerCount, int ExecuteTimeoutMSec)
        {
            mySchedulerCount = SchedulerCount;
            this.ExecuteTimeoutMSec = ExecuteTimeoutMSec;
            SchedulerName = "Sch" + DateTime.Now.Ticks;
        }
        public SchedulerBase(string SchedulerName, int SchedulerCount, int ExecuteTimeoutMSec)
        {
            mySchedulerCount = SchedulerCount;
            this.ExecuteTimeoutMSec = ExecuteTimeoutMSec;
            if (SchedulerName == null)
            {
                this.SchedulerName = "Sch" + DateTime.Now.Ticks;
            }
            else
            {
                this.SchedulerName = SchedulerName;
            }
        }

        /// <summary>
        /// 执行状态
        /// </summary>
        public bool IsRun { get; private set; }
        /// <summary>
        /// 指示调度器是否可继续执行
        /// </summary>
        private bool CanRun { get; set; }
        /// <summary>
        /// 队列任务数
        /// </summary>
        public int Count
        {
            get
            {
                return GetCount();
            }
        }
        /// <summary>
        /// 开始
        /// </summary>
        public virtual void Start()
        {
            IsRun = true;
            CanRun = true;
            _WorkThreadCount = 0;
            ths = new Thread[mySchedulerCount];
            cancellStopThread = new CancellationTokenSource[mySchedulerCount];
            ExecuteStartTimes = new DateTime[mySchedulerCount];
            Executed = new bool[mySchedulerCount];
            //ExecutedKey = new string[mySchedulerCount];
            if (Tags == null)
                Tags = new T[mySchedulerCount];
            for (int i = 0; i < mySchedulerCount; i++)
            {
                ths[i] = new System.Threading.Thread(Run);
                ExecuteStartTimes[i] = DateTime.Now;
                ths[i].Name = SchedulerName + "_" + i;
                cancellStopThread[i] = new CancellationTokenSource();
            }
            for (int i = 0; i < ths.Length; i++)
            {
                ths[i].IsBackground = true;
                ths[i].Start(i);
            };
            if (ExecuteTimeoutMSec > 0)
            {
                CheckScheduler.Singleton.Add(this);
                IsAddToCheck = true;
            }
            else
            {
                IsAddToCheck = false;
            }

        }
        /// <summary>
        /// 安全停止(等待队列处理完毕后再停止)
        /// </summary>
        public void StopSafe()
        {
            while (HasWork())
            {
                Thread.Sleep(10);
            }

            this.Stop();
        }

        /// <summary>
        /// 调度器工作情况获取
        /// </summary>
        /// <returns></returns>
        public bool HasWork()
        {
            return this.Count > 0 || this.WorkThreadCount > 0;
        }
        /// <summary>
        /// 停止
        /// </summary>
        public virtual void Stop()
        {
            try
            {
                CanRun = false;
                cancelStop.Cancel();
                foreach (var item in cancellStopThread)
                {
                    item.Cancel();
                }

                if (IsAddToCheck)
                {
                    CheckScheduler.Singleton.Remove(this);
                    IsAddToCheck = false;
                }
            }
            finally
            {
                IsRun = false;
            }
        }
        ///// <summary>
        ///// 暂停
        ///// </summary>
        //public void Pause()
        //{
        //    //PauseSignal.Reset();
        //}
        ///// <summary>
        ///// 恢复
        ///// </summary>
        //public void Resume()
        //{
        //    PauseSignal.Set();
        //}
        /// <summary>
        /// 添加任务到调度器
        /// </summary>
        /// <param name="item"></param>
        public abstract void Add(IWorkItem<T> item);
        /// <summary>
        /// 清除调度器任务队列
        /// </summary>
        public abstract void Clear();
        /// <summary>
        /// 获取第一个任务
        /// </summary>
        /// <returns></returns>
        protected abstract IWorkItem<T> GetItem();
        //protected virtual void WorkThreadChange(int i)
        //{
        //    lock (WorkThreadCountLock)
        //    {
        //        WorkThreadCount += i;
        //    }
        //}
        protected abstract int GetCount();

        /// <summary>
        /// 执行逻辑方法
        /// </summary>
        protected virtual void OnRun(int index)
        {
            bool flag = false;
            //WorkThreadChange(1);
            cancellStopThread[index].Token.ThrowIfCancellationRequested();
            IWorkItem<T> item = GetItem();
            cancellStopThread[index].Token.ThrowIfCancellationRequested();
            if (item != null)
            {

                try
                {
                    System.Threading.Interlocked.Increment(ref _WorkThreadCount);
                    using (item)
                    {
                        ExecuteStartTimes[index] = DateTime.Now;
                        Executed[index] = true;
                        //if (item is IKeyWorkItem<T>)
                        //{
                        //    ExecutedKey[index] = ((IKeyWorkItem<T>)item).GetKey();
                        //}
                        try
                        {
                            item.Execute(Tags[index], cancellStopThread[index].Token);
                        }
                        catch (ThreadAbortException abortException)
                        {
                            ErrorLog.WriteLog4Ex("Scheduler.itemExecuteThreadAbortException", abortException);
                            if (item is IWorkItemCanAbort<T>)
                            {
                                ((IWorkItemCanAbort<T>)item).ThreadAbort(Tags[index], abortException);
                            }
                        }
                        catch (System.OperationCanceledException timeoutException)
                        {
                            ErrorLog.WriteLog4Ex("Scheduler.itemExecuteThreadTimeOutException", timeoutException);
                            if (item is IWorkItemCanAbort<T>)
                            {
                                ((IWorkItemCanAbort<T>)item).ThreadAbort(Tags[index], timeoutException);
                            }
                        }
                    }
                }
                finally
                {
                    //ExecutedKey[index] = null;
                    Executed[index] = false;
                    System.Threading.Interlocked.Decrement(ref _WorkThreadCount);
                    //WorkThreadChange(-1);
                }
            }
            else
            {
                flag = true;
            }
            if (flag)
            {
#if NETFRAMEWORK
                Thread.Sleep(10);
#else
                Thread.Sleep(100);
#endif
            }
            //OnRunPauseSignal.WaitOne();
            //PauseSignal.WaitOne();
        }
        /// <summary>
        /// 线程执行方法
        /// </summary>
        protected virtual void Run(object i)
        {
            int index = (int)i;
            while (CanRun && !cancellStopThread[index].IsCancellationRequested)
            {
                try
                {
                    OnRun(index);
                }
                catch (ThreadAbortException)
                {
                    //Debug.WriteLine((string)abortException.ExceptionState);
                }
                catch (System.OperationCanceledException)
                {

                }
                catch (System.Exception ex)
                {
                    ErrorLog.WriteLog4Ex("Scheduler.Run", ex);
                }
            }
        }
        /// <summary>
        /// 检查执行超时方法
        /// </summary>
        public void Check()
        {
            if (CanRun)
            {
                for (int i = 0; i < mySchedulerCount; i++)
                {
                    if (Executed[i] && DateTimeHelper.DiffNowMSec(ExecuteStartTimes[i]) >= ExecuteTimeoutMSec)
                    {
#if NETSTANDARD || NET5_0_OR_GREATER  || NETCOREAPP
                        cancellStopThread[i].Cancel();
                        while (ths[i].IsAlive)
                        {
                            //等待线程退出
                            Thread.Sleep(100);
                        }
                        cancellStopThread[i] = new CancellationTokenSource();
#else
                        //.NET45环境 结束线程比较直接
                        ths[i].Abort(i);
                        ths[i].Join();
#endif
                        ths[i] = new System.Threading.Thread(Run);
                        ths[i].IsBackground = true;
                        if (SchedulerName != null)
                        {
                            ths[i].Name = SchedulerName + "_" + i + "R" + DateTime.Now.Ticks;
                        }
                        else
                        {
                            ths[i].Name = "Sch_" + i + "R" + DateTime.Now.Ticks;
                        }
                        ExecuteStartTimes[i] = DateTime.Now;
                        ths[i].Start(i);


                    }
                }
            }
        }
        public void Dispose()
        {
            try
            {
                lock (this)
                {
                    if (IsRun)
                        Stop();
                    //if (!mDispose)
                    //{
                    //    mDispose = true;
                    //}
                }

            }
            catch (System.Exception ex)
            {
                ErrorLog.WriteLog4Ex("Scheduler.Dispose", ex);
            }
        }
    }

}