﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace SQ.Base.Queue
{
    /// <summary>
    /// 队列任务项接口
    /// </summary>
    public interface IWorkItem<T> : IDisposable
    {

        /// <summary>
        /// 执行任务
        /// </summary> 
        void Execute(T t, CancellationToken cancellationToken);
    }
    public interface IWorkItemCanAbort<T> : IWorkItem<T>
    {
        /// <summary>
        /// 发生线程中止时触发（1.Work执行时间超时；2.调度器中止）
        /// </summary>
        /// <param name="tag"></param>
        void ThreadAbort(T tag, System.Threading.ThreadAbortException abortException);
        /// <summary>
        /// 发生线程中止时触发（1.Work执行时间超时；2.调度器中止）
        /// </summary>
        /// <param name="tag"></param>
        /// <param name="timeoutException"></param>
        void ThreadAbort(T tag, System.OperationCanceledException timeoutException);
    }

    public interface IKeyWorkItem<T> : IWorkItem<T>
    {
        string GetKey();
    }
    public interface IKeyWorkItemCanAbort<T> : IKeyWorkItem<T>, IWorkItemCanAbort<T>
    {
    }
}