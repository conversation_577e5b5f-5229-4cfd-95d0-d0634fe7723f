﻿using System;
using System.Text;

namespace SQ.Base
{
    public static class DateTimeHelper
    {
        readonly static DateTime dtUnix = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        readonly static System.DateTime startTime = TimeZoneInfo.ConvertTimeFromUtc(dtUnix, TimeZoneInfo.Local);
        /// <summary>
        /// UTC时间戳(单位秒)转DateTime
        /// </summary>
        /// <param name="seconds"></param>
        /// <returns></returns>
        public static DateTime UNIXtoDateTime(this long seconds)
        {
            double secs = Convert.ToDouble(seconds);
            return UNIXtoDateTime(secs);
        }
        /// <summary>
        /// UTC时间戳(单位秒)转DateTime
        /// </summary>
        /// <param name="seconds"></param>
        /// <returns></returns>
        public static DateTime UNIXtoDateTime(this ulong seconds)
        {
            double secs = Convert.ToDouble(seconds);
            return UNIXtoDateTime(secs);
        }
        /// <summary>
        /// UTC时间戳(单位秒)转DateTime
        /// </summary>
        /// <param name="seconds"></param>
        /// <returns></returns>
        public static DateTime UNIXtoDateTime(this double seconds)
        {
            return startTime.AddSeconds(seconds);
        }
        /// <summary>
        /// 将c# DateTime时间格式转换为Unix时间戳格式(单位秒)
        /// </summary>
        /// <param name="time">时间</param>
        /// <returns>double</returns>
        public static double DateTimeToUNIX_Double(this System.DateTime time)
        {
            double intResult = 0;
            intResult = (time - startTime).TotalSeconds;
            return intResult;
        }
        /// <summary>
        /// DateTime时间格式转换为Unix时间戳格式(单位秒)
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public static ulong DateTimeToUNIX_Ulong(this System.DateTime time)
        {
            ulong result = 0;
            result = Convert.ToUInt64(DateTimeToUNIX_Double(time));
            return result;
        }
        /// <summary>
        /// DateTime时间格式转换为Unix时间戳格式(单位秒)
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public static long DateTimeToUNIX_long(this System.DateTime time)
        {
            return Convert.ToInt64(DateTimeToUNIX_Double(time));
        }
        public static long DateTimeToUNIX_ns(this System.DateTime time)
        {
            return Convert.ToInt64(DateTimeToUNIX_Double(time) * 1000000000);
        }
        /// <summary>
        /// DateTime时间格式转换为Unix时间戳格式(单位毫秒)
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public static long ToTimeStamp(this DateTime time)
        {
            var val = DateTimeToUNIX_Double(time);
            return (long)(val * 1000);
        }

        public static double DiffNowSec(this DateTime dt)
        {
            return (DateTime.Now - dt).TotalSeconds;
        }
        public static double DiffNowMSec(this DateTime dt)
        {
            return (DateTime.Now - dt).TotalMilliseconds;
        }
        /// <summary>
        /// DateTime转UTC时间戳(单位毫秒)
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static long ConvertToLong(this DateTime value)
        {
            return (long)(DateTimeToUNIX_Double(value) * 1000);
        }


        /// <summary>
        /// UTC时间戳(单位毫秒)转DateTime
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static DateTime ConverToDateTime(this long value)
        {
            if (value <= 0)
            {
                return DateTime.MinValue;
            }
            return UNIXtoDateTime(value / 1000D);
        }
        /// <summary>
        /// UTC时间戳(单位毫秒)转DateTime字符串
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string ConverToTimeString(this long value)
        {

            if (value <= 0)
            {
                return "NULL";
            }
            return "'" + UNIXtoDateTime(value / 1000D).ToString("yyyy-MM-dd HH:mm:ss") + "'";
        }

        public static int ConvertDateTimeToInt(this DateTime time)
        {
            return (int)DateTimeToUNIX_Double(time);
        }
        public static string ToTStr(this DateTime time)
        {
            return time.ToString("yyyy-MM-ddTHH:mm:ss");
        }
    }
}