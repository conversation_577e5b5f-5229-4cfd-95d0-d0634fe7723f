﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace SQ.Base.Cluster
{
    public class ServerConf
    {
        public ServerConf()
        {
            NowVer = "test";
        }
        /// <summary>
        /// 服务名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 服务类型ID 全局唯一
        /// </summary>
        public int Type { get; set; }
        /// <summary>
        /// 当前版本
        /// </summary>
        [Newtonsoft.Json.JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore, NullValueHandling = NullValueHandling.Ignore)]
        [DefaultValue("test")]
        public string NowVer { get; set; }
        /// <summary>
        /// 服务说明
        /// </summary>
        [Newtonsoft.Json.JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore, NullValueHandling = NullValueHandling.Ignore)]
        public string ReMark { get; set; }
        /// <summary>
        /// 心跳超时时间
        /// </summary>
        public double TimeoutMsec { get; set; }
        /// <summary>
        /// 支持代理(Proxy)模式
        /// </summary>
        [Newtonsoft.Json.JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool CanProxy { get; set; }
        /// <summary>
        /// 支持跳转代理(Agent)模式
        /// </summary>
        [Newtonsoft.Json.JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool CanAgent { get; set; }
        /// <summary>
        /// 支持仅跳转端口代理(AgentPort)模式
        /// </summary>
        [Newtonsoft.Json.JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool CanAgentPort { get; set; }
        /// <summary>
        /// 服务列表
        /// </summary>
        [JsonIgnoreAttribute]
        public ServerListByVer ServerList = new ServerListByVer();
        /// <summary>
        /// 升级
        /// </summary>
        /// <param name="NewVer"></param>
        /// <returns></returns>
        public bool Upgrade(string NewVer)
        {
            if (NewVer != null && NowVer != NewVer)
            {
                NowVer = NewVer;
                return true;
            }
            return false;
        }
    }
}
