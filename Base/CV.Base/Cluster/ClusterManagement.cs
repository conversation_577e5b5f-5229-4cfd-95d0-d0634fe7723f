﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace SQ.Base.Cluster
{
    public class ClusterManagement
    {
        /// <summary>
        /// 单例
        /// </summary>
        public static ClusterManagement Instance = new ClusterManagement();
        static ClusterManagement()
        {
            Instance.ReadConf();
            Instance.ReadVer();
        }

        /// <summary>
        /// 服务配置信息
        /// </summary>
        ConcurrentDictionary<int, ServerConf> ditServerConf = new ConcurrentDictionary<int, ServerConf>();
        /// <summary>
        /// 特征码列表 记录的Value是ServerInfo的Key值
        /// </summary>
        ConcurrentDictionary<string, string> ditTagServerKey = new ConcurrentDictionary<string, string>();
        /// <summary>
        /// 特征码MKey列表
        /// </summary>
        ConcurrentDictionary<string, string> ditTagMKey = new ConcurrentDictionary<string, string>();
        /// <summary>
        /// 更新服务
        /// </summary>
        /// <param name="lst"></param>
        /// <returns>1 未更新 2 部分已更新</returns>
        public int UpdateServers(string MKey, List<ServerInfo> lst)
        {
            int ret = 1;
            foreach (var item in lst)
            {
                item.MKey = MKey;
                if (ditServerConf.ContainsKey(item.Type))
                {
                    var serverConf = ditServerConf[item.Type];
                    serverConf.ServerList.Update(item);
                    if (serverConf.NowVer != item.Ver)
                    {
                        ret = 2;
                    }
                }
            }
            return ret;
        }
        /// <summary>
        /// 设置配置
        /// </summary>
        /// <param name="conf"></param>
        /// <returns></returns>
        public void SetConf(ServerConf conf)
        {
            if (ditServerConf.TryGetValue(conf.Type, out var item))
            {
                conf.ServerList = item.ServerList;
            }
            ditServerConf[conf.Type] = conf;
            WriteConf();
            WriteVer();
        }
        /// <summary>
        /// 获取所有配置
        /// </summary>
        /// <returns></returns>
        public List<ServerConf> GetConfs()
        {
            return ditServerConf.Values.ToList();
        }
        /// <summary>
        /// 获取配置
        /// </summary>
        /// <param name="Type"></param>
        /// <returns></returns>
        public ServerConf GetConf(int Type)
        {
            if (ditServerConf.ContainsKey(Type))
            {
                return ditServerConf[Type];
            }
            return null;
        }

        /// <summary>
        /// 升级版本 
        /// </summary>
        /// <param name="Type"></param>
        /// <param name="NewVer"></param>
        public bool Upgrade(int Type, string NewVer)
        {
            if (ditServerConf.ContainsKey(Type))
            {
                if (ditServerConf[Type].Upgrade(NewVer))
                {
                    WriteConf();
                    WriteVer();
                    return true;
                }
            }
            return false;
        }
        /// <summary>
        /// 获取最优服务
        /// </summary>
        /// <param name="type"></param>
        /// <param name="Tag">特征码</param>
        /// <returns></returns>
        public ServerInfo GetBestServer(int type, string Tag)
        {
            if (ditServerConf.ContainsKey(type))
            {
                var conf = ditServerConf[type];
                var code = type + "_" + Tag;
                ServerInfo item;
                //特征码不为空 且 特征码列表中有记录
                if (!string.IsNullOrWhiteSpace(Tag) && ditTagServerKey.ContainsKey(code))
                {
                    //通过key值获取ServerInfo
                    item = conf.ServerList.Get(conf.NowVer, ditTagServerKey[code]);
                    if (item == null || item.IsTimeout(conf.TimeoutMsec))
                    {
                        ditTagServerKey.TryRemove(code,out var _);
                        ditTagMKey.TryRemove(Tag, out var _);
                    }
                    else
                    {
                        if (ditTagMKey.TryGetValue(Tag, out var MKey2))
                        {
                            if (item.MKey != MKey2)
                            {
                                ditTagServerKey.TryRemove(code, out var _);
                            }
                            else
                            {
                                return item;
                            }
                        }
                        else
                        {
                            return item;
                        }
                    }
                }

                if (!string.IsNullOrWhiteSpace(Tag))
                {
                    if (ditTagMKey.TryGetValue(Tag, out var MKey))
                    {
                        item = conf.ServerList.GetNext(conf, MKey);
                        if (item != null)
                        {
                            ditTagServerKey[code] = item.GetKey();
                            return item;
                        }
                        else
                        {
                            ditTagMKey.TryRemove(Tag, out var _);
                        }
                    }
                }

                item = conf.ServerList.GetNext(conf, null);
                if (item != null && !string.IsNullOrWhiteSpace(Tag))
                {
                    ditTagServerKey[code] = item.GetKey();
                    ditTagMKey[Tag] = item.MKey;
                }
                return item;
            }
            return null;
        }

        ThreadWhile<object> threadWhile = new ThreadWhile<object>();
        public void StartCheck()
        {
            threadWhile.SleepMs = 60000;
            threadWhile.Start(CheckTimeout, null, "ClusterManagementCheck");
        }
        public void StopCheck()
        {
            threadWhile.Stop();
        }
        private void CheckTimeout(object tag, CancellationToken cancellationToken)
        {
            var keys = ditServerConf.Keys.ToArray();
            foreach (var key in keys)
            {
                cancellationToken.ThrowIfCancellationRequested();
                if (ditServerConf.TryGetValue(key, out var Conf))
                {
                    Conf.ServerList.CheckTimeout(Conf.TimeoutMsec);
                }
            }
        }


        static string path = SQ.Base.FileHelp.GetMyConfPath() + "ClusterConf.json";
        public bool ReadConf()
        {
            try
            {
                var lst = SQ.Base.SerializableHelper.DeserializeSetting<List<ServerConf>>(path, SQ.Base.SerializableHelper.SerializableType.Json);
                ConcurrentDictionary<int, ServerConf> tmpConf = new ConcurrentDictionary<int, ServerConf>();
                foreach (var item in lst)
                {
                    tmpConf[item.Type] = item;
                }
                ditServerConf = tmpConf;
                return true;
            }
            catch (System.Exception ex)
            {
                SQ.Base.ErrorLog.WriteLog4Ex("ReadConf", ex);
                return false;
            }
        }
        public bool WriteConf()
        {
            try
            {
                var lst = ditServerConf.Values.ToList();
                SQ.Base.SerializableHelper.SerializeSetting(lst, path, false, SQ.Base.SerializableHelper.SerializableType.Json);
                return true;
            }
            catch (System.Exception ex)
            {
                SQ.Base.ErrorLog.WriteLog4Ex("WriteConf", ex);
                return false;
            }
        }

        static string pathVer = SQ.Base.FileHelp.GetMyConfPath() + "ClusterConfVer.json";
        public bool ReadVer()
        {
            try
            {
                if (System.IO.File.Exists(pathVer))
                {
                    var lst = SQ.Base.SerializableHelper.DeserializeSetting<List<ServerVer>>(pathVer, SQ.Base.SerializableHelper.SerializableType.Json);

                    foreach (var item in lst)
                    {
                        if (ditServerConf.TryGetValue(item.Type, out var Conf))
                        {
                            Conf.NowVer = item.NowVer;
                        }
                    }
                    return true;
                }
            }
            catch (System.Exception ex)
            {
                SQ.Base.ErrorLog.WriteLog4Ex("ReadVer", ex);
            }
            return false;
        }
        public bool WriteVer()
        {
            try
            {
                List<ServerVer> lst = new List<ServerVer>(ditServerConf.Count);
                var lstdata = ditServerConf.Values.ToList();
                foreach (var item in lstdata)
                {
                    lst.Add(new ServerVer
                    {
                        NowVer = item.NowVer,
                        Type = item.Type
                    });
                }
                SQ.Base.SerializableHelper.SerializeSetting(lst, pathVer, false, SQ.Base.SerializableHelper.SerializableType.Json);
                return true;
            }
            catch (System.Exception ex)
            {
                SQ.Base.ErrorLog.WriteLog4Ex("WriteVer", ex);
                return false;
            }
        }
    }
}
