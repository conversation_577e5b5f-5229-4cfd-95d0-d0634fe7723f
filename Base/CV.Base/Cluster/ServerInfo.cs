﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using SQ.Base;

namespace SQ.Base.Cluster
{
    /// <summary>
    /// 上报的服务信息
    /// </summary>
    public class ServerInfo
    {
        /// <summary>
        /// 连接信息
        /// </summary>
        public string ConnInfo { get; set; }
        /// <summary>
        /// 在线数
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int OnlineCount { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public int Type { get; set; }
        /// <summary>
        /// 版本号
        /// </summary>
        public string Ver { get; set; }
        /// <summary>
        /// 附加信息
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string Extra { get; set; }
        /// <summary>
        /// 上次心跳时间
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public DateTime LastHeartbeat { get; set; }

        /// <summary>
        /// 首次收到心跳时间(启动时间)
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public DateTime StartTime { get; set; }
        /// <summary>
        /// 同一个服务MKey相同
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string MKey { get; set; }
        /// <summary>
        /// 超时判断
        /// </summary>
        /// <param name="timeoutMsec">超时时间 毫秒</param>
        /// <returns></returns>
        public bool IsTimeout(double timeoutMsec)
        {
            return LastHeartbeat.DiffNowMSec() > timeoutMsec;
        }
        /// <summary>
        /// 获取当前服务的不重复值 当前为连接信息
        /// </summary>
        /// <returns></returns>
        public string GetKey()
        {
            return ConnInfo;
        }

    }
     
}
