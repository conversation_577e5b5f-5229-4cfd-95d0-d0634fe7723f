﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SQ.Base;

namespace SQ.Base.Cluster
{
    public delegate bool DeleteCheck();

    public class LinkNode<T>
    {
        public LinkNode(T Key)
        {
            this.Key = Key;
            IsDelete = false;
        }

        /// <summary>
        /// 下一个
        /// </summary>
        public LinkNode<T> Next { get; set; }
        /// <summary>
        /// 上一个
        /// </summary>
        public LinkNode<T> Previous { get; set; }
        /// <summary>
        /// 值
        /// </summary>
        public T Key { get; set; }
        /// <summary>
        /// 已被移除
        /// </summary>
        public bool IsDelete { get; set; }
    }
    public class LinkedByDit<K, V>
    {
        public delegate V dglSetValue(V old, V val);
        ConcurrentDictionary<K, V> ditdata = new ConcurrentDictionary<K, V>();
        /// <summary>
        /// 第一个
        /// </summary>
        public LinkNode<K> First { get; protected set; }
        /// <summary>
        /// 最后一个
        /// </summary>
        public LinkNode<K> Last { get; protected set; }
        protected LinkNode<K> _NowNode;

        object lck = new object();
        public void Update(K key, V info, dglSetValue onSetValue)
        {
            if (!ditdata.ContainsKey(key))
            {
                lock (lck)
                {
                    if (!ditdata.ContainsKey(key))
                    {
                        #region ADD2END
                        if (First == null)
                        {
                            //开始
                            First = new LinkNode<K>(key);
                            Last = First;
                        }
                        else
                        {
                            var tmp = Last;
                            Last = new LinkNode<K>(key);
                            Last.Previous = tmp;
                            tmp.Next = Last;
                        }
                        //环状
                        First.Previous = Last;
                        Last.Next = First;
                        #endregion
                        if (onSetValue != null)
                        {
                            info = onSetValue(default, info);
                        }
                        ditdata[key] = info;
                    }
                }
            }
            else
            {
                if (onSetValue != null)
                {
                    info = onSetValue(ditdata[key], info);
                }
                ditdata[key] = info;
            }
        }

        public V GetValue(K key)
        {
            ditdata.TryGetValue(key, out var value);
            return value;
        }
        /// <summary>
        /// 移除数据
        /// </summary>
        /// <param name="node"></param>
        public void Delete(LinkNode<K> node, DeleteCheck deleteCheck = null)
        {
            if (ditdata.ContainsKey(node.Key))
            {
                lock (lck)
                {
                    if (ditdata.TryRemove(node.Key, out var value))
                    {
                        //不需要删除检查 或 删除检查成功才进行删除
                        if (deleteCheck == null || deleteCheck())
                        {
                            if (ditdata.Count == 1)
                            {
                                First = Last = null;
                                node.Next = node.Previous = null;
                                node.IsDelete = true;
                            }
                            else if (ditdata.Count == 2)
                            {
                                First = Last = node.Next;
                                First.Next = Last;
                                First.Previous = Last;
                                node.Next = node.Previous = First;
                                node.IsDelete = true;
                            }
                            else
                            {
                                if (node == First)
                                {
                                    //当前为第一个时 
                                    //改变first为下一个
                                    First = node.Next;
                                    ////改变新的first的Previous 指向为last
                                    //First.Previous = Last;
                                    ////改变last的next指向为新的first
                                    //Last.Next = First;
                                }
                                else if (node == Last)
                                {
                                    //当前为最后一个时 
                                    //改变Last为上一个
                                    Last = node.Previous;
                                    ////改变first的Previous 指向为新的last
                                    //First.Previous = Last;
                                    ////改变新的Last的next 指向为first
                                    //Last.Next = First;
                                }
                                node.Previous.Next = node.Next;
                                node.Next.Previous = node.Previous;
                                if (node.Next.IsDelete)
                                {
                                    node.Next = node.Next.Next;
                                }
                                if (node.Previous.IsDelete)
                                {
                                    node.Previous = node.Previous.Previous;
                                }
                                node.IsDelete = true;
                            }

                        }
                    }
                }
            }
        }
        /// <summary>
        /// 当前节点
        /// </summary>
        public LinkNode<K> GetNowAndStep()
        {
            if (_NowNode == null)
            {
                if (First == null)
                {
                    return null;
                }
                _NowNode = First;
            }
            var tmp = _NowNode;
            _NowNode = _NowNode.Next;
            return tmp;
        }
        public List<V> GetList()
        {
            return ditdata.Values.ToList();
        }
        public List<K> GetKeys()
        {
            return ditdata.Keys.ToList();
        }
        public int Count
        {
            get { return ditdata.Count; }
        }
    }
    /// <summary>
    /// ServerInfo链表
    /// </summary>
    public class LinkedServerInfo : LinkedByDit<string, ServerInfo>
    {
        public LinkedServerInfo()
        {
            ReadTime = WriteTime = DateTime.Now;
        }
        /// <summary>
        /// 上一次写时间
        /// </summary>
        public DateTime WriteTime { get; set; }
        /// <summary>
        /// 上一次读时间
        /// </summary>
        public DateTime ReadTime { get; set; }
        /// <summary>
        /// 检查超时
        /// </summary>
        /// <param name="timeoutMsec"></param>
        /// <returns>是否整体超时</returns>
        public bool CheckTimeout(double timeoutMsec)
        {
            //遍历链表 移除超时服务
            var now = First;
            ConcurrentDictionary<string, string> ditChecked = new ConcurrentDictionary<string, string>();
            while (now != null && !ditChecked.ContainsKey(now.Key))
            {
                var val = GetValue(now.Key);
                if (val.IsTimeout(timeoutMsec))
                {
                    Delete(now
                        , () => val.IsTimeout(timeoutMsec));
                }
                ditChecked[now.Key] = now.Key;
                now = now.Next;
            }
            //返回链表是否整体超时
            return (this.Count == 0 && ReadTime.DiffNowMSec() > timeoutMsec && WriteTime.DiffNowMSec() > timeoutMsec);
        }
    }
    /// <summary>
    /// 服务列表
    /// </summary>
    public class ServerListByVer
    {
        /// <summary>
        /// 链表
        /// </summary>
        ConcurrentDictionary<string, LinkedServerInfo> ditByVer = new ConcurrentDictionary<string, LinkedServerInfo>();
        /// <summary>
        /// 获取或初始化ServerInfo链表
        /// </summary>
        /// <param name="Ver">版本号</param>
        /// <returns>ServerInfo链表</returns>
        private LinkedServerInfo GetListByVer(string Ver)
        {
            if (!ditByVer.ContainsKey(Ver))
            {
                lock (lckVer)
                {
                    if (!ditByVer.ContainsKey(Ver))
                    {
                        var item = new LinkedServerInfo();
                        ditByVer[Ver] = item;
                        return item;
                    }
                }
            }
            return ditByVer[Ver];
        }
        /// <summary>
        /// ServerInfo链表锁
        /// </summary>
        object lckVer = new object();
        object lckLoseVer = new object();
        /// <summary>
        /// 更新服务信息
        /// </summary>
        /// <param name="info"></param>
        public void Update(ServerInfo info)
        {
            info.StartTime = info.LastHeartbeat = DateTime.Now;
            var ditServerByName = GetListByVer(info.Ver);
            ditServerByName.WriteTime = DateTime.Now;
            var key = info.GetKey();
            ditServerByName.Update(key, info, (old, val) =>
             {
                 if (old != null)
                 {
                     val.StartTime = old.StartTime;
                 }
                 return val;
             });
        }
        /// <summary>
        /// 获取下一个可用服务
        /// </summary>
        /// <param name="Ver"></param>
        /// <returns></returns>
        public ServerInfo GetNext(ServerConf conf, string MKey)
        {

            int times = 0;
            while (times < 10)
            {
                times++;
                var ditServerByName = GetListByVer(conf.NowVer);
                ditServerByName.ReadTime = DateTime.Now;
                var node = ditServerByName.GetNowAndStep();
                if (node == null)
                {
                    return null;
                }
                var tmp = ditServerByName.GetValue(node.Key);
                if (tmp == null)
                {
                    continue;
                }
                if (tmp.IsTimeout(conf.TimeoutMsec))
                {
                    ditServerByName.Delete(node
                        , () => tmp.IsTimeout(conf.TimeoutMsec));
                }
                else if (MKey == null || tmp.MKey == MKey)
                {
                    return tmp;
                }
            }
            //找一圈后如果找不到 则返回空
            return null;
        }



        public int Count
        {
            get
            {
                int i = 0;
                foreach (var item in ditByVer.Values.ToList())
                {
                    i += item.Count;
                }
                return i;
            }
        }

        public List2 GetList(string NowVer)
        {
            List2 lst = new List2();
            foreach (var key in ditByVer.Keys.ToArray())
            {
                if (key == NowVer)
                {
                    lst.NowVer.AddRange(ditByVer[key].GetList());
                }
                else
                {
                    lst.OtherVer.AddRange(ditByVer[key].GetList());
                }
            }
            return lst;
        }

        public ServerInfo Get(string ver, string key)
        {
            return GetListByVer(ver).GetValue(key);
        }

        /// <summary>
        /// 检查超时
        /// </summary>
        /// <param name="timeoutMsec"></param>
        public void CheckTimeout(double timeoutMsec)
        {
            var keys = ditByVer.Keys.ToArray();
            foreach (var key in keys)
            {
                if (ditByVer.ContainsKey(key))
                {
                    var item = ditByVer[key];
                    if (item.CheckTimeout(timeoutMsec))
                    {
                        ditByVer.TryRemove(key, out var _);
                    }
                }
            }
        }
        public class List2
        {
            public List2()
            {
                NowVer = new List<ServerInfo>();
                OtherVer = new List<ServerInfo>();
            }
            public List<ServerInfo> NowVer { get; set; }
            public List<ServerInfo> OtherVer { get; set; }
        }
    }
}
