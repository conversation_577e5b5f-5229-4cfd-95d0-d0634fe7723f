﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace SQ.Base.Cluster
{
    public class ClusterClient
    {
        /// <summary>
        /// 单例
        /// </summary>
        public static ClusterClient Instance = new ClusterClient();
        static ClusterClient()
        {
            Instance.ReadServerConf();
        }

        ClusterServerInfo config;
        HttpHelper http = new HttpHelper();

        /// <summary>
        /// 心跳
        /// </summary>
        /// <param name="lst">上报的服务信息</param>
        /// <returns>-1 连接错误 1成功 2已被升级，需在适当时停止 3服务已停用，需立即停止</returns>
        public int Heartbeat(List<ServerInfo> lst, string MKey)
        {
            var ret = http.PostPage(config.Addr + "/Heartbeat", "data=" + lst.ToJson() + "&mkey=" + MKey);

            if (int.TryParse(ret, out var i))
            {
                return i;
            }
            return -1;
        }

        public string Authentication(string auth_text, string sign_text, string MKey)
        {
            return http.PostPage(config.Addr + "/Authentication", "auth=" + auth_text + "&sign=" + sign_text + "&mkey=" + MKey);
        }

        public string AuthenticationRecord(string auth_text, string sign_text, string MKey)
        {
            return http.PostPage(config.Addr + "/AuthenticationRecord", "auth=" + auth_text + "&sign=" + sign_text + "&mkey=" + MKey);
        }
        /// <summary>
        /// 升级
        /// </summary>
        /// <param name="Type">服务类型</param>
        /// <param name="NewVer">新版本号</param>
        /// <returns>-1连接错误 0失败 1成功</returns>
        public int Upgrade(int Type, string NewVer)
        {
            var ret = http.GetHTML(config.Addr + "/Upgrade?Type=" + Type + "&NewVer=" + NewVer, "*/*");

            if (int.TryParse(ret, out var i))
            {
                return i;
            }
            return -1;
        }
        /// <summary>
        /// 设置配置
        /// </summary>
        /// <param name="Conf"></param>
        /// <returns>-1连接错误 1成功</returns>
        public int SetConf(ServerConf Conf)
        {
            var ret = http.PostPage(config.Addr + "/SetConf", "Conf=" + Conf.ToJson());

            if (int.TryParse(ret, out var i))
            {
                return i;
            }
            return -1;
        }

        /// <summary>
        /// 获取最优服务器信息
        /// </summary>
        /// <param name="Type">服务类型</param>
        /// <param name="Tag">特征码(在未升级或服务未失效情况下，同一特征码一定会路由到同一服务，为NULL或空字符串时特征码不起作用)</param>
        /// <returns>null:失败或连接错误 其他:连接信息</returns>
        public string GetBest(int Type, string Tag = null)
        {
            if (string.IsNullOrWhiteSpace(Tag))
            {
                return http.GetHTML(config.Addr + "/GetBest?Type=" + Type, "*/*");
            }
            else
            {
                return http.GetHTML(config.Addr + "/GetBest?Type=" + Type + "&Tag=" + Tag, "*/*");
            }
        }





        static string path = System.IO.Path.Combine(SQ.Base.FileHelp.GetMyConfPath(), "Config", "ClusterServer.json");
        public bool ReadServerConf()
        {
            try
            {
                if (File.Exists(path))
                {
                    config = SerializableHelper.DeserializeSetting<ClusterServerInfo>(path, SerializableHelper.SerializableType.Json);
                    return true;
                }
                config = new ClusterServerInfo();
                WriteServerConf();
                return false;
            }
            catch (System.Exception ex)
            {
                SQ.Base.ErrorLog.WriteLog4Ex("ReadConf", ex);
                return false;
            }
        }
        public bool WriteServerConf()
        {
            try
            {
                SerializableHelper.SerializeSetting<ClusterServerInfo>(config, path, false, SerializableHelper.SerializableType.Json);
                return true;
            }
            catch (System.Exception ex)
            {
                SQ.Base.ErrorLog.WriteLog4Ex("WriteConf", ex);
                return false;
            }
        }
        /// <summary>
        /// 是否配置正确
        /// </summary>
        /// <returns></returns>
        public bool HasServerConf()
        {
            return config != null && !string.IsNullOrWhiteSpace(config.Addr);
        }
    }

    public class ClusterServerInfo
    {
        /// <summary>
        /// 地址
        /// </summary>
        public string Addr { get; set; }
    }
}
