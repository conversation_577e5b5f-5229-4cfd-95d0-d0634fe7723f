﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SQ.Base
{
    public static class StreamHelper
    {
        public static byte Read2Byte(this System.IO.Stream stream)
        {
            var tmp = new byte[1];
            stream.Read(tmp, 0, 1);
            return tmp[0];
        }
        public static ushort Read2Uint16(this System.IO.Stream stream, bool big = true)
        {
            var data = new byte[2];
            stream.Read(data, 0, 2);
            if (big)
            {
                return ByteHelper.Byte2ToUInt(data[0], data[1]);
            }
            else
            {

                return ByteHelper.Byte2ToUInt(data[1], data[0]);
            }
        }
        public static uint Read2Uint32(this System.IO.Stream stream, bool big = true)
        {
            var data = new byte[4];
            stream.Read(data, 0, 4);
            if (big)
            {
                return ByteHelper.Byte4ToUInt(data[0], data[1], data[2], data[3]);
            }
            else
            {

                return ByteHelper.Byte4ToUInt(data[3], data[2], data[1], data[0]);
            }
        }
        public static byte[] Read2Bytes(this System.IO.Stream stream, int count)
        {
            var data = new byte[count];
            stream.Read(data, 0, count);
            return data;
        }

        public static bool Save2File(this System.IO.Stream stream, string filePath, int length, int buffLen = 4096)
        {
            byte[] buff = new byte[buffLen];
            int rlen = buffLen;
            using (var file = System.IO.File.Open(filePath, System.IO.FileMode.Create, System.IO.FileAccess.Write))
            {
                while (length > 0 && rlen > 0)
                {
                    if (buffLen > length)
                        rlen = stream.Read(buff, 0, length);
                    else
                        rlen = stream.Read(buff, 0, buffLen);
                    if (rlen > 0)
                    {
                        length -= rlen;
                        file.Write(buff, 0, buffLen);
                    }
                }
                file.Flush();
                file.Close();

            }
            return length == 0;
        }
    }
}
