﻿using System.Xml.Serialization;
using System.IO;
using System;
using System.Runtime.Serialization.Formatters.Binary;

namespace SQ.Base
{
    public class SerializableHelper
    {
        public enum SerializableType
        {
            Xml,
            Binary,
            Json,
            CJson
        }
        #region 序列化设置类

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <typeparam name="T">序列化类型</typeparam>
        /// <param name="model">序列化实体</param>
        /// <param name="path">保存文件路径</param>
        public static void SerializeSetting<T>(T model, string path, bool BakOld = false, SerializableType type = SerializableType.Xml)
        {
            if (BakOld && System.IO.File.Exists(path))
            {
                System.IO.File.Move(path, path + " " + DateTime.Now.Ticks + ".bak");
            }
            if (!Directory.Exists(Path.GetDirectoryName(path)))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(path));
            }
            if (type == SerializableType.Xml)
            {
                XmlSerializer mySerializer = new XmlSerializer(typeof(T));
                // To write to a file, create a StreamWriter object.
                using (StreamWriter myWriter = new StreamWriter(path))
                {
                    mySerializer.Serialize(myWriter, model);
                    myWriter.Close();
                }
            }
            else if (type == SerializableType.Json)
            {
                using (StreamWriter myWriter = new StreamWriter(path, false, System.Text.Encoding.UTF8))
                {
                    var content = model.ToJson();
                    if (!string.IsNullOrEmpty(content))
                    {
                        myWriter.Write(content);
                        myWriter.Close();
                    }
                }
            }
            else if (type == SerializableType.CJson)
            {
                using (StreamWriter myWriter = new StreamWriter(path, false, System.Text.Encoding.UTF8))
                {
                    myWriter.Write(model.ToCJson());
                    myWriter.Close();
                }
            }
            else
            {

#if NET8_0_OR_GREATER
                throw new System.Exception("Obsolete");
#else
                BinaryFormatter mySerializer = new BinaryFormatter();
                using (FileStream myWriter = new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.Read))
                {
                    mySerializer.Serialize(myWriter, model);
                    myWriter.Close();
                }
#endif
            }
        }


        /// <summary>
        /// 读取配置
        /// </summary>
        /// <typeparam name="T">序列化类型</typeparam>
        /// <param name="path">文件路径</param>
        /// <returns>序列化实体</returns>
        public static T DeserializeSetting<T>(string path, SerializableType type = SerializableType.Xml)
        {
            if (type == SerializableType.Xml)
            {
                // Construct an instance of the XmlSerializer with the type
                // of object that is being deserialized.
                var arr = XmlSerializer.FromTypes(new[] { typeof(T) });
                if (arr.Length == 0)
                {
                    return default(T);
                }
                XmlSerializer mySerializer = arr[0];
                // To read the file, create a FileStream.
                using (FileStream myFileStream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    // Call the Deserialize method and cast to the object type.
                    var model = (T)mySerializer.Deserialize(myFileStream);
                    myFileStream.Close();
                    return model;
                }
            }
            else if (type == SerializableType.Json)
            {
                var str = File.ReadAllText(path, System.Text.Encoding.UTF8);
                return str.ParseJSON<T>();

            }
            else if (type == SerializableType.CJson)
            {
                var str = File.ReadAllText(path, System.Text.Encoding.UTF8);
                return str.ParseCJSON<T>();
            }
            else
            {
#if NET8_0_OR_GREATER
                throw new System.Exception("Obsolete");
#else
                // Construct an instance of the XmlSerializer with the type
                // of object that is being deserialized.
                BinaryFormatter mySerializer = new BinaryFormatter();
                // To read the file, create a FileStream.
                using (FileStream myFileStream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    // Call the Deserialize method and cast to the object type.
                    var model = (T)mySerializer.Deserialize(myFileStream);
                    myFileStream.Close();
                    return model;
                }
#endif
            }
        }

        public static T DeserializeByStr<T>(string str, SerializableType type = SerializableType.Xml)
        {
            if (type == SerializableType.Xml)
            {
                // Construct an instance of the XmlSerializer with the type
                // of object that is being deserialized.
                var arr = XmlSerializer.FromTypes(new[] { typeof(T) });
                if (arr.Length == 0)
                {
                    return default(T);
                }
                XmlSerializer mySerializer = arr[0];

                // To read the file, create a FileStream.
                using (StringReader sr = new StringReader(str))
                {
                    // Call the Deserialize method and cast to the object type.
                    var model = (T)mySerializer.Deserialize(sr);
                    sr.Close();
                    return model;
                }
            }
            else if (type == SerializableType.Json)
            {
                return str.ParseJSON<T>();

            }
            else if (type == SerializableType.CJson)
            {
                return str.ParseCJSON<T>();
            }
            else
            {
                return default(T);
            }
        }
        #region XML

        public static byte[] SerializeSetting<T>(T model)
        {
            XmlSerializer mySerializer = new XmlSerializer(typeof(T));
            // To write to a file, create a StreamWriter object.
            using (MemoryStream myWriter = new MemoryStream())
            {
                mySerializer.Serialize(myWriter, model);
                return myWriter.ToArray();
            }
        }

        /// <summary>
        /// 反序列化信息
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="bts"></param>
        /// <returns></returns>
        public static T DeserializeSetting<T>(byte[] bts)
        {
            // Construct an instance of the XmlSerializer with the type
            // of object that is being deserialized.
            XmlSerializer mySerializer =
            new XmlSerializer(typeof(T));
            // To read the file, create a FileStream.
            using (MemoryStream myStream = new MemoryStream(bts))
            {
                // Call the Deserialize method and cast to the object type.
                var model = (T)mySerializer.Deserialize(myStream);
                myStream.Close();
                return model;
            }
        }
        #endregion

        #region Binary


#if !NET8_0_OR_GREATER
        /// <summary>
        /// 二进制序列化
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="model"></param>
        /// <returns></returns>
        public static byte[] SerializeBinary<T>(T model)
        {
            BinaryFormatter mySerializer = new BinaryFormatter();
            // To write to a file, create a StreamWriter object.
            using (MemoryStream myWriter = new MemoryStream())
            {
                mySerializer.Serialize(myWriter, model);
                return myWriter.ToArray();
            }
        }
        /// <summary>
        /// 二进制反序列化
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="bts"></param>
        /// <returns></returns>
        public static T DeserializeBinary<T>(byte[] bts)
        {
            // Construct an instance of the XmlSerializer with the type
            // of object that is being deserialized.
            BinaryFormatter mySerializer = new BinaryFormatter();
            // To read the file, create a FileStream.
            using (MemoryStream myStream = new MemoryStream(bts))
            {
                // Call the Deserialize method and cast to the object type.
                var model = (T)mySerializer.Deserialize(myStream);
                myStream.Close();
                return model;
            }
        }
#endif

        #endregion

        #endregion
    }
}
