﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.0;netstandard2.1;net45;net8.0</TargetFrameworks>
    <RootNamespace>SQ.Base</RootNamespace>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateSerializationAssemblies>Auto</GenerateSerializationAssemblies>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|netstandard2.1|AnyCPU'">
    <WarningLevel>3</WarningLevel>
  </PropertyGroup>




  <!--通用平台配置满足netstandard2.0和net45-->
  <ItemGroup>
    <PackageReference Include="log4net" Version="2.0.17" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />   
  </ItemGroup>

  <!--平台配置满足netstandard2.0-->
  <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.0' Or '$(TargetFramework)' == 'netstandard2.1' Or '$(TargetFramework)' == 'net8.0'">
    <PackageReference Include="System.IO.Pipelines" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json">
      <Version>2.1.1</Version>
    </PackageReference>
    <PackageReference Include="System.Drawing.Common" Version="8.0.5" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="4.5.0" />
  </ItemGroup>

  <!--平台配置满足net45-->
  <ItemGroup Condition="'$(TargetFramework)' == 'net45'">

    <Reference Include="System.configuration" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Design" />
  </ItemGroup>

  <ItemGroup>
    <None Update="log4.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
	
</Project>
