﻿using System;
using System.IO;
using System.IO.Compression;
using System.Text;

namespace SQ.Base.Compression
{
    public class Compressor
    {
        public static string Compress(string str, CompressionType algorithm)
        {
            if (algorithm == CompressionType.None)
            {
                return str;
            }
            var buffer = Encoding.UTF8.GetBytes(str);
            var data = Compress(buffer, algorithm);
            return Convert.ToBase64String(data);
        }
        public static byte[] Compress(byte[] decompressedData, CompressionType algorithm)
        {
            if (algorithm == CompressionType.None)
            {
                return decompressedData;
            }
            using (MemoryStream stream = new MemoryStream())
            {
                if (algorithm == CompressionType.GZip)
                {

                    GZipStream stream2 = new GZipStream(stream, CompressionMode.Compress, true);
                    stream2.Write(decompressedData, 0, decompressedData.Length);
                    stream2.Close();
                }
                else
                {
                    DeflateStream stream3 = new DeflateStream(stream, CompressionMode.Compress, true);
                    stream3.Write(decompressedData, 0, decompressedData.Length);
                    stream3.Close();
                }
                return stream.ToArray();
            }
        }

        public static bool CompressFile(string FileName, string OutFileName, CompressionType algorithm)
        {
            if (algorithm == CompressionType.None)
            {
                return false;
            }
            if (!Directory.Exists(Path.GetDirectoryName(OutFileName)))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(OutFileName));
            }
            using (FileStream outFS = new FileStream(OutFileName, FileMode.Create))
            {
                using (FileStream stream = new FileStream(FileName, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    Compress(stream, outFS, algorithm);
                }
            }
            return true;
        }

        public static void Compress(Stream InStream, Stream OutStream, CompressionType algorithm)
        {
            if (algorithm == CompressionType.GZip)
            {

                using (GZipStream gzS = new GZipStream(OutStream, CompressionMode.Compress, true))
                {

                    int num;
                    byte[] buffer = new byte[0x400];
                    while ((num = InStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        gzS.Write(buffer, 0, num);
                    }
                    gzS.Close();
                }
            }
            else
            {
                using (DeflateStream dfS = new DeflateStream(OutStream, CompressionMode.Compress, true))
                {
                    int num;
                    byte[] buffer = new byte[0x400];
                    while ((num = InStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        dfS.Write(buffer, 0, num);
                    }
                    dfS.Close();
                }
            }
        }

        public static string Decompress(string str, CompressionType algorithm)
        {
            if (algorithm == CompressionType.None)
            {
                return str;
            }
            var buffer = Convert.FromBase64String(str);
            var data = Decompress(buffer, algorithm);
            return Encoding.UTF8.GetString(data);
        }
        public static byte[] Decompress(byte[] compressedData, CompressionType algorithm)
        {
            if (algorithm == CompressionType.None)
            {
                return compressedData;
            }
            using (MemoryStream stream = new MemoryStream(compressedData))
            {
                //if (algorithm == CompressionType.GZip)
                //{
                //    using (GZipStream stream2 = new GZipStream(stream, CompressionMode.Decompress))
                //    {
                //        return LoadToBuffer(stream2);
                //    }
                //}
                //else
                //{
                //    using (DeflateStream stream3 = new DeflateStream(stream, CompressionMode.Decompress))
                //    {
                //        return LoadToBuffer(stream3);
                //    }
                //}
                using (MemoryStream outstream = new MemoryStream())
                {
                    Decompress(stream, outstream, algorithm);
                    return outstream.ToArray();
                }
            }
        }
        public static bool Decompress(Stream InStream, Stream OutStream, CompressionType algorithm)
        {
            if (algorithm == CompressionType.None)
            {
                return false;
            }
            if (algorithm == CompressionType.GZip)
            {
                using (GZipStream gzS = new GZipStream(InStream, CompressionMode.Decompress))
                {

                    int num;
                    byte[] buffer = new byte[0x400];
                    while ((num = gzS.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        OutStream.Write(buffer, 0, num);
                    }
                }

            }
            else
            {
                using (DeflateStream gzS = new DeflateStream(InStream, CompressionMode.Decompress))
                {


                    int num;
                    byte[] buffer = new byte[0x400];
                    while ((num = gzS.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        OutStream.Write(buffer, 0, num);
                    }

                }
            }

            return false;
        }
        public static bool DecompressFile(string FileName, string OutFileName, CompressionType algorithm)
        {
            if (algorithm == CompressionType.None)
            {
                return false;
            }
            if (!Directory.Exists(Path.GetDirectoryName(OutFileName)))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(OutFileName));
            }
            using (FileStream outFS = new FileStream(OutFileName, FileMode.Create))
            {
                using (FileStream stream = new FileStream(FileName, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    Decompress(stream, outFS, algorithm);
                }
            }
            return true;
        }


        public static byte[] LoadToBuffer(Stream stream)
        {
            using (MemoryStream stream2 = new MemoryStream())
            {
                int num;
                byte[] buffer = new byte[0x400];
                while ((num = stream.Read(buffer, 0, buffer.Length)) > 0)
                {
                    stream2.Write(buffer, 0, num);
                }
                return stream2.ToArray();
            }
        }
    }
}