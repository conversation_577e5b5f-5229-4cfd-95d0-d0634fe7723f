﻿using System;
using System.Collections.Generic;

namespace SQ.Base
{
    public class CheckHelper
    {
        /// <summary>
        /// CRC-CCITT
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static UInt16 crc16_ccitt(byte[] data, int offset, int length)
        {
            UInt16 crc = 0xFFFF;          // initial value
            UInt16 polynomial = 0x1021;   // 0001 0000 0010 0001  (0, 5, 12)
            for (int j = offset; j < offset + length; j++)
            {
                for (int i = 0; i < 8; i++)
                {
                    bool bit = ((data[j] >> (7 - i) & 1) == 1);
                    bool c15 = ((crc >> 15 & 1) == 1);
                    crc <<= 1;
                    if (c15 ^ bit)
                        crc ^= polynomial;
                }
            }
            crc &= 0xffff;

            return crc;
        }
        /// <summary>
        /// CRC16 初值0
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static UInt16 crc16_DO(byte[] data, int offset, int length)
        {
            UInt16 crc = 0;          // initial value
            UInt16 polynomial = 0x1021;   // 0001 0000 0010 0001  (0, 5, 12)
            for (int j = offset; j < offset + length; j++)
            {
                for (int i = 0; i < 8; i++)
                {
                    bool bit = ((data[j] >> (7 - i) & 1) == 1);
                    bool c15 = ((crc >> 15 & 1) == 1);
                    crc <<= 1;
                    if (c15 ^ bit)
                        crc ^= polynomial;
                }
            }
            crc &= 0xffff;

            return crc;
        }

        public static void crc16_ccittAndEscape(List<byte> data, int offset, int length)
        {
            var crc = crc16_ccitt(data.ToArray(), offset, length);
            var btstmp = BitConverter.GetBytes(crc);
            data.Insert(offset + length, btstmp[1]);
            data.Insert(offset + length + 1, btstmp[0]);
            length += 2;
            for (int i = offset; i < offset + length; i++)
            {
                if (data[i] == 0x5b)//0x5a+0x01
                {
                    data[i] = 0x5a;
                    data.Insert(i + 1, 0x01);
                    i++;
                    length++;
                }
                else if (data[i] == 0x5a)//0x5a+0x02
                {
                    data[i] = 0x5a;
                    data.Insert(i + 1, 0x02);
                    i++;
                    length++;
                }
                else if (data[i] == 0x5d)//0x5e+0x01
                {
                    data[i] = 0x5e;
                    data.Insert(i + 1, 0x01);
                    i++;
                    length++;
                }
                else if (data[i] == 0x5e)//0x5e+0x02
                {
                    data[i] = 0x5e;
                    data.Insert(i + 1, 0x02);
                    i++;
                    length++;
                }
            }
        }

        /// <summary>
        /// CRC效验
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static short CRC(byte[] data, int offset, int length)
        {
            short xda = -1;
            short xdapoly = -24575;
            for (int i = offset; i <= offset + (length - 1); i++)
            {
                xda = (short)(xda ^ data[i]);
                for (int j = 0; j <= 7; j++)
                {
                    byte xdabit = Convert.ToByte(xda & 0x1);
                    xda >>= 1;
                    if (xdabit == 1)
                    {
                        xda = (short)(xda ^ xdapoly);
                    }
                }
            }
            return xda;
        }
        ///// <summary>
        ///// 校验并转义(CRC)
        ///// </summary>
        ///// <param name="data"></param>
        ///// <param name="offset"></param>
        ///// <param name="length"></param>
        //public static void CRCAndEscape(ref List<byte> data, int offset, int length)
        //{
        //    short xda = -1;
        //    short xdapoly = -24575;
        //    for (int i = offset; i < offset + length; i++)
        //    {
        //        xda = (short)(xda ^ data[i]);
        //        for (int j = 0; j <= 7; j++)
        //        {
        //            byte xdabit = Convert.ToByte(xda & 0x1);
        //            xda >>= 1;
        //            if (xdabit == 1)
        //            {
        //                xda = (short)(xda ^ xdapoly);
        //            }
        //        }
        //        if (data[i] == 0x5b)//0x5a+0x01
        //        {
        //            data[i] = 0x5a;
        //            data.Insert(i + 1, 0x01);
        //            length++;
        //            i++;
        //        }
        //        else if (data[i] == 0x5a)//0x5a+0x02
        //        {
        //            data.Insert(i + 1, 0x02);
        //            length++;
        //            i++;
        //        }
        //        else if (data[i] == 0x5d)//0x5e+0x01
        //        {
        //            data[i] = 0x5e;
        //            data.Insert(i + 1, 0x01);
        //            length++;
        //            i++;
        //        }
        //        else if (data[i] == 0x5e)//0x5e+0x02
        //        {
        //            data.Insert(i + 1, 0x02);
        //            length++;
        //            i++;
        //        }
        //    }
        //    byte[] bttmp = new byte[2];
        //    ByteHelper.IntToByte2(xda, bttmp, 0);
        //    for (int i = 0; i < 2; i++)
        //    {
        //        if (bttmp[i] == 0x5b)//0x5a+0x01
        //        {
        //            data.Insert(offset + length + i, 0x5a);
        //            data.Insert(offset + length + i + 1, 0x01);
        //            length++;
        //        }
        //        else if (data[i] == 0x5a)//0x5a+0x02
        //        {
        //            data.Insert(offset + length + i, 0x5a);
        //            data.Insert(offset + length + i + 1, 0x02);
        //            length++;
        //        }
        //        else if (data[i] == 0x5d)//0x5e+0x01
        //        {
        //            data.Insert(offset + length + i, 0x5e);
        //            data.Insert(offset + length + i + 1, 0x01);
        //            length++;
        //        }
        //        else if (data[i] == 0x5e)//0x5e+0x02
        //        {
        //            data.Insert(offset + length + i, 0x5e);
        //            data.Insert(offset + length + i + 1, 0x02);
        //            length++;
        //        }
        //        else
        //        {
        //            data.Insert(offset + length + i, bttmp[i]);
        //        }
        //    }
        //}


        /// <summary>
        /// 校验(累加和，丢弃溢出的高字节数据)
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static byte Check(byte[] data, int offset, int length)
        {
            Int16 xda = 0;

            for (int i = offset; i <= offset + (length - 1); i++)
            {
                xda += data[i];
                if (xda > 255)
                {
                    xda -= 255;
                }
            }
            return (byte)xda;
        }
        /// <summary>
        /// 校验(取反码，累加和，丢弃溢出的高字节数据)
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static byte Check_Reverse(byte[] data, int offset, int length)
        {
            Int16 xda = 0;

            for (int i = offset; i <= offset + (length - 1); i++)
            {
                xda += (short)~data[i];
                if (xda > 255)
                {
                    xda -= 255;
                }
            }
            return (byte)xda;
        }

        /// <summary>
        /// 和校验
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static int CheckSum(byte[] data, int offset, int length)
        {
            int num = 0;
            for (int i = offset; i <= (offset + (length - 1)); i++)
            {
                num += data[i];
            }
            return num;
        }

        /// <summary>
        /// 和校验
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static int CheckSum_Reverse(byte[] data, int offset, int length)
        {
            int num = 0;
            for (int i = 0; i < length; i++)
            {
                num += ~data[offset + i];
            }
            return num;
        }

        /// <summary>
        /// 校验(异或效验)
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static byte CheckXOR(byte[] data, int offset, int length)
        {
            byte xda = 0;
            for (int i = 0; i < length; i++)
            {
                xda = (byte)((data[i + offset] ^ xda) & 0xFF);
            }
            return xda;
        }

        /// <summary>
        /// 校验(异或效验)
        /// </summary>
        /// <param name="data">校验数据源</param>
        /// <param name="offset">偏移</param>
        /// <param name="length">长度</param>
        /// <param name="iData">提取数据</param>
        /// <param name="ioffset">提取偏移</param>
        /// <returns></returns>
        public static byte CheckXOR(byte[] data, int offset, int length, out byte[] iData, int ioffset)
        {
            byte xda = 0;
            iData = new byte[length - ioffset + offset];
            for (int i = 0; i < length; i++)
            {
                xda = (byte)((data[i + offset] ^ xda) & 0xFF);

                if (iData.Length > i)
                {
                    iData[i] = data[i + ioffset];
                }

            }
            return xda;
        }

        public static byte CheckXOR(byte[] data, long offset, long length)
        {
            byte xda = 0;
            for (long i = 0; i < length; i++)
            {
                xda = (byte)((data[i + offset] ^ xda) & 0xFF);
            }
            return xda;
        }

        public static void XOR(List<byte> data, int offset, int length)
        {
            byte xda = 0;
            for (int i = offset; i < offset + length; i++)
            {
                xda = (byte)(data[i] ^ xda);

            }
            data.Insert(offset + length, xda);

        }

        /// <summary>
        /// 校验并转义(异或效验)
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static void XORAndEscape(List<byte> data, int offset, int length)
        {
            byte xda = 0;
            for (int i = offset; i < offset + length; i++)
            {
                xda = (byte)(data[i] ^ xda);
                if (data[i] == 0x7e)//转义 向后插入一个0x02
                {
                    data[i] = 0x7d;
                    data.Insert(i + 1, 0x02);
                    length++;
                    i++;
                }
                else if (data[i] == 0x7d)//转义 向后插入一个0x01
                {
                    data.Insert(i + 1, 0x01);
                    length++;
                    i++;
                }
            }
            if (xda == 0x7e)//转义 向后插入一个0x02
            {
                data.Insert(offset + length, 0x7d);
                data.Insert(offset + length + 1, 0x02);
            }
            else if (xda == 0x7d)//转义 向后插入一个0x01 
            {
                data.Insert(offset + length, 0x7d);
                data.Insert(offset + length + 1, 0x01);
            }
            else
            {
                data.Insert(offset + length, xda);
            }
        }

        /// <summary>
        /// 校验并转义(异或效验)
        /// JTGEZN
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        public static void XORAndEscapeForGEZN(List<byte> data, int offset, int length)
        {
            byte xda = 0;
            for (int i = offset; i < offset + length; i++)
            {
                xda = (byte)(data[i] ^ xda);
                if (data[i] == 0x8e)//转义 向后插入一个0x02
                {
                    data[i] = 0x8d;
                    data.Insert(i + 1, 0x02);
                    length++;
                    i++;
                }
                else if (data[i] == 0x8d)//转义 向后插入一个0x01
                {
                    data.Insert(i + 1, 0x01);
                    length++;
                    i++;
                }
            }
            if (xda == 0x8e)//转义 向后插入一个0x02
            {
                data.Insert(offset + length, 0x8d);
                data.Insert(offset + length + 1, 0x02);
            }
            else if (xda == 0x8d)//转义 向后插入一个0x01 
            {
                data.Insert(offset + length, 0x8d);
                data.Insert(offset + length + 1, 0x01);
            }
            else
            {
                data.Insert(offset + length, xda);
            }
        }

        public static short CRC2(byte[] data, int offset, int length)
        {
            int count = length;
            byte[] buf = new byte[length + 2];
            Array.Copy(data, offset, buf, 0, length);
            int ptr = 0;
            int i = 0;
            short crc = 0;
            byte crc1, crc2, crc3;
            crc1 = buf[ptr++];
            crc2 = buf[ptr++];
            buf[count] = 0;
            buf[count + 1] = 0;
            while (--count >= 0)
            {
                crc3 = buf[ptr++];
                for (i = 0; i < 8; i++)
                {
                    if (((crc1 & 0x80) >> 7) == 1)//判断crc1高位是否为1
                    {
                        crc1 = (byte)(crc1 << 1); //移出高位
                        if (((crc2 & 0x80) >> 7) == 1)//判断crc2高位是否为1
                        {
                            crc1 = (byte)(crc1 | 0x01);//crc1低位由0变1
                        }
                        crc2 = (byte)(crc2 << 1);//crc2移出高位
                        if (((crc3 & 0x80) >> 7) == 1) //判断crc3高位是否为1
                        {
                            crc2 = (byte)(crc2 | 0x01); //crc2低位由0变1
                        }
                        crc3 = (byte)(crc3 << 1);//crc3移出高位
                        crc1 = (byte)(crc1 ^ 0x10);
                        crc2 = (byte)(crc2 ^ 0x21);
                    }
                    else
                    {
                        crc1 = (byte)(crc1 << 1); //移出高位
                        if (((crc2 & 0x80) >> 7) == 1)//判断crc2高位是否为1
                        {
                            crc1 = (byte)(crc1 | 0x01);//crc1低位由0变1
                        }
                        crc2 = (byte)(crc2 << 1);//crc2移出高位
                        if (((crc3 & 0x80) >> 7) == 1) //判断crc3高位是否为1
                        {
                            crc2 = (byte)(crc2 | 0x01); //crc2低位由0变1
                        }
                        crc3 = (byte)(crc3 << 1);//crc3移出高位
                    }
                }
            }
            crc = (short)(((crc1 << 8) + crc2) & 0xFFFF);
            return crc;
        }
        public static void CRC2AndEscape(List<byte> data, int offset, int length)
        {
            var crc = CRC2(data.ToArray(), offset, length);
            var btstmp = BitConverter.GetBytes(crc);
            data.Insert(offset + length, btstmp[1]);
            data.Insert(offset + length + 1, btstmp[0]);
            length += 2;
            for (int i = offset; i < offset + length; i++)
            {
                if (data[i] == 0x5b)//0x5a+0x01
                {
                    data[i] = 0x5a;
                    data.Insert(i + 1, 0x01);
                    i++;
                    length++;
                }
                else if (data[i] == 0x5a)//0x5a+0x02
                {
                    data[i] = 0x5a;
                    data.Insert(i + 1, 0x02);
                    i++;
                    length++;
                }
                else if (data[i] == 0x5d)//0x5e+0x01
                {
                    data[i] = 0x5e;
                    data.Insert(i + 1, 0x01);
                    i++;
                    length++;
                }
                else if (data[i] == 0x5e)//0x5e+0x02
                {
                    data[i] = 0x5e;
                    data.Insert(i + 1, 0x02);
                    i++;
                    length++;
                }
            }
        }

        /// <summary>
        /// 加密算法
        /// </summary>
        /// <param name="key"></param>
        /// <param name="buffer"></param>
        /// <param name="size"></param>
        /// <param name="M1"></param>
        /// <param name="IA1"></param>
        /// <param name="IC1"></param>
        public static void encrypt(uint key, ref byte[] buffer, int offset, int length, uint M1, uint IA1, uint IC1)
        {
            if (0 == key)
            {
                key = 1;
            }
            uint mkey = M1;
            if (0 == mkey)
            {
                mkey = 1;
            }
            length += offset;
            while (offset < length)
            {
                key = IA1 * (key % mkey) + IC1;
                buffer[offset] ^= (byte)((key >> 20) & 0xFF);
                offset++;
            }
        }

        public static byte XORCheckSum(byte[] data, int offset, int length)
        {
            byte result = 0;
            for (int i = offset; i < offset + length; i++)
            {
                result ^= data[i];
            }
            return result;
        }
    }
}