﻿
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;

namespace SQ.Base.GW
{
    //车机相关的一个连接，一个物理连接可能有多个此连接
    public abstract class Cheji //: IDisposable
    {
        #region 成员变量
        //物理连接
        public Client client;
        //下线原因
        public LogOutReason NowLogOutReason { get; set; }
        //车机唯一标识
        public string Key { get; protected set; }
        //本体是否已被释放
        public bool IsDispose { get; protected set; }
        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public Cheji()
        {
            IsDispose = false;
        }

        #region 需要子类实现的接口
        /// <summary>
        /// 超时检查
        /// </summary>
        /// <returns>true为超时，false为正常</returns>
        public abstract bool TimeoutCheck();
        /// <summary>
        /// 释放资源,释放后应不再使用它
        /// </summary>
        public virtual void Dispose()
        {
            IsDispose = true;
        }
        #endregion
    }
}
