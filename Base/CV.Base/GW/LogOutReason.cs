﻿using System;
using System.Collections.Generic;
using System.Text;

namespace SQ.Base.GW
{
    public enum LogOutReason
    {
        /// <summary>
        /// 255 默认，未改变
        /// </summary>
        Default = 255,
        /// <summary>
        /// 0 未知下线类型
        /// </summary>
        Unknown = 0,
        /// <summary>
        /// 1 多次上线
        /// </summary>
        Relogin = 1,
        /// <summary>
        /// 2 心跳超时
        /// </summary>
        HeartbeatTimeout = 2,
        /// <summary>
        /// 3 连接超时
        /// </summary>
        ConnectionTimeout = 3,
        /// <summary>
        /// 4 网关重启 
        /// </summary>
        GatewayReboot = 4,
        /// <summary>
        /// 5 错误数据
        /// </summary>
        DataError = 5,
        /// <summary>
        /// 6 被动关闭
        /// </summary>
        ShutDown = 6,
        /// <summary>
        /// 7 设备注销
        /// </summary>
        LogOut = 7,
        /// <summary>
        /// 8 强制下线
        /// </summary>
        ForcedOffline = 8,

        /// <summary>
        /// 客户端心跳超时
        /// </summary>
        HeartbeatTimeout_Client   =  100,
        /// <summary>
        /// 车机心跳超时
        /// </summary>
        HeartbeatTimeout_Cheji,

    }

    public class LogOutReasonString
    {
        static public string ToString(LogOutReason reason)
        {
            switch (reason)
            {
                case LogOutReason.HeartbeatTimeout:
                    { return "心跳超时"; }
                case LogOutReason.HeartbeatTimeout_Client:
                    { return "客户端心跳超时"; }
                case LogOutReason.HeartbeatTimeout_Cheji:
                    { return "车机心跳超时"; }
                case LogOutReason.ShutDown:
                    { return "远端断开连接"; }
                default:
                    { return "未知下线原因"; }
            }
        }
    }
}
