﻿
using SQ.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace SQ.Base.GW
{
    public abstract class Server<T, C> where T : Client<C> where C : Cheji
    {
        #region 成员变量
        /// <summary>
        /// 客户端列表
        /// </summary>
        private Dictionary<string, T> ClientPool = new Dictionary<string, T>();
        /// <summary>
        /// 客户端列表，读写锁
        /// </summary>
        private ReaderWriterLockSlim _poolLock = new ReaderWriterLockSlim();
        #endregion

        #region 需子类实现的接口
        /// <summary>
        /// 子类实现，启动服务
        /// </summary>
        public abstract void Start();
        /// <summary>
        /// 子类实现，停止服务
        /// </summary>
        public abstract void Stop();
        /// <summary>
        /// 子类实现，获得ClientPool的索引key值
        /// </summary>
        /// <returns>返回key</returns>
        protected abstract string GetClientKey();
        /// <summary>
        /// 子类实现，client删除后调用
        /// </summary>
        protected virtual void OnCJRemove(T client) { }
        #endregion

        #region 公共方法
        /// <summary>
        /// 检查在线列表,超时则断开(启动线程定时检查)
        /// </summary>
        public void Check()
        {
            try
            {
                var items = this.ClientPoolsGetValues();
                foreach (var item in items)
                {
                    if (item.TimeoutCheck())
                    {
                        RemoveClient(item, LogOutReason.HeartbeatTimeout_Client);
                    }
                    else
                    {
                        CheckSinge(item);
                    }
                }
            }
            catch (System.Exception ex)
            {
                Log.WriteLog4Ex("JTTCPServer.Check", ex);
            }
        }
        /// <summary>
        /// 清除所有连接，遍历ClientPool，对每个Client调用RemoveClient
        /// </summary>
        public void ClearClientPool()
        {
            try
            {
                var items = this.ClientPoolsGetValues();
                foreach (var item in items)
                {
                    RemoveClient(item, LogOutReason.GatewayReboot);
                }
            }
            catch (System.Exception ex)
            {
                ErrorLog.WriteLog4Ex("Server::ClearClientPool", ex);
            }
        }
        /// <summary>
        /// 删除客户端,并调用Client::Dispose和OnCJRemove
        /// </summary>
        /// <param name="client">要删除的Client对象</param>
        /// <param name="LogOutReason">删除原因</param>
        public void RemoveClient(T client, LogOutReason logOutReason)
        {
            try
            {
                //记录删除原因
                if (logOutReason != LogOutReason.Default)
                    client.NowLogOutReason = logOutReason;
                //找到Client并删除
                if (this.ClientPoolsDel(client))
                {
                    client.Dispose();
                    OnCJRemove(client);
                }
            }
            catch (System.Exception ex)
            {
                Log.WriteLog4Ex("RemoveClient", ex);
            }
        }

        #region 客户端列表操作
        /// <summary>
        /// 获得客户端列表个数
        /// </summary>
        /// <returns></returns>
        public int ClientPoolsGetCount()
        {
            return ClientPool.Count;
        }
        /// <summary>
        /// 获得客户端列表
        /// </summary>
        /// <returns></returns>
        public T[] ClientPoolsGetValues()
        {
            _poolLock.TryEnterReadLock(-1);
            var items = ClientPool.Values.ToArray();
            _poolLock.ExitReadLock();

            return items;
        }
        /// <summary>
        /// 添加客户端
        /// </summary>
        /// <param name="client"></param>
        public void ClientPoolsAdd(T client)
        {
            try
            {
                _poolLock.TryEnterWriteLock(-1);

                #region 生成key
                string cjKey = GetClientKey();
                while (ClientPool.ContainsKey(cjKey))
                {
                    cjKey += "0";
                }
                #endregion

                client.Key = cjKey;

                ClientPool.Add(cjKey, client);
            }
            finally
            {
                _poolLock.ExitWriteLock();
            }


        }
        #endregion

        #endregion

        #region 私有方法
        /// <summary>
        /// Client没有超时，则会调用Client::CheckCheji()
        /// </summary>
        private void CheckSinge(Client cl)
        {
            try
            {
                cl.CheckCheji();
            }
            catch (System.Exception ex)
            {
                Log.WriteLog4Ex("JTTCPServer.CheckSinge", ex);
            }
        }

        #region 客户端列表操作
        private void ClientPoolsClr()
        {
            try
            {
                _poolLock.TryEnterWriteLock(-1);
                ClientPool.Clear();
            }
            finally
            {
                _poolLock.ExitWriteLock();
            }

        }
        private bool ClientPoolsDel(T client)
        {
            bool flag = false;
            try
            {
                _poolLock.TryEnterWriteLock(-1);
                if (ClientPool.ContainsKey(client.Key))
                {
                    ClientPool.Remove(client.Key);
                    flag = true;
                }
                return flag;
            }
            finally
            {
                _poolLock.ExitWriteLock();
            }

        }
        #endregion

        #endregion
    }
}
