﻿
using SQ.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

//Client为一个物理的链路连接，cheji为一个逻辑上的链接，1个Client可能包含多个Cheji

namespace SQ.Base.GW
{
    //客户端的一个物理连接
    public abstract class Client
    {
        #region 成员变量
        /// <summary>
        /// 下线原因:255 不改变 0未知下线类型 1多次上线 2心跳超时 3连接超时 4网关重启 5错误数据 6被动关闭 7设备注销 8强制下线
        /// </summary>
        public LogOutReason NowLogOutReason { get; set; }
        /// <summary>
        /// 本体是否已被释放
        /// </summary>
        public bool IsDispose { get; protected set; }
        /// <summary>
        /// 连接时间
        /// </summary>
        public System.DateTime CNTTime;
        /// <summary>
        /// 最后一次数据的时间
        /// </summary>
        public System.DateTime LastDataTime;
        /// <summary>
        /// 最后一次鉴权成功时间
        /// </summary>
        public DateTime LastSuccessTime;
        /// <summary>
        /// 连接标识(通常为创建连接时的时间戳)
        /// </summary>
        public string Key { get; set; }
        /// <summary>
        /// 客户端信息IP:Port
        /// </summary>
        public string RemoteInfo { get; protected set; }
        /// <summary>
        /// 客户端ip地址
        /// </summary>
        public string IP;
        /// <summary>
        /// 客户端port
        /// </summary>
        public int Port;
        #endregion

        public Client()
        {
            IsDispose = false;
        }

        #region 需子类实现的接口
        /// <summary>
        /// 子类实现，是否超时，超时删除
        /// </summary>
        public abstract bool TimeoutCheck();
        /// <summary>
        /// 子类实现，发送数据
        /// </summary>
        /// <returns>true为成功，否则失败</returns>
        protected abstract bool SendDataInternal(byte[] bts, int offset, int size, ulong timestamp);
        /// <summary>
        /// 释放
        /// </summary>
        public virtual void Dispose()
        {
            IsDispose = true;
        }
        /// <summary>
        /// 子类实现，会定时调用(检查线程Server::Check)
        /// </summary>
        public virtual void CheckCheji() { }
        #endregion

        #region 公共接口
        /// <summary>
        /// 发送数据，实际发送函数为SendDataInternal
        /// </summary>
        /// <returns>true为成功，否则失败</returns>
        public bool SendData(byte[] bts, int offset, int size, ulong timestamp = 0)
        {
            //没有释放才能发送
            lock(this)
            {
                if (!IsDispose)
                {
                    return SendDataInternal(bts, offset, size, timestamp);
                }
            }
            return false;
        }
        #endregion
    }

    //继承自Client，加入了对Cheji的管理
    public abstract class Client<T> : Client where T : Cheji
    {
        #region 成员变量
        //当前车机列表
        protected Dictionary<string, T> CheJiList { get; } = new Dictionary<string, T>();
        //当前车机列表（CheJiList）的读写锁
        private ReaderWriterLockSlim chejiReadWriterLock { get; } = new ReaderWriterLockSlim();
        #endregion

        #region 需子类实现的接口
        //Client::TimeoutCheck Client::SendDataInternal
        /// <summary>
        /// 子类实现，会定时调用
        /// </summary>
        public virtual void CheckChejiSinge(T nowcj) { }
        /// <summary>
        /// 实现父类方法Client::OnDispose,遍历CheJiList对所有Cheji调用RemoveCheji
        /// </summary>
        public override void Dispose()
        {
            if (IsDispose)
                return;

            base.Dispose();
            //清除车机
            chejiReadWriterLock.TryEnterReadLock(1000);
            var chejiList = CheJiList.Values.ToArray();
            if (chejiReadWriterLock.IsReadLockHeld)
            {
                chejiReadWriterLock.ExitReadLock();
            }
            foreach (var item in chejiList)
            {
                RemoveCheji(item, NowLogOutReason);
            }
        }
        #endregion

        #region 公共接口
        public int GetChejiCount()
        {
            return CheJiList.Count;
        }
        /// <summary>
        /// CheJiList移除Cheji，对Cheji调用OnRemoveCheji
        /// </summary>
        /// <param name="nowcj">要删除的车机</param>
        /// <param name="LogOutReason">删除原因</param>
        /// <param name="Force"></param>
        public virtual void RemoveCheji(T nowcj, LogOutReason logOutReason, bool Force = false)
        {
            try
            {
                try
                {
                    if (logOutReason != LogOutReason.Default)
                        nowcj.NowLogOutReason = logOutReason;
                    chejiReadWriterLock.TryEnterWriteLock(1000);
                    //移除CheJiList容器
                    CheJiList.Remove(nowcj.Key);
                    nowcj.Dispose();
                }
                catch (System.Exception ex)
                {
                    Log.WriteLog4Ex("RemoveCheji:", ex);
                }
                finally
                {
                    if (chejiReadWriterLock.IsWriteLockHeld)
                    {
                        chejiReadWriterLock.ExitWriteLock();
                    }
                }
            }
            catch (System.Exception ex)
            {
                Log.WriteLog4Ex("RemoveCheji:", ex);
            }
        }
        /// <summary>
        /// 从CheJiList中获得Cheji
        /// </summary>
        /// <param name="key">索引</param>
        public T GetChejiFromList(string key)
        {
            chejiReadWriterLock.TryEnterReadLock(1000);
            CheJiList.TryGetValue(key, out var item);
            if (chejiReadWriterLock.IsReadLockHeld)
            {
                chejiReadWriterLock.ExitReadLock();
            }

            return item;
        }
        /// <summary>
        /// 添加Cheji到CheJiList中,Dispose后添加会失败
        /// </summary>
        /// <param name="key">索引</param>
        /// <param name="cheji">要添加的Cheji</param>
        public bool AddChejiToList(string key, T cheji)
        {
            bool ret = true;
            chejiReadWriterLock.TryEnterWriteLock(1000);
            if (IsDispose)
            {
                ret = false;
            }
            else
            {
                base.LastSuccessTime = DateTime.Now;
                CheJiList.Add(key, cheji);
            }
            if (chejiReadWriterLock.IsWriteLockHeld)
            {
                chejiReadWriterLock.ExitWriteLock();
            }

            return ret;
        }
        /// <summary>
        /// 获得车机列表
        /// </summary>
        /// <returns></returns>
        public T[] GetChejiArrayFramList()
        {
            chejiReadWriterLock.TryEnterReadLock(1000);
            var chejiList = CheJiList.Values.ToArray();
            if (chejiReadWriterLock.IsReadLockHeld)
            {
                chejiReadWriterLock.ExitReadLock();
            }
            return chejiList;
        }
        #endregion

        #region 私有
        /// <summary>
        /// 检查cheji是否超时
        /// </summary>
        public override void CheckCheji()
        {
            try
            {
                List<T> lstCheji = null;
                try
                {
                    chejiReadWriterLock.TryEnterReadLock(1000);
                    lstCheji = CheJiList.Values.ToList();
                }
                finally
                {
                    if (chejiReadWriterLock.IsReadLockHeld)
                    {
                        chejiReadWriterLock.ExitReadLock();
                    }
                }
                foreach (var item in lstCheji)
                {
                    if (item.TimeoutCheck())
                    {
                        RemoveCheji(item, LogOutReason.HeartbeatTimeout_Cheji);
                    }
                    else
                    {
                        //没有超时，继续其它检查
                        CheckChejiSinge(item);
                    }
                }
            }
            catch (System.Exception ex)
            {
                Log.WriteLog4Ex("TCPClient.CheckCheji", ex);
            }
        }
        #endregion


    }
}
