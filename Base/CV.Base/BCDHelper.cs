﻿using System;
using System.Collections.Generic;
using System.Text;

namespace SQ.Base
{
    public class BCDHelper
    {
        /// <summary>
        /// 用BCD码压缩数字字符串
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static byte[] StrToBCD(string str)
        {
            return StrToBCD(str, str.Length);
        }
        public static byte[] StrToBCD(string str, int numlen)
        {

            while (str.Length < numlen)
            {
                str = "0" + str;
            }


            int Num4Len = (numlen + 1) / 2;
            byte[] Num4bitByte = new byte[Num4Len];
            int index = 0;
            for (int i = 0; i < numlen; i += 2)
            {
                Num4bitByte[index] = (byte)(((str[i] & 0xf) << 4) | (str[i + 1] & 0xf));
                index++;
            }

            return Num4bitByte;
        }

        /// <summary>
        /// 用BCD码压缩数字字符串
        /// </summary>
        /// <param name="NumBitByte"></param>
        /// <param name="offset">偏移量</param>
        /// <param name="numlen">数字字符串位数</param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static byte[] ByteArrayToBCD_Old(byte[] NumBitByte, int offset, int numlen)
        {
            //8位的ascii码
            int Num4Len = (numlen + 1) / 2 - 1;
            byte[] Num4bitByte = new byte[Num4Len + 1];
            Num4bitByte[Num4Len] = 0;
            for (int i = 0; i <= numlen - 1; i++)
            {
                byte num = 0;

                if (i + offset >= NumBitByte.Length)
                {
                    //num = 255;
                    num = 0;
                }
                else
                {
                    num = Convert.ToByte(NumBitByte[i + offset] - 0x30);
                }

                if (i % 2 == 0)
                {
                    Num4bitByte[i / 2] = Convert.ToByte((Num4bitByte[i / 2] & 0xf) | ((num << 4) & 0xFF));
                }
                else
                {
                    Num4bitByte[i / 2] = Convert.ToByte((Num4bitByte[i / 2] & 0xf0) | num);
                }
            }

            return Num4bitByte;
        }

        public static byte[] ByteArrayToBCD(byte[] NumBitByte, int offset, int numlen)
        {
            int Num4Len = (numlen + 1) / 2;
            byte[] Num4bitByte = new byte[Num4Len];
            int index = 0;
            int val1, val2;
            for (int i = 0; i < numlen; i += 2)
            {
                if (i + offset >= NumBitByte.Length)
                {
                    break;
                }
                else
                {
                    val1 = ((NumBitByte[i] - 0x30) & 0xf) << 4;
                }


                if (i + offset + 1 >= NumBitByte.Length)
                {
                    val2 = 0;
                }
                else
                {
                    val2 = (NumBitByte[i + 1] - 0x30) & 0xf;
                }
                Num4bitByte[index] = (byte)(val1 | val2);
                index++;
            }
            return Num4bitByte;
        }
        /// <summary>
        /// BCD转int
        /// </summary>
        /// <param name="bcdNum"></param>
        /// <param name="offset">偏移量</param>
        /// <param name="numlen">数字字符串位数</param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static int bcdToInt(byte[] bcdNum, int offset, int numlen)
        {
            return Convert.ToInt32(bcdToString(bcdNum, offset, numlen));
        }



        /// <summary>
        /// BCD转字符串
        /// </summary>
        /// <param name="bcdNum"></param>
        /// <param name="offset">偏移量</param>
        /// <param name="numlen">数字字符串位数</param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static string bcdToString(byte[] bcdNum, int offset, int numlen)
        {
            string retString = "";
            int res;
            var len = Math.DivRem(numlen, 2, out res);
            if (res != 0)
            {
                len++;
            }

            for (int i = 0; i < len; i++)
            {
                retString += ((bcdNum[i + offset] & 0xf0) >> 4).ToString("x");
                retString += (bcdNum[i + offset] & 0xf).ToString("x");
            }



            //Dim byteChar As Byte() = New Byte(length - 1) {}
            //Dim tempHigh As Byte = 0, tempLow As Byte = 0
            //Dim i As Integer = 0
            //While tempHigh <> &HF AndAlso tempLow <> &HF0
            //    tempHigh = Convert.ToByte(bcdNum(i + offset) And &HF0)
            //    '取出高四位；
            //    tempHigh = Convert.ToByte(tempHigh >> 4)
            //    tempLow = Convert.ToByte((bcdNum(i + offset) And &HF) << 4)
            //    byteChar(i) = Convert.ToByte(tempLow Or tempHigh)
            //    i += 1
            //End While
            //Dim HexString As String() = BitConverter.ToString(byteChar).Trim().Split("-"c)
            //For Each str As String In HexString
            //    retString += str.Trim()
            //Next
            //Dim LastIndex As Integer = retString.IndexOf("F"c)
            //retString = retString.Substring(0, LastIndex)

            return retString.ToLower().Replace("f", "");
        }

        /// <summary>
        /// BCD码转小时分钟
        /// date:2012-12-18
        /// author:guozh
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset">偏移位</param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static System.DateTime Bcd2ToDateTime(byte[] data, int offset)
        {
            Int32 hour = default(Int32);
            Int32 minute = default(Int32);
            hour = Convert.ToInt32(bcdToString(data, offset, 2));
            minute = Convert.ToInt32(bcdToString(data, offset + 1, 2));
            return new System.DateTime(0000, 00, 00, hour, minute, 00);
        }

        /// <summary>
        /// BCD码转日期
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset">偏移位</param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static System.DateTime Bcd3ToDateTime(byte[] data, int offset)
        {
            Int32 year = default(Int32);
            Int32 month = default(Int32);
            Int32 day = default(Int32);
            year = Convert.ToInt32("20" + bcdToString(data, offset, 2));
            month = Convert.ToInt32(bcdToString(data, offset + 1, 2));
            day = Convert.ToInt32(bcdToString(data, offset + 2, 2));
            if (day == 0 || month == 0)
            {
                return DateTime.MinValue;
            }
            return new System.DateTime(year, month, day);
        }
        public static System.DateTime Bcd4ToDateTime(byte[] data, int offset)
        {
            Int32 year = default(Int32);
            Int32 month = default(Int32);
            Int32 day = default(Int32);
            try
            {
                year = Convert.ToInt32(bcdToString(data, offset, 4));
                month = Convert.ToInt32(bcdToString(data, offset + 2, 2));
                day = Convert.ToInt32(bcdToString(data, offset + 3, 2));
            }
            catch
            {
            }
            if (day == 0 || month == 0)
            {
                return DateTime.MinValue;
            }
            return new System.DateTime(year, month, day);
        }
        public static System.DateTime Bcd5ToDateTime(byte[] data, int offset)
        {
            var year = Convert.ToInt32("20" + bcdToString(data, offset, 2));
            var month = Convert.ToInt32(bcdToString(data, offset + 1, 2));
            var day = Convert.ToInt32(bcdToString(data, offset + 2, 2));
            var hour = Convert.ToInt32(bcdToString(data, offset + 3, 2));
            var minute = Convert.ToInt32(bcdToString(data, offset + 4, 2));
            if (day == 0 || month == 0)
            {
                return DateTime.MinValue;
            }
            return new DateTime(year, month, day, hour, minute, 0);
        }
        /// <summary>
        /// BCD码转时间格式
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset">偏移位</param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static System.DateTime Bcd6ToDateTime(byte[] data, int offset)
        {
            try
            {
                if (data.Length < offset + 6)
                {
                    return DateTime.MinValue;
                }
                int year;// = Convert.ToInt32("20" + bcdToString(data, offset, 2));
                if (!int.TryParse("20" + bcdToString(data, offset, 2), out year))
                {
                    return DateTime.MinValue;
                }
                int month;// = Convert.ToInt32(bcdToString(data, offset + 1, 2));
                if (!int.TryParse(bcdToString(data, offset + 1, 2), out month))
                {
                    return DateTime.MinValue;
                }
                int day;// = Convert.ToInt32(bcdToString(data, offset + 2, 2));
                if (!int.TryParse(bcdToString(data, offset + 2, 2), out day))
                {
                    return DateTime.MinValue;
                }
                int hour;// = Convert.ToInt32(bcdToString(data, offset + 3, 2));
                if (!int.TryParse(bcdToString(data, offset + 3, 2), out hour))
                {
                    return DateTime.MinValue;
                }
                int minute;// = Convert.ToInt32(bcdToString(data, offset + 4, 2));
                if (!int.TryParse(bcdToString(data, offset + 4, 2), out minute))
                {
                    return DateTime.MinValue;
                }
                int second;// = Convert.ToInt32(bcdToString(data, offset + 5, 2));
                if (!int.TryParse(bcdToString(data, offset + 5, 2), out second))
                {
                    return DateTime.MinValue;
                }
                if (day == 0 || month == 0 || month > 12 || day > 31 || hour > 24 || minute > 60 || second > 60)
                {
                    return DateTime.MinValue;
                }
                return new DateTime(year, month, day, hour, minute, second);
            }
            catch
            {
                return DateTime.MinValue;
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static System.DateTime Bcd7ToDateTime(byte[] data, int offset)
        {

            var year = Convert.ToInt32(bcdToString(data, offset, 4));
            var month = Convert.ToInt32(bcdToString(data, offset + 2, 2));
            var day = Convert.ToInt32(bcdToString(data, offset + 3, 2));
            var hour = Convert.ToInt32(bcdToString(data, offset + 4, 2));
            var minute = Convert.ToInt32(bcdToString(data, offset + 5, 2));
            var second = Convert.ToInt32(bcdToString(data, offset + 6, 2));
            if (day == 0 || month == 0 || year == 0)
            {
                return DateTime.MinValue;
            }
            return new DateTime(year, month, day, hour, minute, second);
        }
        /// <summary>
        /// 小时分钟转BCD码
        /// date:2012-12-18
        /// author:guozh
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static byte[] DateTimeToBcd2(System.DateTime dt)
        {
            List<byte> lst = new List<byte>();
            //StringBuilder sb = new StringBuilder();
            //sb.Append(dt.Hour.ToString("D2"));
            //sb.Append(dt.Minute.ToString("D2"));
            lst.AddRange(StrToBCD(dt.ToString("HHmm")));
            return lst.ToArray();
        }
        /// <summary>
        /// 日期转BCD码
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        /// <remarks></remarks>
        public static byte[] DateTimeToBcd3(System.DateTime dt)
        {

            List<byte> lst = new List<byte>();

            //StringBuilder sb = new StringBuilder();
            //sb.Append((dt.Year - 2000).ToString("D2"));
            //sb.Append(dt.Month.ToString("D2"));
            //sb.Append(dt.Day.ToString("D2"));
            lst.AddRange(StrToBCD(dt.ToString("yyMMdd")));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Year - 2000).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Month).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Day).ToString()), 0, 2));

            return lst.ToArray();
        }
        public static byte[] DateTimeToBcd4(System.DateTime dt)
        {
            List<byte> lst = new List<byte>();
            StringBuilder sb = new StringBuilder();
            sb.Append((dt.Year).ToString("D4"));
            sb.Append(dt.Month.ToString("D2"));
            sb.Append(dt.Day.ToString("D2"));
            lst.AddRange(StrToBCD(sb.ToString()));
            return lst.ToArray();
        }
        public static byte[] DateTimeToBcd5(System.DateTime dt)
        {
            List<byte> lst = new List<byte>();
            var strTime = dt.ToString("yyMMddHHmm");
            lst.AddRange(StrToBCD(strTime));

            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Year - 2000).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Month).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Day).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Hour).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Minute).ToString()), 0, 2));
            return lst.ToArray();
        }
        public static byte[] DateTimeToBcd6(System.DateTime dt)
        {
            try
            {
                List<byte> lst = new List<byte>();
                var strTime = dt.ToString("yyMMddHHmmss");
                lst.AddRange(StrToBCD(strTime));

                //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Year - 2000).ToString()), 0, 2));
                //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Month).ToString()), 0, 2));
                //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Day).ToString()), 0, 2));
                //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Hour).ToString()), 0, 2));
                //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Minute).ToString()), 0, 2));
                //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Second).ToString()), 0, 2));

                return lst.ToArray();
            }
            catch (System.Exception ex)
            {

                return new byte[] { 0, 0, 0, 0, 0, 0 };
            }
        }
        public static byte[] DateTimeToBcd7(System.DateTime dt)
        {
            List<byte> lst = new List<byte>();


            StringBuilder sb = new StringBuilder();
            sb.Append(dt.Year.ToString("D4"));
            sb.Append(dt.Month.ToString("D2"));
            sb.Append(dt.Day.ToString("D2"));
            sb.Append(dt.Hour.ToString("D2"));
            sb.Append(dt.Minute.ToString("D2"));
            sb.Append(dt.Second.ToString("D2"));
            lst.AddRange(StrToBCD(sb.ToString()));

            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes(dt.Year.ToString()), 0, 4));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Month).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Day).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Hour).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Minute).ToString()), 0, 2));
            //lst.AddRange(ByteArrayToBCD(System.Text.Encoding.ASCII.GetBytes((dt.Second).ToString()), 0, 2));

            return lst.ToArray();
        }
    }
}
