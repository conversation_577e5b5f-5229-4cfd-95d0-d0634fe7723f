﻿using Newtonsoft.Json;
using SQ.Base.Compression;
using System;
//using System.Runtime.Serialization.Json;
using System.IO;
using System.IO.Compression;
using System.Text;
//using System.Web.Script.Serialization;

namespace SQ.Base
{
    public static class JsonHelper
    {
        public static IJson ijs = new JsonByJN();

        /// <summary>
        /// 对象转化为JSON
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static string ToJson(this object source)
        {
            return ijs.ToJson(source);
        }
        /// <summary>
        /// 对象转化为压缩JSON数据
        /// </summary>
        /// <param name="source"></param>
        /// <param name="tp">压缩模式</param>
        /// <returns></returns>
        public static string ToCJson(this object source, Compression.CompressionType tp = CompressionType.GZip)
        {
            if (source is string)
            {
                return 9 + Compressor.Compress((string)source, CompressionType.GZip);
            }
            return ((int)tp) + Compressor.Compress(ijs.ToJson(source), tp);
        }

        public static string J2C(string json, Compression.CompressionType tp = CompressionType.GZip)
        {
            return ((int)tp) + Compressor.Compress(json, tp);
        }
        public static string C2J(string cjson)
        {
            if (cjson.Length > 0)
            {
                CompressionType tp = (CompressionType)Convert.ToInt32(cjson[0].ToString());
                if (tp == CompressionType.StrBGZIP)
                {
                    return Compressor.Decompress(cjson.Substring(1, cjson.Length - 1), CompressionType.GZip);
                }

                return Compression.Compressor.Decompress(cjson.Substring(1, cjson.Length - 1), tp);
            }
            return cjson;
        }


        /// <summary>
        /// JSON字符串转化为对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="str"></param>
        /// <returns></returns>
        public static T ParseJSON<T>(this string str)
        {
            return ijs.ParseJSON<T>(str);

        }

        public static object ParseJSON(this string str)
        {
            return ijs.ParseJSON(str);
        }
        /// <summary>
        /// JSON字符串转化为对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="str"></param>
        /// <param name="tp"></param>
        /// <returns></returns>
        public static T ParseCJSON<T>(this string str)
        {
            if (!string.IsNullOrEmpty(str.Trim()) && str.Length > 0)
            {
                CompressionType tp = (CompressionType)Convert.ToInt32(str[0].ToString());
                if (tp == CompressionType.StrBGZIP)
                {
                    return (T)(object)(Compression.Compressor.Decompress(str.Substring(1, str.Length - 1), CompressionType.GZip));
                }

                return ijs.ParseJSON<T>(Compression.Compressor.Decompress(str.Substring(1, str.Length - 1), tp));
            }
            return default(T);
        }

        /// <summary>
        /// 通过JSON序列化深度克隆类型
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="Source"></param>
        /// <returns></returns>
        public static T DeepClone<T>(this object Source)
        {
            string jsonString = Source.ToJson();
            return jsonString.ParseJSON<T>();
        }

    }

    ///// <summary>
    ///// 转换JSON JavaScriptSerializer方式
    ///// </summary>
    //public class JsonByJSS : IJson
    //{
    //    private static readonly JavaScriptSerializer jss = new JavaScriptSerializer();
    //    public T ParseJSON<T>(string str)
    //    {
    //        return jss.Deserialize<T>(str);
    //    }

    //    public string ToJson(object source)
    //    {
    //        return jss.Serialize(source);
    //    }
    //    public object ParseJSON(string str)
    //    {
    //        return jss.Deserialize<object>(str);
    //    }
    //}

    /// <summary>
    /// 转换JSON Json.net方式
    /// </summary>
    public class JsonByJN : IJson
    {
        public Formatting tformatting = Formatting.None;
        public JsonSerializerSettings tSetting = new JsonSerializerSettings();
        public JsonSerializerSettings pSetting = new JsonSerializerSettings();

        public T ParseJSON<T>(string str)
        {
            return JsonConvert.DeserializeObject<T>(str, pSetting);
        }

        public string ToJson(object source)
        {
            return JsonConvert.SerializeObject(source, tformatting, tSetting);
        }

        public object ParseJSON(string str)
        {
            return JsonConvert.DeserializeObject(str, pSetting);
        }
    }

    public interface IJson
    {

        /// <summary>
        /// 对象转化为JSON
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        string ToJson(object source);

        /// <summary>
        /// JSON字符串转化为对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="str"></param>
        /// <returns></returns>
        T ParseJSON<T>(string str);
        /// <summary>
        /// JSON字符串转化为object
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        object ParseJSON(string str);

    }
}
