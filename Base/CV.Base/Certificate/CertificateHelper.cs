﻿
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text.RegularExpressions;

namespace SQ.Base.Certificate
{
    public class CertificateHelper
    {
#if NETSTANDARD2_1_OR_GREATER || NET5_0_OR_GREATER
        public static X509Certificate2 LoadPemCertificate(string certificatePath, string privateKeyPath)
        {
            using (var publicKey = new X509Certificate2(certificatePath))
            {
                using (var rsa = RSA.Create())
                {
                    FillRsa(rsa, privateKeyPath);
                    var keyPair = publicKey.CopyWithPrivateKey(rsa);
                    return new X509Certificate2(keyPair.Export(X509ContentType.Pfx));
                }
            }
        }
        public static RSA LoadPemRsa(string path)
        {
            try
            {
                RSA rsa = RSA.Create();
                if (FillRsa(rsa, path))
                {
                    return rsa;
                }
                rsa.Dispose();
            }
            catch (System.Exception ex)
            {
                SQ.Base.ErrorLog.WriteLog4Ex("LoadPemRsa", ex);
            }
            return null;
        }
        public static bool FillRsa(RSA rsa, string path)
        {
            var privateKeyText = File.ReadAllText(path);
            var privateKeyBlocks = privateKeyText.Split("-", StringSplitOptions.RemoveEmptyEntries);
            var privateKeyBytes = Convert.FromBase64String(privateKeyBlocks[1]);


            if (privateKeyBlocks[0] == "BEGIN PRIVATE KEY")
            {
                rsa.ImportPkcs8PrivateKey(privateKeyBytes, out _);
            }
            else if (privateKeyBlocks[0] == "BEGIN RSA PRIVATE KEY")
            {
                rsa.ImportRSAPrivateKey(privateKeyBytes, out _);
            }
            return true;
        }
        private static Regex reg = new Regex(@"-+BEGIN CERTIFICATE-+\s+([^-]+)\s+-");
        public static X509Certificate2Collection GetChainCert(string path)
        {
            var ret = new X509Certificate2Collection();
            var mths = reg.Matches(File.ReadAllText(path));
            for (int i = mths.Count - 1; i > 0; i--)
            {
                var raw = Convert.FromBase64String(mths[i].Groups[1].Value);
                var cert = new X509Certificate2(raw);
                //cert.Archived = true;
                ret.Add(cert);
            }
            return ret;
        }

        public static ulong DecryptTs(RSA rsa, string encryptTs)
        {
            //解密过程
            //1.BSAE64解码为二进制数据
            //2.二进制数据用私钥解密
            //3.解密数据(8字节 无符号 高位在前)转化为UTC时间戳(秒)
            var encryptData = Convert.FromBase64String(encryptTs);
            byte[] decryptData = rsa.Decrypt(encryptData, RSAEncryptionPadding.Pkcs1);
            var ts = ByteHelper.Byte8ToULong(decryptData, 0);
            return ts;
        }
        public static string EncryptTs(RSA rsa, ulong ts)
        {
            //加密过程
            //1.取当前UTC时间戳(秒) 转换为二进制数据(8字节 无符号 高位在前)
            //2.用公钥加密
            //3.加密结果用BASE64编码
            byte[] bts = new byte[8];
            ByteHelper.ULongToByte8(ts, bts, 0);
            var encryptData = rsa.Encrypt(bts, RSAEncryptionPadding.Pkcs1);
            return Convert.ToBase64String(encryptData);
        }
#endif


        public static bool Verify(X509Certificate2 certificate)
        {
            try
            {
                //Output chain information of the selected certificate.
                X509Chain ch = new X509Chain();
                ch.ChainPolicy.RevocationFlag = X509RevocationFlag.EndCertificateOnly;
                ch.ChainPolicy.RevocationMode = X509RevocationMode.NoCheck;
                ch.ChainPolicy.VerificationFlags = X509VerificationFlags.AllFlags;
                ch.Build(certificate);
                Console.WriteLine("Chain Information");
                Console.WriteLine("Chain revocation flag: {0}", ch.ChainPolicy.RevocationFlag);
                Console.WriteLine("Chain revocation mode: {0}", ch.ChainPolicy.RevocationMode);
                Console.WriteLine("Chain verification flag: {0}", ch.ChainPolicy.VerificationFlags);
                Console.WriteLine("Chain verification time: {0}", ch.ChainPolicy.VerificationTime);
                Console.WriteLine("Chain status length: {0}", ch.ChainStatus.Length);
                Console.WriteLine("Chain application policy count: {0}", ch.ChainPolicy.ApplicationPolicy.Count);
                Console.WriteLine("Chain certificate policy count: {0} {1}", ch.ChainPolicy.CertificatePolicy.Count, Environment.NewLine);

                if (ch.ChainStatus.Length > 0)
                {
                    foreach (var item in ch.ChainStatus)
                    {
                        Console.WriteLine($"Status:{item.Status} Information{item.StatusInformation}");
                    }
                }

                //Output chain element information.
                Console.WriteLine("Chain Element Information");
                Console.WriteLine("Number of chain elements: {0}", ch.ChainElements.Count);
                Console.WriteLine("Chain elements synchronized? {0} {1}", ch.ChainElements.IsSynchronized, Environment.NewLine);

                foreach (X509ChainElement element in ch.ChainElements)
                {
                    Console.WriteLine("Element issuer name: {0}", element.Certificate.Issuer);
                    Console.WriteLine("Element certificate valid until: {0}", element.Certificate.NotAfter);
                    Console.WriteLine("Element certificate is valid: {0}", element.Certificate.Verify());
                    Console.WriteLine("Element error status length: {0}", element.ChainElementStatus.Length);
                    Console.WriteLine("Element information: {0}", element.Information);
                    Console.WriteLine("Number of element extensions: {0}{1}", element.Certificate.Extensions.Count, Environment.NewLine);

                    if (ch.ChainStatus.Length > 1)
                    {
                        for (int index = 0; index < element.ChainElementStatus.Length; index++)
                        {
                            Console.WriteLine(element.ChainElementStatus[index].Status);
                            Console.WriteLine(element.ChainElementStatus[index].StatusInformation);
                        }
                    }
                }

                var ret = certificate.Verify();
                Console.WriteLine("ServerCertificateVerify:" + ret);
                return ret;
            }
            catch (System.Exception ex)
            {
                Console.WriteLine(ex);
                return false;
            }
        }


        public static X509Certificate2 GetX509Certificate2(string X509FileName, string X509Password
#if NETSTANDARD2_1_OR_GREATER || NET5_0_OR_GREATER
            , string CertificateFileName = null, string PrivateKeyPath = null
#endif
            )
        {
            X509Certificate2 ServerCertificate = null;
            if (!string.IsNullOrWhiteSpace(X509FileName) && System.IO.File.Exists(X509FileName))
            {
                ServerCertificate = new X509Certificate2(X509FileName, X509Password);


            }
#if NETSTANDARD2_1_OR_GREATER || NET5_0_OR_GREATER
            else if (!string.IsNullOrWhiteSpace(CertificateFileName) && System.IO.File.Exists(CertificateFileName)
                && !string.IsNullOrWhiteSpace(PrivateKeyPath) && System.IO.File.Exists(PrivateKeyPath))
            {
                ServerCertificate = SQ.Base.Certificate.CertificateHelper.LoadPemCertificate(CertificateFileName, PrivateKeyPath);
            }
#endif

            if (ServerCertificate != null)
            {
                SQ.Base.Certificate.CertificateHelper.Verify(ServerCertificate);

            }
            return ServerCertificate;
        }
    }
}

