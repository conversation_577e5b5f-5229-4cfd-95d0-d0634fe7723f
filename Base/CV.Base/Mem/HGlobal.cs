﻿using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;

namespace SQ.Base.Mem
{
    public class HGlobal : IDisposable
    {
        public IntPtr Data { get; private set; }
        private bool disposedValue;
        public HGlobal(int cb)
        {
            Data = Marshal.AllocHGlobal(cb);
        }

        public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                disposedValue = true;
                if (disposing)
                {
                    // TODO: 释放托管状态(托管对象)
                }
                Marshal.FreeHGlobal(Data);
                // TODO: 释放未托管的资源(未托管的对象)并重写终结器
                // TODO: 将大型字段设置为 null
            }
        }
        ~HGlobal()
        {
            Dispose();
        }
    }
}
