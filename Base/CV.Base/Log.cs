﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using log4net;
using log4net.Repository;
using log4net.Config;

namespace SQ.Base
{
    public enum LOGTYPE
    {
        DEBUG,
        INFO,
        WARN,
        ERRORD,
        FETAL
    }

    public class Log
    {
        public delegate void LogMsg(string msg, LOGTYPE LogType);
        public static event LogMsg LogChange;
        public static log4net.ILog Log4;
        private static object lckobj = new object();

        static Log()
        {
            ILoggerRepository repository = LogManager.CreateRepository("NETCoreRepository");
            XmlConfigurator.Configure(repository, new FileInfo(FileHelp.GetMyConfPath() + "log4.config"));
            Log4 = log4net.LogManager.GetLogger(repository.Name, "SQ");
        }
        #region 错误日志处理
        /// <summary>
        /// 错误日志写入
        /// </summary>
        /// <returns></returns>
        public static void ErrorLogWirte(System.Exception ex, string path)
        {
            lock (lckobj)
            {
                var pth = Path.GetDirectoryName(path);
                if (!Directory.Exists(pth))
                {
                    path = Directory.CreateDirectory(pth).FullName;
                }
                var strFile = path;
                if (!File.Exists(strFile))
                {
                    using (var fl = File.Create(strFile))
                    {
                        fl.Close();
                    }
                }
                using (System.IO.TextWriter write = System.IO.File.AppendText(strFile))
                {
                    write.WriteLine("\r\n-------------------------------------------------------------------------------------------------");
                    write.WriteLine("时间：" + DateTime.Now.ToString());
                    //write.WriteLine("系统版本号：" + Application.ProductVersion);
                    write.WriteLine("错误信息：" + ex.Message);
                    write.WriteLine("详细信息： \r\n" + ex.StackTrace);
                    write.WriteLine();
                    write.Close();
                }
            }
        }
        /// <summary>
        /// 错误日志写入
        /// </summary>
        /// <returns></returns>
        public static void DebugWrite(string content, string path)
        {
            lock (lckobj)
            {
                var pth = Path.GetDirectoryName(path);
                if (!Directory.Exists(pth))
                {
                    path = Directory.CreateDirectory(pth).FullName;
                }
                var strFile = path;
                if (!File.Exists(strFile))
                {
                    using (var fl = File.Create(strFile))
                    {
                        fl.Close();
                    }
                }
                using (System.IO.TextWriter write = System.IO.File.AppendText(strFile))
                {
                    write.WriteLine($"{content}");
                    write.Close();
                }
            }
        }
        #endregion


        #region "写日志"
        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="LogContent"></param>
        /// <param name="buff"></param>
        /// <param name="LogType"></param>
        public static void WriteLog4(string LogContent, byte[] buff, LOGTYPE LogType)
        {
            if (buff != null && buff.Length > 0)
            {
                WriteLog4(LogContent, buff, 0, buff.Length, LogType);
            }
        }
        public static void WriteLog4(StringBuilder LogContent, byte[] buff, LOGTYPE LogType, bool AddSpace = false)
        {
            WriteLog4(LogContent, buff, 0, buff.Length, LogType, AddSpace);
        }
        public static void WriteLog4(string LogContent, byte[] buff, int offset, int count, LOGTYPE LogType)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(LogContent);
            WriteLog4(sb, buff, 0, count, LogType);
        }
        public static void WriteLog4(StringBuilder LogContent, byte[] buff, int offset, int count, LOGTYPE LogType, bool AddSpace = false)
        {
            WriteLog4(ByteHelper.BytesToHexString(LogContent, buff, offset, count, AddSpace).ToString(), LogType);
        }

        internal static void WriteLog4(ILog logger, string content, LOGTYPE type)
        {
            switch (type)
            {
                case LOGTYPE.DEBUG:
                    logger.Debug(content);
                    break;
                case LOGTYPE.INFO:
                    logger.Info(content);
                    break;
                case LOGTYPE.WARN:
                    logger.Warn(content);
                    break;
                case LOGTYPE.ERRORD:
                    logger.Error(content);
                    break;
                case LOGTYPE.FETAL:
                    logger.Fatal(content);
                    break;
            }
        }
        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="LogContent"></param>
        /// <param name="LogType"></param>
        public static void WriteLog4(string LogContent, LOGTYPE LogType = LOGTYPE.DEBUG)
        {
            switch (LogType)
            {
                case LOGTYPE.DEBUG:
                    Log4.Debug(LogContent);
                    break;
                case LOGTYPE.INFO:
                    Log4.Info(LogContent);
                    break;
                case LOGTYPE.WARN:
                    Log4.Warn(LogContent);
                    break;
                case LOGTYPE.ERRORD:
                    Log4.Error(LogContent);
                    break;
                case LOGTYPE.FETAL:
                    Log4.Fatal(LogContent);
                    break;
            }
            if (LogChange != null)
            {
                LogChange(LogContent, LogType);
            }
        }

        public static void WriteLog4Ex(string LogContent, System.Exception ex)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(LogContent);
            sb.Append(':');
            sb.Append(ex.ToString());
            WriteLog4(sb.ToString(), LOGTYPE.ERRORD);
        }
        ////记录文本日至
        //public static void WriteMemo(string Message, string FileName)
        //{
        //    Int16 FileID = default(Int16);
        //    try
        //    {
        //        FileID = FileSystem.FreeFile();
        //        string wFile = Application.StartupPath + "\\Logfile\\" + FileName + ".dat";
        //        if (!string.IsNullOrEmpty(FileSystem.Dir(wFile)))
        //        {
        //            if (FileSystem.FileLen(wFile) > 1000000)
        //            {
        //                string fNM = FileName + DateAndTime.Now.ToFileTime();
        //                FileSystem.Rename(wFile, Application.StartupPath + "\\Logfile\\" + fNM + ".dat");
        //            }
        //        }
        //        FileSystem.FileOpen(FileID, wFile, OpenMode.Append, OpenAccess.Write, OpenShare.Default);
        //        FileSystem.WriteLine(FileID, string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateAndTime.Now) + ":" + Message);
        //        FileSystem.FileClose(FileID);
        //    }
        //    catch (Exception ex)
        //    {
        //        Debug.WriteLine(ex.ToString());
        //    }
        //}

        //public static void WriteHtmlLog(string Message, Int16 logType)
        //{
        //    string wFile = null;
        //    string NFile = null;
        //    try
        //    {
        //        wFile = Application.StartupPath + "/LogFile/log.htm";
        //        System.IO.FileInfo fInfo = new System.IO.FileInfo(wFile);
        //        if (fInfo.Exists() == false)
        //        {
        //            WriteHtmlBlank();
        //        }
        //        else if (fInfo.Length > 100000)
        //        {
        //            NFile = Application.StartupPath + "\\Logfile\\" + "log" + DateAndTime.Now.ToFileTime() + ".htm";
        //            FileSystem.Rename(wFile, NFile);
        //            WriteHtmlBlank();
        //        }
        //        System.Xml.XmlDocument Fxml = new System.Xml.XmlDocument();
        //        Fxml.Load(Application.StartupPath + "/LogFile/log.htm");
        //        System.Xml.XmlElement xElement = null;
        //        System.Xml.XmlElement xElement2 = null;
        //        System.Xml.XmlElement xElement3 = null;

        //        System.Xml.XmlNode xNode = Fxml.SelectSingleNode("html/body/table");
        //        xElement = Fxml.CreateElement("tr");
        //        xElement2 = Fxml.CreateElement("td");
        //        xElement3 = Fxml.CreateElement("td");
        //        xElement2.InnerText = DateAndTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        //        xElement3.InnerText = Message;
        //        xElement.AppendChild(xElement2);
        //        xElement.AppendChild(xElement3);
        //        xNode.InsertBefore(xElement, xNode.FirstChild);
        //        //OK
        //        //xNode.AppendChild(xElement) 'OK
        //        Fxml.Save(Application.StartupPath + "/LogFile/log.htm");
        //    }
        //    catch (Exception ex)
        //    {
        //        Debug.WriteLine(ex.ToString());
        //    }

        //}
        //private static void WriteHtmlBlank()
        //{
        //    System.IO.StreamWriter StrWrite = null;
        //    string wFile = null;
        //    wFile = Application.StartupPath + "/LogFile/log.htm";
        //    StrWrite = new System.IO.StreamWriter(wFile, true, System.Text.Encoding.Default);
        //    StrWrite.WriteLine("<?xml version=\"1.0\" encoding=\"gb2312\"?>");
        //    StrWrite.WriteLine("<html><head>");
        //    StrWrite.WriteLine("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=gb2312\" />");
        //    StrWrite.WriteLine("<style type=\"text/css\">table{border-top:1px;border-collapse:collapse;}td{border-left:0;border-top:1;border-bottom:1px solid #cb1122;border-top:1px solid #cb1122;border-right:1px solid #00cd11;");
        //    StrWrite.WriteLine("font-size:11px;height:25px;padding:0 12px 0 12px;border-collapse:collapse;}</style>");
        //    StrWrite.WriteLine("</head><body><p>系统运行日志</p><hr /><br/>");
        //    StrWrite.WriteLine("<table border=\"1\" width=\"100%\" ><tr><td style=\"nowrap\"></td><td></td></tr></table></body></html>");
        //    StrWrite.Close();
        //}
        #endregion

    }

    public class ErrorLog
    {
        private static ILog errorLog;
        static ErrorLog()
        {
            ILoggerRepository repository = LogManager.CreateRepository("NETCoreRepository2");
            XmlConfigurator.Configure(repository, new FileInfo(FileHelp.GetMyConfPath() + "log4.config"));
            errorLog = log4net.LogManager.GetLogger(repository.Name, "ErrLog");
        }
        public static void WriteLog4(string content, LOGTYPE logType)
        {
            Log.WriteLog4(errorLog, content, logType);
        }
        public static void WriteLog4(StringBuilder content, byte[] buffer, LOGTYPE logType)
        {
            var logcontent = ByteHelper.BytesToHexString(content, buffer, 0, buffer.Length, false).ToString();
            Log.WriteLog4(errorLog, logcontent, logType);
        }
        public static void WriteLog4Ex(string content, System.Exception ex)
        {
            string errorContent = string.Format("{0}:{1}", content, ex);
            Log.WriteLog4(errorLog, errorContent, LOGTYPE.ERRORD);
        }
    }

    //public class DebugLog
    //{
    //    public static log4net.ILog Log4 = log4net.LogManager.GetLogger("DebugLog");
    //    /// <summary>
    //    /// 写入日志
    //    /// </summary>
    //    /// <param name="LogContent"></param>
    //    /// <param name="LogType"></param>
    //    public static void WriteLog4(string LogContent, LOGTYPE LogType)
    //    {
    //        switch (LogType)
    //        {
    //            case LOGTYPE.DEBUG:
    //                Log4.Debug(LogContent);
    //                break;
    //            case LOGTYPE.INFO:
    //                Log4.Info(LogContent);
    //                break;
    //            case LOGTYPE.WARN:
    //                Log4.Warn(LogContent);
    //                break;
    //            case LOGTYPE.ERRORD:
    //                Log4.Error(LogContent);
    //                break;
    //            case LOGTYPE.FETAL:
    //                Log4.Fatal(LogContent);
    //                break;
    //        }
    //    }

    //    public static void WriteLog4(string LogContent)
    //    {
    //        WriteLog4(LogContent, LOGTYPE.DEBUG);
    //    }
    //}
    public class LogMonitor
    {
        public LogMonitor(string Path, bool OutOld)
        {
            this.Path = Path;
            if (!OutOld)
            {
                var f = new FileInfo(Path);
                if (f.Exists)
                {
                    LastWriteTime = f.LastWriteTime;
                    Length = f.Length;
                }
            }
        }

        string Path;
        long Length;
        DateTime LastWriteTime;

        public string GetNewLog()
        {
            var f = new FileInfo(Path);
            if (f.Exists)
            {
                if (LastWriteTime != f.LastWriteTime)// 改变时才处理
                {
                    using (var read = f.Open(FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                    {
                        if (read.Length > Length)//追加数据时从追加位置读取
                        {
                            read.Position = Length;
                        }
                        using (StreamReader sr = new StreamReader(read, Encoding.Default))
                        {
                            LastWriteTime = f.LastWriteTime;
                            Length = read.Length;
                            return sr.ReadToEnd();
                        }
                    }
                }
            }
            return null;
        }
    }

    //public class AVLog
    //{
    //    private static ILog errorLog = LogManager.GetLogger("AVInfo");
    //    public static void WriteLog4(string content, LOGTYPE logType = LOGTYPE.INFO)
    //    {
    //        Log.WriteLog4(errorLog, content, logType);
    //    }
    //    public static void WriteLog4(StringBuilder content, byte[] buffer, LOGTYPE logType)
    //    {
    //        var logcontent = ByteHelper.BytesToHexString(content, buffer, 0, buffer.Length, false).ToString();
    //        Log.WriteLog4(errorLog, logcontent, logType);
    //    }
    //    public static void WriteLog4Ex(string content, System.Exception ex)
    //    {
    //        string errorContent = string.Format("{0}:{1}", content, ex);
    //        Log.WriteLog4(errorLog, errorContent, LOGTYPE.ERRORD);
    //    }
    //}

}