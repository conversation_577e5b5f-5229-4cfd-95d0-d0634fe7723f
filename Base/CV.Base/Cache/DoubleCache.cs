﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace SQ.Base.Cache
{

    public abstract class DoubleCache<T>
    {
        #region  双缓存机制
        //也可使用环形buff机制
        private ListAndLockSlim<T> lst1 = new ListAndLockSlim<T>();
        private ListAndLockSlim<T> lst2 = new ListAndLockSlim<T>();
        private bool flag = true;



        public int Count
        {
            get
            {
                return lst1.Count + lst2.Count;
            }
        }
        /// <summary>
        /// 获取当前生效队列
        /// </summary>
        /// <returns></returns>
        protected ListAndLockSlim<T> Getlist()
        {
            if (flag)
            {
                return lst1;
            }
            else
            {
                return lst2;
            }
        }
        public virtual ListAndLockSlim<T> GetChange(CancellationToken cancellationToken)
        {
            var lst = Getlist();//获取当前有效队列
            flag = !flag;//翻转有效队列
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                lst.lockSlim.EnterUpgradeableReadLock();
                return lst;
            }
            finally
            {
                if (lst.lockSlim.IsUpgradeableReadLockHeld)
                {
                    lst.lockSlim.ExitUpgradeableReadLock();
                }
            }
            //lock (lst)//防止出现正在操作过程中的问题
            //{
            //}
        }
        #endregion

        //#region 保存近X条记录
        //public List<T> LastData = new List<T>();
        //#endregion

        /// <summary>
        /// 添加到数据源
        /// </summary>
        /// <param name="item"></param>
        public void Add(T item)
        {
            var lst = Getlist();
            try
            {
                lst.lockSlim.EnterReadLock();
                lst.Add(item);
            }
            finally
            {
                lst.lockSlim.ExitReadLock();
            }
            OnAdd(item);
        }
        /// <summary>
        /// 添加后执行方法
        /// </summary>
        /// <param name="item"></param>
        protected virtual void OnAdd(T item)
        {

        }

        /// <summary>
        /// 开始执行 切换数据源并执行DoSomething方法
        /// </summary>
        protected virtual void BeginSwitchAndDoSomething()
        {

        }
        /// <summary>
        /// 结束执行 切换数据源并执行DoSomething方法
        /// </summary>
        protected virtual void EndSwitchAndDoSomething()
        {

        }

        /// <summary>
        /// 切换数据源并执行DoSomething方法
        /// </summary>
        public void SwitchAndDoSomething(CancellationToken cancellationToken)
        {
            BeginSwitchAndDoSomething();
            cancellationToken.ThrowIfCancellationRequested();
            var lst = GetChange(cancellationToken);
            //Thread.Sleep(100);//暂停100ms，防止翻转时有在等待插入
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                lst.lockSlim.EnterUpgradeableReadLock();
                cancellationToken.ThrowIfCancellationRequested();
                lst.lockSlim.EnterWriteLock();
                DoSomething(lst, cancellationToken);
            }
            catch (System.Exception ex)
            {
                ErrorLog.WriteLog4Ex("DoubleCache::SwitchAndDoSomething", ex);
            }
            finally
            {
                if (lst.lockSlim.IsWriteLockHeld)
                {
                    lst.lockSlim.ExitWriteLock();
                }
                if (lst.lockSlim.IsUpgradeableReadLockHeld)
                {
                    lst.lockSlim.ExitUpgradeableReadLock();
                }
                EndSwitchAndDoSomething();
            }
        }
        /// <summary>
        /// 
        /// </summary>
        protected abstract void DoSomething(List<T> lst, CancellationToken cancellationToken);
    }
    /// <summary>
    /// 用于DoubleCacheRun
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public abstract class KeyDoubleCache<T> : DoubleCache<T>
    {
        public string Key { get { return GetKey(); } }
        public abstract string GetKey();
        /// <summary>
        /// 指示是否应在添加时候加入到执行线程
        /// </summary>
        protected bool CanAdd2Run = true;
        /// <summary>
        /// 执行线程
        /// </summary>
        public DoubleCacheRunItem<T> RunItem { get; set; }
        protected override void OnAdd(T item)
        {
            if (CanAdd2Run)
            {
                CanAdd2Run = false;
                RunItem.Add2Run(this);
            }
        }
        protected override void EndSwitchAndDoSomething()
        {
            //数量大于0自动触发下一次执行
            if (this.Count > 0)
            {
                RunItem.Add2Run(this);
            }
            else
            {
                //数量等于0等待下一次添加触发
                CanAdd2Run = true;
            }
        }
    }
    public class ListAndLockSlim<T> : List<T>, IDisposable
    {
        public ReaderWriterLockSlim lockSlim = new ReaderWriterLockSlim(LockRecursionPolicy.NoRecursion);
        private bool disposedValue;

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: 释放托管状态(托管对象)
                }
                lockSlim.Dispose();
                // TODO: 释放未托管的资源(未托管的对象)并重写终结器
                // TODO: 将大型字段设置为 null
                disposedValue = true;
            }
        }

        // TODO: 仅当“Dispose(bool disposing)”拥有用于释放未托管资源的代码时才替代终结器
        ~ListAndLockSlim()
        {
            // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
            Dispose(disposing: false);
        }

        public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入“Dispose(bool disposing)”方法中
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
    }
}