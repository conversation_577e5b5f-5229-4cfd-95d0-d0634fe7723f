﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using SQ.Base.Queue;

namespace SQ.Base.Cache
{





    public class DoubleCacheRun<T>
    {
        protected int ThCount;
        /// <summary>
        /// 执行状态
        /// </summary>
        public bool IsRun { get; private set; }
        private int index;

        public DoubleCacheRun(int ThCount)
        {
            this.ThCount = ThCount;
        }

        /// <summary>
        /// 执行线程
        /// </summary>
        public DoubleCacheRunItem<T>[] ths { get; protected set; }
        public string SchedulerName { get; set; }

        public void Start()
        {
            IsRun = true;
            ths = new DoubleCacheRunItem<T>[ThCount];
            index = 0;
            for (int i = 0; i < ThCount; i++)
            {
                ths[i] = new DoubleCacheRunItem<T>();
                ths[i].Start(SchedulerName + "_" + i);
            }

        }
        public void Clear()
        {
            for (int i = 0; i < ThCount; i++)
            {
                ths[i].sch.Clear();
            }
        }
        public void Dispose()
        {
            try
            {
                lock (this)
                {
                    if (IsRun)
                        Stop();
                    //if (!mDispose)
                    //{
                    //    mDispose = true;
                    //}
                }

            }
            catch (System.Exception ex)
            {
                ErrorLog.WriteLog4Ex("Dispose", ex);
            }
        }

        public void Stop()
        {
            try
            {
                Clear();
                for (int i = 0; i < ths.Length; i++)
                {
                    try
                    {
                        ths[i].Stop();
                    }
                    catch
                    {
                        // ignored
                    }
                };
            }
            finally
            {
                IsRun = false;
            }
        }

        public void SetRunItem(KeyDoubleCache<T> cache)
        {
            cache.RunItem = ths[index];
            index++;
            if (index >= ths.Length)
            {
                index = 0;
            }
        }
    }

    /// <summary>
    /// DoubleCache实际执行类
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class DoubleCacheRunItem<T>
    {
        /// <summary>
        /// 执行任务调度器 
        /// 调度器只能为1个线程 否则又需要解决线程同步问题
        /// </summary>
        public SQ.Base.Queue.Scheduler<int> sch { get; protected set; }

        public void Start(string Name)
        {
            sch = new Base.Queue.Scheduler<int>(1);
            sch.SchedulerName = Name;
            sch.Start();
        }
        public void Stop()
        {
            if (sch != null)
            {
                sch.StopSafe();
            }
        }
        /// <summary>
        /// 添加KeyDoubleCache到执行
        /// </summary>
        /// <param name="cache"></param>
        public void Add2Run(KeyDoubleCache<T> cache)
        {
            sch.Add(new DoubleCacheRunWork<T>(cache));
        }
    }
    public class DoubleCacheRunWork<T> : SQ.Base.Queue.IWorkItem<int>
    {
        private KeyDoubleCache<T> cache;

        public DoubleCacheRunWork(KeyDoubleCache<T> cache)
        {
            this.cache = cache;
        }

        public void Dispose()
        {
        }


        public void Execute(int tag, CancellationToken cancellationToken)
        {
            try
            {
                cache.SwitchAndDoSomething(cancellationToken);
            }
            catch (System.Exception ex)
            {
                ErrorLog.WriteLog4Ex("DoubleCacheRun::Run", ex);
            }
            finally
            {
            }
        }
    }
}
