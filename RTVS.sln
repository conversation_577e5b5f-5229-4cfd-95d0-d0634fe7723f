
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32811.315
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JT1078Server", "Video\JT1078Server\JT1078Server.csproj", "{25D54F29-7411-41D1-AA81-CD8EB4EF1570}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JX", "Video\JX\JX.csproj", "{432D3418-658C-4C15-9B0D-25E97608F9A5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CV.Video.Base", "Video\CV.Video.Base\CV.Video.Base.csproj", "{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RTVSWeb", "Video\RTVSWeb\RTVSWeb.csproj", "{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WASM", "WASM", "{E0DA0372-79D8-424B-BAFF-C002262F902D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Component", "Component", "{FB461C66-6ABE-4E61-9684-42E9C0CAFF71}"
EndProject
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "tstWeb", "Wasm\tstWeb\", "{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}"
	ProjectSection(WebsiteProperties) = preProject
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.0"
		Debug.AspNetCompiler.VirtualPath = "/localhost_52514"
		Debug.AspNetCompiler.PhysicalPath = "Wasm\tstWeb\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52514\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_52514"
		Release.AspNetCompiler.PhysicalPath = "Wasm\tstWeb\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52514\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "52514"
		DefaultWebSiteLanguage = "Visual C#"
	EndProjectSection
	ProjectSection(WebsiteProperties) = preProject
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.0"
		Debug.AspNetCompiler.VirtualPath = "/localhost_52514"
		Debug.AspNetCompiler.PhysicalPath = "Wasm\tstWeb\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52514\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_52514"
		Release.AspNetCompiler.PhysicalPath = "Wasm\tstWeb\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52514\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "52514"
		DefaultWebSiteLanguage = "Visual C#"
	EndProjectSection
	ProjectSection(WebsiteProperties) = preProject
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.0"
		Debug.AspNetCompiler.VirtualPath = "/localhost_52514"
		Debug.AspNetCompiler.PhysicalPath = "Wasm\tstWeb\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52514\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_52514"
		Release.AspNetCompiler.PhysicalPath = "Wasm\tstWeb\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52514\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "52514"
		DefaultWebSiteLanguage = "Visual C#"
	EndProjectSection
	ProjectSection(WebsiteProperties) = preProject
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.0"
		Debug.AspNetCompiler.VirtualPath = "/localhost_52514"
		Debug.AspNetCompiler.PhysicalPath = "Wasm\tstWeb\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52514\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_52514"
		Release.AspNetCompiler.PhysicalPath = "Wasm\tstWeb\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52514\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "52514"
		SlnRelativePath = "Wasm\tstWeb\"
		DefaultWebSiteLanguage = "Visual C#"
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CV.Base", "Base\CV.Base\CV.Base.csproj", "{A7BC437A-ABAA-46A0-9B16-B886505D9037}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CV.Network", "Base\CV.Network\CV.Network.csproj", "{27970161-E05A-4A20-ADCE-8E3E00F65210}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Base", "Base", "{41634D91-429B-43F4-AA62-00D49FDD8FA9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RedisHelp", "Base\RedisHelp\RedisHelp.csproj", "{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SQ.DAL.MySql", "Base\SQ.DAL.MySql\SQ.DAL.MySql.csproj", "{1DA26F01-6AB4-4528-A570-F1ACAD401026}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "InfluxBD", "Video\InfluxBD\InfluxBD.csproj", "{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CVCluster", "Cluster\CVCluster\CVCluster.csproj", "{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VdtFileTranscodeAndUploadService", "Video\VdtFileTranscodeAndUploadService\VdtFileTranscodeAndUploadService.csproj", "{21B9629B-5E8F-4D70-9A81-691F904C8D51}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Record", "Record", "{0EBEFAAD-9593-4098-8F24-55E51B6AD5D9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RecordLib", "Record\RecordLib\RecordLib.csproj", "{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RTVSRecord", "Record\RTVSRecord\RTVSRecord.csproj", "{6FDC0C8B-6D43-4C45-B438-B6724854F324}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Van.DAL.DM", "Base\Van.DAL.DM\Van.DAL.DM.csproj", "{313A3B22-709D-420F-8553-2986FABC1CF2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FileCache", "Video\FileCache\FileCache.csproj", "{12249B59-45A4-4983-97E6-4DD2B72DFEBE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Server", "Server", "{4B6FB51E-76DD-4221-9BB9-5A5CFAFFD520}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Cluster", "Cluster", "{********-AAF0-4EB6-BF41-2810EC04F918}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Debug|x64.ActiveCfg = Debug|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Debug|x64.Build.0 = Debug|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Debug|x86.ActiveCfg = Debug|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Debug|x86.Build.0 = Debug|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Release|Any CPU.Build.0 = Release|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Release|x64.ActiveCfg = Release|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Release|x64.Build.0 = Release|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Release|x86.ActiveCfg = Release|Any CPU
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570}.Release|x86.Build.0 = Release|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Debug|x64.Build.0 = Debug|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Debug|x86.Build.0 = Debug|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Release|x64.ActiveCfg = Release|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Release|x64.Build.0 = Release|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Release|x86.ActiveCfg = Release|Any CPU
		{432D3418-658C-4C15-9B0D-25E97608F9A5}.Release|x86.Build.0 = Release|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Debug|x64.Build.0 = Debug|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Debug|x86.Build.0 = Debug|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Release|x64.ActiveCfg = Release|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Release|x64.Build.0 = Release|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Release|x86.ActiveCfg = Release|Any CPU
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3}.Release|x86.Build.0 = Release|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Debug|x64.ActiveCfg = Debug|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Debug|x64.Build.0 = Debug|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Debug|x86.ActiveCfg = Debug|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Debug|x86.Build.0 = Debug|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Release|x64.ActiveCfg = Release|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Release|x64.Build.0 = Release|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Release|x86.ActiveCfg = Release|Any CPU
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1}.Release|x86.Build.0 = Release|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Debug|x64.Build.0 = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Debug|x86.Build.0 = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Release|Any CPU.ActiveCfg = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Release|Any CPU.Build.0 = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Release|x64.ActiveCfg = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Release|x64.Build.0 = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Release|x86.ActiveCfg = Debug|Any CPU
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA}.Release|x86.Build.0 = Debug|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Debug|x64.Build.0 = Debug|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Debug|x86.Build.0 = Debug|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Release|x64.ActiveCfg = Release|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Release|x64.Build.0 = Release|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Release|x86.ActiveCfg = Release|Any CPU
		{A7BC437A-ABAA-46A0-9B16-B886505D9037}.Release|x86.Build.0 = Release|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Debug|x64.ActiveCfg = Debug|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Debug|x64.Build.0 = Debug|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Debug|x86.ActiveCfg = Debug|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Debug|x86.Build.0 = Debug|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Release|Any CPU.Build.0 = Release|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Release|x64.ActiveCfg = Release|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Release|x64.Build.0 = Release|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Release|x86.ActiveCfg = Release|Any CPU
		{27970161-E05A-4A20-ADCE-8E3E00F65210}.Release|x86.Build.0 = Release|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Debug|x64.Build.0 = Debug|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Debug|x86.Build.0 = Debug|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Release|Any CPU.Build.0 = Release|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Release|x64.ActiveCfg = Release|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Release|x64.Build.0 = Release|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Release|x86.ActiveCfg = Release|Any CPU
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037}.Release|x86.Build.0 = Release|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Debug|x64.Build.0 = Debug|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Debug|x86.Build.0 = Debug|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Release|Any CPU.Build.0 = Release|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Release|x64.ActiveCfg = Release|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Release|x64.Build.0 = Release|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Release|x86.ActiveCfg = Release|Any CPU
		{1DA26F01-6AB4-4528-A570-F1ACAD401026}.Release|x86.Build.0 = Release|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Debug|x64.Build.0 = Debug|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Debug|x86.Build.0 = Debug|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Release|x64.ActiveCfg = Release|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Release|x64.Build.0 = Release|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Release|x86.ActiveCfg = Release|Any CPU
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA}.Release|x86.Build.0 = Release|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Debug|x64.Build.0 = Debug|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Debug|x86.Build.0 = Debug|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Release|Any CPU.Build.0 = Release|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Release|x64.ActiveCfg = Release|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Release|x64.Build.0 = Release|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Release|x86.ActiveCfg = Release|Any CPU
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7}.Release|x86.Build.0 = Release|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Debug|x64.ActiveCfg = Debug|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Debug|x64.Build.0 = Debug|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Debug|x86.ActiveCfg = Debug|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Debug|x86.Build.0 = Debug|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Release|Any CPU.Build.0 = Release|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Release|x64.ActiveCfg = Release|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Release|x64.Build.0 = Release|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Release|x86.ActiveCfg = Release|Any CPU
		{21B9629B-5E8F-4D70-9A81-691F904C8D51}.Release|x86.Build.0 = Release|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Debug|x64.Build.0 = Debug|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Debug|x86.Build.0 = Debug|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Release|Any CPU.Build.0 = Release|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Release|x64.ActiveCfg = Release|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Release|x64.Build.0 = Release|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Release|x86.ActiveCfg = Release|Any CPU
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD}.Release|x86.Build.0 = Release|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Debug|x64.Build.0 = Debug|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Debug|x86.Build.0 = Debug|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Release|Any CPU.Build.0 = Release|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Release|x64.ActiveCfg = Release|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Release|x64.Build.0 = Release|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Release|x86.ActiveCfg = Release|Any CPU
		{6FDC0C8B-6D43-4C45-B438-B6724854F324}.Release|x86.Build.0 = Release|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Debug|x64.Build.0 = Debug|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Debug|x86.Build.0 = Debug|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Release|x64.ActiveCfg = Release|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Release|x64.Build.0 = Release|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Release|x86.ActiveCfg = Release|Any CPU
		{313A3B22-709D-420F-8553-2986FABC1CF2}.Release|x86.Build.0 = Release|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Debug|x64.Build.0 = Debug|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Debug|x86.Build.0 = Debug|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Release|Any CPU.Build.0 = Release|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Release|x64.ActiveCfg = Release|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Release|x64.Build.0 = Release|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Release|x86.ActiveCfg = Release|Any CPU
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{25D54F29-7411-41D1-AA81-CD8EB4EF1570} = {4B6FB51E-76DD-4221-9BB9-5A5CFAFFD520}
		{432D3418-658C-4C15-9B0D-25E97608F9A5} = {FB461C66-6ABE-4E61-9684-42E9C0CAFF71}
		{853892EE-B1BF-43EA-BC9D-F17DEC1626E3} = {4B6FB51E-76DD-4221-9BB9-5A5CFAFFD520}
		{828BF73E-E30F-49E6-B4A0-9B38DD5976B1} = {4B6FB51E-76DD-4221-9BB9-5A5CFAFFD520}
		{37EC497B-7B9E-4CD7-AA05-68EC0675F1EA} = {E0DA0372-79D8-424B-BAFF-C002262F902D}
		{A7BC437A-ABAA-46A0-9B16-B886505D9037} = {41634D91-429B-43F4-AA62-00D49FDD8FA9}
		{27970161-E05A-4A20-ADCE-8E3E00F65210} = {41634D91-429B-43F4-AA62-00D49FDD8FA9}
		{CE47DE8F-3D1A-4E3D-B029-68097D9BC037} = {41634D91-429B-43F4-AA62-00D49FDD8FA9}
		{1DA26F01-6AB4-4528-A570-F1ACAD401026} = {41634D91-429B-43F4-AA62-00D49FDD8FA9}
		{F0AC8D8C-0C21-48AB-B8FE-481C18B63AEA} = {FB461C66-6ABE-4E61-9684-42E9C0CAFF71}
		{4DE4E93B-4C32-426C-9D51-8D1A5530BBB7} = {********-AAF0-4EB6-BF41-2810EC04F918}
		{21B9629B-5E8F-4D70-9A81-691F904C8D51} = {FB461C66-6ABE-4E61-9684-42E9C0CAFF71}
		{EDDE5831-2884-41C4-87F8-A0CBC588EDAD} = {0EBEFAAD-9593-4098-8F24-55E51B6AD5D9}
		{6FDC0C8B-6D43-4C45-B438-B6724854F324} = {0EBEFAAD-9593-4098-8F24-55E51B6AD5D9}
		{313A3B22-709D-420F-8553-2986FABC1CF2} = {41634D91-429B-43F4-AA62-00D49FDD8FA9}
		{12249B59-45A4-4983-97E6-4DD2B72DFEBE} = {FB461C66-6ABE-4E61-9684-42E9C0CAFF71}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {EAADD5ED-D65B-4829-ADB4-253FFCA8588D}
	EndGlobalSection
EndGlobal
