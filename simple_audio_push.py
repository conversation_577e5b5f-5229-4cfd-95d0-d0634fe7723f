#!/usr/bin/env python3
"""
简化的语音推流工具
专注于语音对讲功能
"""

import asyncio
import httpx
import os
from pathlib import Path

# 配置信息
API_BASE_URL = "http://127.0.0.1:8000"
DEFAULT_SIM = "13800138000"
DEFAULT_CHANNEL = 1
DEFAULT_DEVICE_ID = "13896118973"

async def push_audio_to_device(audio_file_path: str, sim: str = None, channel: int = None, device_id: str = None):
    """
    推送音频文件到设备
    
    Args:
        audio_file_path: 音频文件路径
        sim: 设备SIM卡号
        channel: 通道号
        device_id: 设备ID
    """
    
    # 使用默认值
    sim = sim or DEFAULT_SIM
    channel = channel or DEFAULT_CHANNEL
    device_id = device_id or DEFAULT_DEVICE_ID
    
    # 检查文件
    if not os.path.exists(audio_file_path):
        print(f"❌ 文件不存在: {audio_file_path}")
        return False
    
    file_size = os.path.getsize(audio_file_path) / 1024 / 1024  # MB
    print(f"🎵 准备推流音频文件: {os.path.basename(audio_file_path)} ({file_size:.1f} MB)")
    print(f"📱 目标设备: SIM={sim}, 通道={channel}, 设备ID={device_id}")
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            # 1. 健康检查
            print("🔍 检查API服务...")
            health_response = await client.get(f"{API_BASE_URL}/health")
            if health_response.status_code != 200:
                print("❌ API服务不可用")
                return False
            print("✅ API服务正常")
            
            # 2. 开始语音对讲
            print("🎤 开始语音对讲...")
            
            with open(audio_file_path, "rb") as audio_file:
                files = {
                    "audio_file": (
                        os.path.basename(audio_file_path),
                        audio_file,
                        "audio/mpeg"
                    )
                }
                data = {
                    "sim": sim,
                    "channel": channel,
                    "device_id": device_id
                }
                
                response = await client.post(
                    f"{API_BASE_URL}/api/audio/talk",
                    files=files,
                    data=data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 语音对讲成功: {result.get('message', '成功')}")
                    
                    server_info = result.get('server_info', {})
                    if server_info:
                        print(f"🔗 服务器: {server_info.get('host')}:{server_info.get('port')}")
                    
                    return True
                else:
                    print(f"❌ 语音对讲失败: HTTP {response.status_code}")
                    try:
                        error_detail = response.json()
                        print(f"   错误详情: {error_detail.get('detail', '未知错误')}")
                    except:
                        print(f"   响应内容: {response.text}")
                    return False
                    
    except httpx.TimeoutException:
        print("❌ 请求超时")
        return False
    except httpx.ConnectError:
        print("❌ 连接失败，请确保API服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 推流过程中发生错误: {e}")
        return False

def find_audio_files():
    """查找可用的音频文件"""
    audio_files = []
    
    # 查找当前目录
    current_dir = Path(".")
    for pattern in ["*.mp3", "*.wav", "*.m4a"]:
        audio_files.extend(list(current_dir.glob(pattern)))
    
    # 查找rtvs-python目录
    rtvs_python_dir = Path("rtvs-python")
    if rtvs_python_dir.exists():
        for pattern in ["*.mp3", "*.wav", "*.m4a"]:
            audio_files.extend(list(rtvs_python_dir.glob(pattern)))
    
    return audio_files

async def interactive_push():
    """交互式推流"""
    print("🎵 RTVS 语音对讲工具")
    print("=" * 40)
    
    # 查找音频文件
    audio_files = find_audio_files()
    
    if not audio_files:
        print("❌ 未找到音频文件")
        print("请将音频文件（MP3、WAV、M4A）放在当前目录或rtvs-python目录中")
        return
    
    print("📁 找到以下音频文件:")
    for i, file in enumerate(audio_files, 1):
        file_size = file.stat().st_size / 1024 / 1024  # MB
        print(f"  {i}. {file.name} ({file_size:.1f} MB)")
    
    # 选择文件
    while True:
        try:
            choice = input(f"\n请选择要推流的文件 (1-{len(audio_files)}) 或输入 'q' 退出: ").strip()
            if choice.lower() == 'q':
                return
            
            file_index = int(choice) - 1
            if 0 <= file_index < len(audio_files):
                selected_file = audio_files[file_index]
                break
            else:
                print("❌ 无效选择，请重新输入")
        except ValueError:
            print("❌ 请输入有效数字")
    
    # 输入设备信息
    print(f"\n📋 设备配置 (直接回车使用默认值):")
    sim = input(f"SIM卡号 (默认: {DEFAULT_SIM}): ").strip() or DEFAULT_SIM
    
    try:
        channel = int(input(f"通道号 (默认: {DEFAULT_CHANNEL}): ").strip() or DEFAULT_CHANNEL)
    except ValueError:
        channel = DEFAULT_CHANNEL
    
    device_id = input(f"设备ID (默认: {DEFAULT_DEVICE_ID}): ").strip() or DEFAULT_DEVICE_ID
    
    # 确认信息
    print(f"\n📋 推流信息确认:")
    print(f"   文件: {selected_file.name}")
    print(f"   SIM: {sim}")
    print(f"   通道: {channel}")
    print(f"   设备ID: {device_id}")
    
    confirm = input("\n确认开始推流? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 推流已取消")
        return
    
    # 开始推流
    success = await push_audio_to_device(str(selected_file), sim, channel, device_id)
    
    if success:
        print("\n🎉 语音对讲完成!")
    else:
        print("\n💥 语音对讲失败，请检查:")
        print("   1. API服务是否正在运行 (python audio_talk_api.py)")
        print("   2. 设备是否在线")
        print("   3. 网络连接是否正常")
        print("   4. 配置信息是否正确")

async def batch_push(directory_path: str, sim: str = None, channel: int = None, device_id: str = None):
    """批量推流目录中的音频文件"""
    directory = Path(directory_path)
    if not directory.exists():
        print(f"❌ 目录不存在: {directory_path}")
        return
    
    # 查找音频文件
    audio_files = []
    for pattern in ["*.mp3", "*.wav", "*.m4a"]:
        audio_files.extend(list(directory.glob(pattern)))
    
    if not audio_files:
        print(f"❌ 在目录 {directory_path} 中未找到音频文件")
        return
    
    print(f"📁 找到 {len(audio_files)} 个音频文件，开始批量推流...")
    
    success_count = 0
    for i, audio_file in enumerate(audio_files, 1):
        print(f"\n--- 处理文件 {i}/{len(audio_files)} ---")
        success = await push_audio_to_device(str(audio_file), sim, channel, device_id)
        if success:
            success_count += 1
        
        # 文件间间隔
        if i < len(audio_files):
            print("⏳ 等待3秒后处理下一个文件...")
            await asyncio.sleep(3)
    
    print(f"\n📊 批量推流完成: {success_count}/{len(audio_files)} 个文件成功")

async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        # 命令行模式
        audio_file = sys.argv[1]
        sim = sys.argv[2] if len(sys.argv) > 2 else None
        channel = int(sys.argv[3]) if len(sys.argv) > 3 else None
        device_id = sys.argv[4] if len(sys.argv) > 4 else None
        
        print(f"🚀 命令行模式推流")
        await push_audio_to_device(audio_file, sim, channel, device_id)
    else:
        # 交互模式
        await interactive_push()

if __name__ == "__main__":
    print("请确保语音对讲API服务正在运行:")
    print("python audio_talk_api.py")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 推流已被用户中断")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
