#!/usr/bin/env python3
"""
RTVS 语音对讲 API
专注于语音对讲功能的简化版本
"""

import asyncio
import websockets
import httpx
import logging
import json
import time
from datetime import datetime
from typing import Dict, Optional
from dataclasses import dataclass
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pydub import AudioSegment
from contextlib import asynccontextmanager
import io

# =============================================================================
# 1. 配置和数据模型
# =============================================================================

@dataclass
class AudioTalkConfig:
    """语音对讲配置"""
    app_code: str = "SV_CWTPRO"
    config_url: str = "http://************/jt-video/video/websocket/address"
    timeout: int = 10
    
    # 音频配置
    sample_rate: int = 8000
    channels: int = 1
    sample_width: int = 2
    chunk_size: int = 320
    interval: float = 0.02

# =============================================================================
# 2. 语音对讲客户端
# =============================================================================

class AudioTalkClient:
    """语音对讲客户端"""
    
    def __init__(self, config: AudioTalkConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, _, __, ___):
        await self.close()
        
    async def close(self):
        """关闭客户端连接"""
        await self.http_client.aclose()
    
    async def get_server_config(self, device_id: str) -> dict:
        """
        获取服务器配置（包含token）
        
        Args:
            device_id: 设备ID
            
        Returns:
            dict: 包含address, port, token等信息的配置字典
        """
        params = {
            "appCode": self.config.app_code,
            "deviceId": device_id
        }
        
        try:
            self.logger.info(f"正在从 {self.config.config_url} 获取设备 {device_id} 的配置...")
            response = await self.http_client.get(self.config.config_url, params=params, timeout=self.config.timeout)
            response.raise_for_status()
            data = response.json()
            
            if data.get("result") == 0:
                config = data.get("detail", {})
                self.logger.info(f"获取配置成功: {config}")
                
                # 验证必要的配置项
                required_keys = ["address", "port", "token"]
                if not all(k in config for k in required_keys):
                    missing_keys = [k for k in required_keys if k not in config]
                    raise Exception(f"返回的配置中缺少必要字段: {missing_keys}")
                
                return config
            else:
                error_msg = data.get('resultNote', '未知错误')
                raise Exception(f"服务器返回错误: {error_msg}")
                
        except httpx.RequestError as e:
            self.logger.error(f"请求配置接口失败: {e}")
            raise Exception(f"请求配置接口失败: {e}")
        except Exception as e:
            self.logger.error(f"解析配置数据失败: {e}")
            raise Exception(f"解析配置数据失败: {e}")
    
    async def start_audio_talk(self, sim: str, channel: int, audio_file: UploadFile = None, device_id: str = None) -> Dict:
        """开始语音对讲"""
        try:
            # 使用sim作为默认device_id
            if device_id is None:
                device_id = sim
            
            # 获取服务器配置（包含token）
            server_config = await self.get_server_config(device_id)
            host = server_config["address"]
            port = int(server_config["port"])
            token = server_config["token"]
            
            ws_url = f"ws://{host}:{port}"
            self.logger.info(f"准备连接到: {ws_url}")
            
            # 构建对讲请求包
            packet = self._build_talk_packet(sim, channel, token, host, port)
            self.logger.info(f"发送对讲请求包 (hex): {packet.hex(' ')}")
            
            # 建立WebSocket连接
            websocket = await websockets.connect(ws_url, open_timeout=10, close_timeout=10)
            await websocket.send(packet)
            
            # 等待服务器确认
            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            self.logger.info(f"服务器确认: {response.hex(' ')}")
            
            if audio_file:
                # 处理音频文件并推流
                await self._stream_audio_file(websocket, sim, channel, audio_file)
                self.logger.info("音频推流完成")
            
            await websocket.close()
            
            return {
                "success": True,
                "message": "语音对讲完成",
                "server_info": {
                    "host": host,
                    "port": port,
                    "ws_url": ws_url
                }
            }
            
        except asyncio.TimeoutError:
            self.logger.error("语音对讲超时")
            raise HTTPException(status_code=408, detail="服务器响应超时，设备可能不在线")
        except Exception as e:
            self.logger.error(f"语音对讲失败: {e}")
            raise HTTPException(status_code=500, detail=f"语音对讲失败: {str(e)}")
    
    async def _stream_audio_file(self, websocket, sim: str, channel: int, audio_file: UploadFile):
        """推流音频文件"""
        try:
            # 读取并转换音频文件
            file_content = await audio_file.read()
            audio = AudioSegment.from_file(io.BytesIO(file_content))
            audio = audio.set_frame_rate(self.config.sample_rate).set_channels(self.config.channels).set_sample_width(self.config.sample_width)
            pcm_data = audio.raw_data
            
            self.logger.info(f"音频文件信息: 时长={len(audio)/1000:.1f}秒, 大小={len(pcm_data)}字节")
            
            # 分包发送音频数据
            sequence = 1
            timestamp = 0
            
            for i in range(0, len(pcm_data), self.config.chunk_size):
                sequence += 1
                chunk = pcm_data[i:i+self.config.chunk_size]
                audio_packet = self._build_audio_packet(sim, channel, sequence, timestamp, chunk)
                await websocket.send(audio_packet)
                await asyncio.sleep(self.config.interval)
                timestamp += int(self.config.interval * 1000)  # 转换为毫秒
                
            self.logger.info("音频数据推流完成")
            
        except Exception as e:
            self.logger.error(f"音频推流失败: {e}")
            raise
    
    def _build_talk_packet(self, sim: str, channel: int, token: str, server_ip: str, server_tcp_port: int) -> bytes:
        """构建对讲请求包"""
        # 1. 构建JT/T 808 (0x9101) 消息体
        msg_id_808 = 0x9101
        ip_bytes = server_ip.encode('ascii')
        body_808 = bytearray()
        body_808.append(len(ip_bytes))  # 服务器地址长度
        body_808.extend(ip_bytes)       # 服务器地址
        body_808.extend(server_tcp_port.to_bytes(2, 'big'))  # TCP端口
        body_808.extend((0).to_bytes(2, 'big'))  # UDP端口
        body_808.append(channel)        # 逻辑通道号
        body_808.append(2)              # 数据类型: 2=双向对讲
        body_808.append(1)              # 码流类型: 1=子码流
        
        # 2. 构建808消息头
        sequence = 1
        header_808 = self._build_jt808_header(sim, msg_id_808, len(body_808), sequence)
        packet_808 = header_808 + body_808
        
        # 3. 计算校验码并转义
        checksum_808 = 0
        for byte in packet_808:
            checksum_808 ^= byte
        
        escaped_808 = self._escape_data(packet_808)
        escaped_808.extend(self._escape_data(checksum_808.to_bytes(1, 'big')))
        
        # 4. 构建认证尾部
        auth_tail = bytearray()
        token_bytes = token.encode('utf-8')
        auth_tail.extend((0).to_bytes(2, 'big'))
        auth_tail.extend((0).to_bytes(2, 'big'))
        auth_tail.append(1)
        auth_tail.extend(len(token_bytes).to_bytes(2, 'big'))
        auth_tail.extend(token_bytes)
        
        # 5. 构建最外层包头
        body_1078_len = 1 + len(escaped_808) + 1 + len(auth_tail)
        header_1078 = self._build_1078_header(sim, channel, body_1078_len, sequence)
        
        # 6. 组装最终数据包
        final_packet = bytearray()
        final_packet.extend(header_1078)
        final_packet.extend(b'\x7e')
        final_packet.extend(escaped_808)
        final_packet.extend(b'\x7e')
        final_packet.extend(auth_tail)
        
        return bytes(final_packet)
    
    def _build_audio_packet(self, sim: str, channel: int, sequence: int, timestamp: int, audio_data: bytes) -> bytes:
        """构建音频数据包"""
        codec_type_g711a = 6
        header = self._build_1078_header(sim, channel, len(audio_data), sequence, has_timestamp=True, timestamp=timestamp, payload_type=codec_type_g711a)
        return header + audio_data
    
    def _build_1078_header(self, sim: str, channel: int, data_len: int, sequence: int, has_timestamp: bool = False, timestamp: int = 0, payload_type: int = 0) -> bytearray:
        """构建1078协议头"""
        header = bytearray(b'\x30\x31\x63\x64')
        header.append(0x81)
        header.append(payload_type)
        header.extend(sequence.to_bytes(2, 'big'))
        header.extend(bytes.fromhex(sim.zfill(12)))
        header.append(channel)
        
        if has_timestamp:
            header.append(0b00100000)
            header.extend(timestamp.to_bytes(8, 'big'))
            header.extend((0).to_bytes(2, 'big'))
            header.extend((0).to_bytes(2, 'big'))
            header.extend(data_len.to_bytes(2, 'big'))
        else:
            header.append(0x80)
            header.extend(data_len.to_bytes(2, 'big'))
        
        return header
    
    def _build_jt808_header(self, sim: str, msg_id: int, body_length: int, sequence: int) -> bytearray:
        """构建JT808消息头"""
        header = bytearray()
        header.extend(msg_id.to_bytes(2, 'big'))
        body_props = body_length & 0x3FF
        header.extend(body_props.to_bytes(2, 'big'))
        header.extend(bytes.fromhex(sim.zfill(12)))
        header.extend(sequence.to_bytes(2, 'big'))
        return header
    
    def _escape_data(self, data: bytearray) -> bytearray:
        """数据转义"""
        escaped_data = bytearray()
        for byte in data:
            if byte == 0x7e:
                escaped_data.extend([0x7d, 0x02])
            elif byte == 0x7d:
                escaped_data.extend([0x7d, 0x01])
            else:
                escaped_data.append(byte)
        return escaped_data

# =============================================================================
# 3. FastAPI应用
# =============================================================================

# 全局配置和客户端
config = AudioTalkConfig()
audio_client = None

@asynccontextmanager
async def lifespan(_):
    """应用生命周期管理"""
    global audio_client
    # 启动
    audio_client = AudioTalkClient(config)
    logging.basicConfig(level=logging.INFO)
    yield
    # 关闭
    if audio_client:
        await audio_client.close()

app = FastAPI(
    title="RTVS 语音对讲 API",
    description="RTVS语音对讲专用API接口",
    version="1.0.0",
    lifespan=lifespan
)

# =============================================================================
# 4. API路由
# =============================================================================

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "audio_talk", "timestamp": datetime.now().isoformat()}

@app.post("/api/audio/talk")
async def start_audio_talk(
    sim: str = Form(...),
    channel: int = Form(...),
    device_id: str = Form(None),
    audio_file: UploadFile = File(...)
):
    """开始语音对讲"""
    try:
        result = await audio_client.start_audio_talk(sim, channel, audio_file, device_id)
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/config")
async def get_config():
    """获取当前配置"""
    return {
        "app_code": config.app_code,
        "config_url": config.config_url,
        "timeout": config.timeout,
        "audio": {
            "sample_rate": config.sample_rate,
            "channels": config.channels,
            "sample_width": config.sample_width,
            "chunk_size": config.chunk_size,
            "interval": config.interval
        }
    }

@app.post("/api/config")
async def update_config(new_config: dict):
    """更新配置"""
    global config, audio_client
    try:
        # 更新配置
        for key, value in new_config.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        # 重新创建客户端
        if audio_client:
            await audio_client.close()
        audio_client = AudioTalkClient(config)
        
        return {"success": True, "message": "配置已更新"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# =============================================================================
# 5. 主函数
# =============================================================================

if __name__ == "__main__":
    import uvicorn
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 启动服务
    uvicorn.run(
        "audio_talk_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
