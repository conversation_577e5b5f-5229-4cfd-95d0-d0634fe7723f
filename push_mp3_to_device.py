#!/usr/bin/env python3
"""
MP3文件推流到设备示例
使用RTVS Python API将本地MP3文件推流到指定设备
"""

import asyncio
import httpx
import os
from pathlib import Path

# 配置信息
API_BASE_URL = "http://127.0.0.1:8000"  # API服务地址
TARGET_SIM = "13800138000"               # 目标设备SIM卡号
TARGET_CHANNEL = 1                       # 目标通道号

async def push_mp3_to_device(mp3_file_path: str, sim: str, channel: int, device_id: str = None):
    """
    将MP3文件推流到指定设备

    Args:
        mp3_file_path: MP3文件路径
        sim: 设备SIM卡号
        channel: 通道号
        device_id: 设备ID（可选，默认使用sim）
    """
    
    # 检查文件是否存在
    if not os.path.exists(mp3_file_path):
        print(f"❌ 错误: 文件不存在 - {mp3_file_path}")
        return False
    
    print(f"🎵 准备推流MP3文件: {mp3_file_path}")
    print(f"📱 目标设备: SIM={sim}, 通道={channel}")
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            # 1. 首先检查API服务是否可用
            print("🔍 检查API服务状态...")
            health_response = await client.get(f"{API_BASE_URL}/health")
            if health_response.status_code != 200:
                print("❌ API服务不可用")
                return False
            print("✅ API服务正常")
            
            # 2. 获取服务器统计信息（可选，用于验证连接）
            print("📊 获取服务器信息...")
            try:
                stats_response = await client.get(f"{API_BASE_URL}/api/server/stats")
                if stats_response.status_code == 200:
                    stats = stats_response.json()
                    print(f"   在线设备: {stats.get('online_devices', 0)}")
                    print(f"   总连接数: {stats.get('total_connections', 0)}")
            except Exception as e:
                print(f"⚠️  获取服务器信息失败: {e}")
            
            # 3. 开始音频对讲推流
            print("🎤 开始音频对讲推流...")
            
            with open(mp3_file_path, "rb") as audio_file:
                files = {
                    "audio_file": (
                        os.path.basename(mp3_file_path),
                        audio_file,
                        "audio/mpeg"
                    )
                }
                data = {
                    "sim": sim,
                    "channel": channel,
                    "device_id": device_id or sim  # 如果没有指定device_id，使用sim
                }
                
                # 发送推流请求
                response = await client.post(
                    f"{API_BASE_URL}/api/audio/talk/start",
                    files=files,
                    data=data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 推流成功: {result.get('message', '成功')}")
                    print(f"🔗 连接ID: {result.get('connection_id', 'N/A')}")
                    return True
                else:
                    print(f"❌ 推流失败: HTTP {response.status_code}")
                    try:
                        error_detail = response.json()
                        print(f"   错误详情: {error_detail.get('detail', '未知错误')}")
                    except:
                        print(f"   响应内容: {response.text}")
                    return False
                    
    except httpx.TimeoutException:
        print("❌ 请求超时，可能是网络问题或设备离线")
        return False
    except httpx.ConnectError:
        print("❌ 连接失败，请确保API服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 推流过程中发生错误: {e}")
        return False

async def batch_push_mp3_files(directory_path: str, sim: str, channel: int):
    """
    批量推流目录中的所有MP3文件
    
    Args:
        directory_path: MP3文件目录路径
        sim: 设备SIM卡号
        channel: 通道号
    """
    
    directory = Path(directory_path)
    if not directory.exists() or not directory.is_dir():
        print(f"❌ 错误: 目录不存在 - {directory_path}")
        return
    
    # 查找所有MP3文件
    mp3_files = list(directory.glob("*.mp3")) + list(directory.glob("*.MP3"))
    
    if not mp3_files:
        print(f"❌ 在目录 {directory_path} 中未找到MP3文件")
        return
    
    print(f"📁 找到 {len(mp3_files)} 个MP3文件")
    
    success_count = 0
    for i, mp3_file in enumerate(mp3_files, 1):
        print(f"\n--- 处理文件 {i}/{len(mp3_files)} ---")
        success = await push_mp3_to_device(str(mp3_file), sim, channel)
        if success:
            success_count += 1
        
        # 文件间间隔，避免过于频繁的请求
        if i < len(mp3_files):
            print("⏳ 等待5秒后处理下一个文件...")
            await asyncio.sleep(5)
    
    print(f"\n📊 批量推流完成: {success_count}/{len(mp3_files)} 个文件成功")

def find_mp3_files_in_current_directory():
    """在当前目录查找MP3文件"""
    current_dir = Path(".")
    mp3_files = list(current_dir.glob("*.mp3")) + list(current_dir.glob("*.MP3"))
    
    # 也检查rtvs-python目录
    rtvs_python_dir = Path("rtvs-python")
    if rtvs_python_dir.exists():
        mp3_files.extend(list(rtvs_python_dir.glob("*.mp3")))
        mp3_files.extend(list(rtvs_python_dir.glob("*.MP3")))
    
    return mp3_files

async def interactive_push():
    """交互式推流"""
    print("🎵 RTVS MP3推流工具")
    print("=" * 50)
    
    # 查找可用的MP3文件
    mp3_files = find_mp3_files_in_current_directory()
    
    if not mp3_files:
        print("❌ 在当前目录和rtvs-python目录中未找到MP3文件")
        print("请将MP3文件放在当前目录或rtvs-python目录中")
        return
    
    print("📁 找到以下MP3文件:")
    for i, file in enumerate(mp3_files, 1):
        file_size = file.stat().st_size / 1024 / 1024  # MB
        print(f"  {i}. {file.name} ({file_size:.1f} MB)")
    
    # 选择文件
    while True:
        try:
            choice = input(f"\n请选择要推流的文件 (1-{len(mp3_files)}) 或输入 'q' 退出: ").strip()
            if choice.lower() == 'q':
                return
            
            file_index = int(choice) - 1
            if 0 <= file_index < len(mp3_files):
                selected_file = mp3_files[file_index]
                break
            else:
                print("❌ 无效选择，请重新输入")
        except ValueError:
            print("❌ 请输入有效数字")
    
    # 输入设备信息
    sim = input(f"请输入设备SIM卡号 (默认: {TARGET_SIM}): ").strip() or TARGET_SIM
    
    try:
        channel = int(input(f"请输入通道号 (默认: {TARGET_CHANNEL}): ").strip() or TARGET_CHANNEL)
    except ValueError:
        channel = TARGET_CHANNEL
        print(f"使用默认通道号: {channel}")
    
    # 确认推流
    print(f"\n📋 推流信息确认:")
    print(f"   文件: {selected_file.name}")
    print(f"   设备: {sim}")
    print(f"   通道: {channel}")
    
    confirm = input("\n确认开始推流? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 推流已取消")
        return
    
    # 开始推流
    success = await push_mp3_to_device(str(selected_file), sim, channel)
    
    if success:
        print("\n🎉 推流完成!")
    else:
        print("\n💥 推流失败，请检查:")
        print("   1. API服务是否正在运行 (python rtvs_python_api.py)")
        print("   2. 设备是否在线")
        print("   3. 网络连接是否正常")
        print("   4. SIM卡号和通道号是否正确")

async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        # 命令行模式
        mp3_file = sys.argv[1]
        sim = sys.argv[2] if len(sys.argv) > 2 else TARGET_SIM
        channel = int(sys.argv[3]) if len(sys.argv) > 3 else TARGET_CHANNEL
        
        print(f"🚀 命令行模式推流")
        await push_mp3_to_device(mp3_file, sim, channel)
    else:
        # 交互模式
        await interactive_push()

if __name__ == "__main__":
    print("请确保RTVS Python API服务正在运行:")
    print("python rtvs_python_api.py")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 推流已被用户中断")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
