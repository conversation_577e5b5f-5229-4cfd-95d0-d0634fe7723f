﻿@model IEnumerable<RecordLib.ClientState>
@{

}

<style>
th {
    width: 150px;
    text-align: left;
}
</style>

<table class="table">
    <thead>
        <tr>
            <th>
                SIM
            </th>
            <th>
                Channel
            </th>
            <th>
                Status
            </th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Sim)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Channel)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Status)
                </td>
            </tr>
        }
    </tbody>
</table>
