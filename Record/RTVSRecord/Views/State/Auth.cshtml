﻿@model RTVSRecord.Auth
@{
    var endStr = Model.m_vertify_utc > 0x0FFFFFFFFFFFFFF ? "无限制" : SQ.Base.DateTimeHelper.UNIXtoDateTime(Model.m_vertify_utc).ToString("yyyy-MM-dd HH:mm:ss");

}

@if (!Model.m_is_authed)
{
    <span>机器码</span>
    <br />
    <textarea rows="5" cols="100" readonly="readonly">@Model.m_auth_text</textarea>
    <br />
    <span>授权码(请联系厂家获取)</span>
    <br />
    <textarea rows="5" cols="100" id="txtSign"></textarea>
    <br />
    <input type="button" value="验证" onclick="Sign()" />

    <br />
    <br />
    <span>当前：限定2路录像</span>
}
@* else if (tsk.m_status_server_authed == 0)
{
    <span>本地已验证，服务器验证中..</span>
} *@
else if (Model.m_status_server_authed < 0)
{
    <span>非法授权，请联系厂家</span>
    <br />
    <span>限定2路录像</span>
}
else
{
    <span>到期时间：@endStr</span>
    <br />
    <span>无限制</span>
}
<script src="/lib/jquery/dist/jquery.js"></script>
<script type="text/javascript">
    function Sign() {
        let sign_text = $("#txtSign").val();
        $.ajax({
            url: "/State/Sign?text=" + sign_text,
            success: function (data) {
                if (data) {
                    alert("成功");
                } else {
                    alert("失败");
                }
                window.location.reload();
            }
        });
    }
</script>