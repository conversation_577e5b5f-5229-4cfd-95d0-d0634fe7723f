﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RTVSRecord.Controllers
{
    public class StateController : BaseController
    {
        public IActionResult Index()
        {
            return View(Program.Manager.GetState());
        }
        public IActionResult Auth()
        {
            return View(RTVSRecord.Auth.GetInstance());
        }

        public bool Sign(string text)
        {
            if (text == null)
                return false;
            string sign = text.Replace(" ", "+");
            return RTVSRecord.Auth.GetInstance().AuthVerifySign(sign);
        }

        class StateSim
        {
            string sim;
            int channel;
            int status;
        }

        //public List<StateSim> StateSim()
        //{
        //    List<StateSim> list_ss = new List<StateSim>();

        //    RTVSRecord.Auth.GetInstance().RecordLib.ClientState

        //    return null;
        //}

    }
}
