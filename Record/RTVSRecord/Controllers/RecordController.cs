﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RecordLib;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace RTVSRecord.Controllers
{


    [ApiController]
    [Route("[controller]")]
    public class RecordController : ControllerBase
    {
        private readonly ILogger<RecordController> _logger;

        public RecordController(ILogger<RecordController> logger)
        {
            _logger = logger;
        }

        [HttpGet("[action]")]
        public RecordRet Start(string Sim, string Channel, int Protocol, string CTags)
        {
            RecordRet rr = new RecordRet();
            if (!Auth.GetInstance().AuthCheck() && Program.Manager.GetChannelCount() >= 2)
            {
                rr.Result = -1;
                rr.ResultNote = "未授权,只支持两路";
                return rr;
            }

            if (!Program.Manager.StartNew(Sim, Channel, Protocol, CTags))
            {
                rr.Result = -1;
            }

            return rr;
        }
        [HttpGet("[action]")]
        public RecordRet Stop(string Sim, string Channel, int Protocol)
        {
            RecordRet rr = new RecordRet();
            if (Program.Manager.Remove(Sim, Channel, Protocol))
            {

            }
            else
            {
                rr.Result = -1;
            }

            return rr;
        }

        [DataContract]
        public class DeviceInfo
        {
            [DataMember]
            public string Sim { get; set; }
            [DataMember]
            public List<string> Channel { get; set; }
            [DataMember]
            public int Protocol { get; set; }
            [DataMember]
            public string CTags { get; set; }
        }

        [DataContract]
        public class QueryDevicesVideoFiles
        {
            [DataMember]
            public String TimeStart { get; set; }
            [DataMember]
            public String TimeEnd { get; set; }
            [DataMember]
            public List<DeviceInfo> Devices { get; set; }
        }

        [DataContract]
        public class RecordRet
        {
            [DataMember]
            public int Result { get; set; }
            [DataMember]
            public string ResultNote { get; set; }
            [DataMember]
            public string detail { get; set; }
        }

        [DataContract]
        public class DeleteByTimeAndDevices
        {
            [DataMember]
            public string time { get; set; }
            [DataMember]
            public List<DeviceSimChannel> devices { get; set; }
        }


        /// <summary>
        /// 启动多个
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        [HttpPost("[action]")]
        public RecordRet Open(List<DeviceInfo> info)
        {
            return BatchStart(info);
        }

        /// <summary>
        /// 启动多个
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        [HttpPost("[action]")]
        public RecordRet BatchStart(List<DeviceInfo> info)
        {
            bool ret;
            RecordRet rr = new RecordRet();
            if (!Auth.GetInstance().AuthCheck())
            {
                int count = 0;
                foreach (var item in info)
                {
                    foreach (var Channel in item.Channel)
                    {
                        count++;
                    }
                }
                if (Program.Manager.GetChannelCount() + count > 2)
                {
                    rr.Result = -1;
                    rr.ResultNote = "未授权,只支持两路";
                    return rr;
                }
            }


            foreach (var item in info)
            {
                foreach (var Channel in item.Channel)
                {
                    ret = Program.Manager.StartNew(item.Sim, Channel, item.Protocol, item.CTags);
                }
            }

            return rr;
        }

        /// <summary>
        /// 关闭多个
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        [HttpPost("[action]")]
        public RecordRet BatchStop(List<DeviceInfo> info)
        {
            RecordRet rr = new RecordRet();

            foreach (var item in info)
            {
                foreach (var Channel in item.Channel)
                {
                    Program.Manager.Remove(item.Sim, Channel, item.Protocol);
                }
            }

            return rr;
        }

        /// <summary>
        /// 关闭多个
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        [HttpPost("[action]")]
        public RecordRet Close(List<DeviceInfo> info)
        {
            return BatchStop(info);
        }

        [HttpPost("[action]")]
        public string QueryVideoFiles(QueryDevicesVideoFiles info)
        {
            Dictionary<string, List<string>> devices = new Dictionary<string, List<string>>();

            DateTime starttime;
            DateTime endtime;
            DateTime.TryParse(info.TimeStart, out starttime);
            DateTime.TryParse(info.TimeEnd, out endtime);

            if (info.Devices != null)
            {
                foreach (var item in info.Devices)
                {
                    var sim = item.Sim;
                    List<string> channels = new List<string>();
                    if (item.Channel != null)
                    {
                        foreach (var Channel in item.Channel)
                        {
                            channels.Add(Channel);
                        }
                    }
                    devices.Add(sim, channels);
                }
            }

            return Program.Manager.QueryVideoFiles(devices, starttime, endtime);
        }

        [HttpPost("[action]")]
        public bool DeleteVideoAll()
        {
            return Program.Manager.DeleteVideoAll();
        }

        [HttpPost("[action]")]
        public bool DeleteVideoByTime(DateTime time)    
        {
            return Program.Manager.DeleteVideoBeforeTime(time);
        }

        [HttpPost("[action]")]
        public bool DeleteVideoByTimeAndDevices(DeleteByTimeAndDevices info)
        {
            DateTime time = DateTime.Parse(info.time);

            return Program.Manager.DeleteVideoBeforeTimeByDevice(time, info.devices);
        }

        //Record/Start?Sim=111111111112&Channel=1&Protocol=0
        //Record/Stop?Sim=111111111112&Channel=1&Protocol=0
        //Record/QueryVideoFiles?StartTime=2023-11-07 00:00:00&EndTime=2023-11-09 23:59:59&Sims=1111,222,333

        /* 测试
        DeviceInfo t1 = new DeviceInfo();
        DeviceInfo t2 = new DeviceInfo();
        t1.Sim = "111111111112";
        t1.Channel.Add("1");
        t1.Channel.Add("2");
        t1.Protocol = 0;
        t2.Sim = "111111111113";
        t2.Channel.Add("1");
        t2.Protocol = 0;

        List<DeviceInfo> list_di = new List<DeviceInfo>();
        list_di.Add(t1);
        list_di.Add(t2);
        var tt = JsonConvert.SerializeObject(list_di);
        */
    }
}
