﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

// For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace RTVSRecord.Controllers
{
    public class BaseController : Controller
    {
        /// <summary>
        /// 请求过滤处理
        /// </summary>
        /// <param name="filterContext"></param>
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            if (!CheckLogin(filterContext.HttpContext))
            {
                filterContext.Result = new RedirectResult("/Login/SignIn");
                return;
            }
            base.OnActionExecuting(filterContext);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public static bool CheckLogin(HttpContext context)
        {
            if (context.Session != null && context.Session.TryGetValue("Login", out var bytes))
            {
                return true;
            }
            if (context.Request.Cookies.TryGetValue("Authentication", out var cookie))
            {

                if (cookie == SQ.Base.EnDecrypt.EnDecrypt.MD5(Program.Manager.Setting.WebUsrName + "_" + Program.Manager.Setting.WebUsrPwd))
                {
                    context.Session.SetInt32("Login", 1);
                    return true;
                }

            }
            return false;
        }
    }
}
