﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Cryptography;
using System.Text;
using System;
using System.Linq;
using RTVSRecord.Models;

namespace RTVSRecord.Controllers
{
    public class LoginController : Controller
    {
        // GET: /<controller>/
        public IActionResult SignIn(UserModel userModel)
        {
            if (ModelState.IsValid)
            {
                MD5 md5Hash = MD5.Create();
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(Program.Manager.Setting.WebUsrPwd));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < data.Length; i++)
                {
                    builder.Append(data[i].ToString("x2")); // 将每个字节转换为十六进制并添加到结果中
                }
                var webPwd = builder.ToString();

                //Program.task.Config.WebUsrPwd 加密md5
                //MD5CryptoServiceProvider md5Hasher = new MD5CryptoServiceProvider();

                //检查用户信息
                if (userModel.Username == Program.Manager.Setting.WebUsrName && userModel.Password == webPwd)
                {
                    userModel.Password = Program.Manager.Setting.WebUsrPwd;
                    //记录Session
                    HttpContext.Session.SetInt32("Login", 1);
                    if (userModel.RememberMe)
                    {
                        HttpContext.Response.Cookies.Append("Authentication"
                            , SQ.Base.EnDecrypt.EnDecrypt.MD5(userModel.Username + "_" + userModel.Password)
                            , new CookieOptions { Expires = DateTime.Now.AddDays(10) });
                    }
                    //跳转到系统首页
                    return RedirectToAction("Index", "State");
                }
                ViewBag.ErrorInfo = "用户名或密码错误";
                return View(userModel);
            }
            ViewBag.ErrorInfo = ModelState.Values.First().Errors[0].ErrorMessage;
            return View(userModel);
        }

        public IActionResult SignOut()
        {
            //清除Session
            HttpContext.Session.Clear();
            HttpContext.Response.Cookies.Delete("Authentication");
            //跳转到系统登录界面
            return RedirectToAction("SignIn", "Login");
        }

    }
}
