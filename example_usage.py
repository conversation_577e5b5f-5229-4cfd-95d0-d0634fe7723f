#!/usr/bin/env python3
"""
RTVS Python API 使用示例
演示如何使用Python API与RTVS系统交互
"""

import asyncio
import httpx
import json
from pathlib import Path

# API服务器地址
API_BASE_URL = "http://127.0.0.1:8000"

async def main():
    """主函数 - 演示各种API调用"""
    
    async with httpx.AsyncClient() as client:
        print("=== RTVS Python API 使用示例 ===\n")
        
        # 1. 健康检查
        print("1. 健康检查")
        try:
            response = await client.get(f"{API_BASE_URL}/health")
            print(f"   状态: {response.json()}")
        except Exception as e:
            print(f"   错误: {e}")
        print()
        
        # 2. 获取服务器统计
        print("2. 获取服务器统计")
        try:
            response = await client.get(f"{API_BASE_URL}/api/server/stats")
            stats = response.json()
            print(f"   在线设备: {stats.get('online_devices', 0)}")
            print(f"   总连接数: {stats.get('total_connections', 0)}")
            print(f"   CPU使用率: {stats.get('cpu_usage', 0)}%")
            print(f"   内存使用率: {stats.get('memory_usage', 0)}%")
        except Exception as e:
            print(f"   错误: {e}")
        print()
        
        # 3. 获取访问令牌
        print("3. 获取访问令牌")
        try:
            response = await client.get(f"{API_BASE_URL}/api/token")
            token_info = response.json()
            print(f"   令牌: {token_info.get('token', 'N/A')[:20]}...")
        except Exception as e:
            print(f"   错误: {e}")
        print()
        
        # 4. 开始实时视频播放
        print("4. 开始实时视频播放")
        try:
            video_request = {
                "sim": "13800138000",
                "channel": 1,
                "stream_type": 0,
                "play_type": "realtime"
            }
            response = await client.post(
                f"{API_BASE_URL}/api/video/play/start",
                json=video_request
            )
            result = response.json()
            print(f"   结果: {result.get('message', 'N/A')}")
            connection_id = result.get('connection_id')
            
            # 等待一段时间后停止播放
            if connection_id:
                await asyncio.sleep(2)
                print("   停止视频播放...")
                stop_response = await client.post(
                    f"{API_BASE_URL}/api/video/play/stop/{connection_id}"
                )
                print(f"   停止结果: {stop_response.json().get('message', 'N/A')}")
                
        except Exception as e:
            print(f"   错误: {e}")
        print()
        
        # 5. 查询录像文件
        print("5. 查询录像文件")
        try:
            sim = "13800138000"
            channel = 1
            start_time = "2024-12-01 00:00:00"
            end_time = "2024-12-01 23:59:59"
            
            response = await client.get(
                f"{API_BASE_URL}/api/video/files/{sim}/{channel}",
                params={
                    "start_time": start_time,
                    "end_time": end_time
                }
            )
            files = response.json()
            print(f"   找到 {len(files.get('files', []))} 个录像文件")
            for file_info in files.get('files', [])[:3]:  # 只显示前3个
                print(f"     - {file_info.get('filename', 'N/A')}")
                
        except Exception as e:
            print(f"   错误: {e}")
        print()
        
        # 6. 设备控制
        print("6. 设备控制")
        try:
            control_request = {
                "sim": "13800138000",
                "channel": 1,
                "command": 0,  # 关闭音视频传输
                "param": {"type": "stop"}
            }
            response = await client.post(
                f"{API_BASE_URL}/api/device/control",
                json=control_request
            )
            result = response.json()
            print(f"   控制结果: {result.get('message', 'N/A')}")
        except Exception as e:
            print(f"   错误: {e}")
        print()
        
        # 7. 音频对讲 (如果有音频文件)
        print("7. 音频对讲")
        audio_file_path = Path("rtvs-python/111.mp3")
        if audio_file_path.exists():
            try:
                with open(audio_file_path, "rb") as audio_file:
                    files = {"audio_file": ("test.mp3", audio_file, "audio/mpeg")}
                    data = {"sim": "13800138000", "channel": 1}
                    
                    response = await client.post(
                        f"{API_BASE_URL}/api/audio/talk/start",
                        files=files,
                        data=data
                    )
                    result = response.json()
                    print(f"   对讲结果: {result.get('message', 'N/A')}")
            except Exception as e:
                print(f"   错误: {e}")
        else:
            print("   跳过 - 未找到音频文件")
        print()
        
        print("=== 示例完成 ===")

def demo_tree_grid_functionality():
    """演示树形表格功能的Python实现"""
    print("\n=== 树形表格功能演示 ===")
    
    # 模拟树形数据结构
    tree_data = [
        {
            "guid_key": "device_001",
            "name": "设备001",
            "status": "在线",
            "ip": "*************",
            "children": [
                {"guid_key": "ch_001_1", "name": "通道1", "status": "正常", "stream": "主码流"},
                {"guid_key": "ch_001_2", "name": "通道2", "status": "正常", "stream": "子码流"},
                {"guid_key": "ch_001_3", "name": "通道3", "status": "离线", "stream": "无"},
            ]
        },
        {
            "guid_key": "device_002",
            "name": "设备002",
            "status": "离线",
            "ip": "*************",
            "children": [
                {"guid_key": "ch_002_1", "name": "通道1", "status": "离线", "stream": "无"},
                {"guid_key": "ch_002_2", "name": "通道2", "status": "离线", "stream": "无"},
            ]
        }
    ]
    
    def print_tree_node(node, level=0):
        """递归打印树形节点"""
        indent = "  " * level
        icon = "📁" if node.get("children") else "📄"
        status_icon = "🟢" if node.get("status") == "在线" or node.get("status") == "正常" else "🔴"
        
        print(f"{indent}{icon} {status_icon} {node.get('name', 'N/A')} - {node.get('status', 'N/A')}")
        
        # 打印子节点
        for child in node.get("children", []):
            print_tree_node(child, level + 1)
    
    print("设备树形结构:")
    for device in tree_data:
        print_tree_node(device)
    
    print("\n可用操作:")
    print("- 展开/折叠节点")
    print("- 选择设备或通道")
    print("- 开始/停止视频播放")
    print("- 查看设备状态")
    print("- 音频对讲")

if __name__ == "__main__":
    print("请确保RTVS Python API服务正在运行 (python rtvs_python_api.py)")
    print("然后运行此示例脚本\n")
    
    # 运行API示例
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n示例被用户中断")
    except Exception as e:
        print(f"\n示例运行出错: {e}")
    
    # 演示树形表格功能
    demo_tree_grid_functionality()
